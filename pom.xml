<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-cloud-shuidi-parent</artifactId>
        <groupId>com.shuidihuzhu.infra</groupId>
        <version>3.1.49</version>
    </parent>

    <groupId>com.shuidihuzhu.cf</groupId>
    <artifactId>cf-clewtrack-api</artifactId>
    <packaging>jar</packaging>
    <version>3.1.141-SNAPSHOT</version>


    <properties>
        <account-grpc-client.version>2.2.127</account-grpc-client.version>
        <weixin-java-mp.version>1.3.68</weixin-java-mp.version>
        <baseservice-client.version>2.1.57</baseservice-client.version>
        <frame-client.version>3.0.0</frame-client.version>
        <org-json.version>********</org-json.version>
        <cf-client.version>1.2.454</cf-client.version>
        <cf-api-client.version>3.6.549</cf-api-client.version>
        <wx-grpc-client-version>1.0.100</wx-grpc-client-version>
        <eb.grafana.starter.version>2.0.22</eb.grafana.starter.version>
        <schedule.mq.client.version>2.1.7</schedule.mq.client.version>
        <elastic-job.version>1.0.17</elastic-job.version>
        <apollo-client.version>1.1.0</apollo-client.version>
        <jsonassert-version>1.5.0</jsonassert-version>
        <es.version>7.10.1</es.version>
        <shuidi-es-client.version>3.1.6</shuidi-es-client.version>
        <boot-image.tag>2</boot-image.tag>
        <java.version>11</java.version>
        <msg-rpc-client.version>2.1.75</msg-rpc-client.version>
        <servicelog-meta-cf.version>1.0.67</servicelog-meta-cf.version>
        <dataservice-client.version>2.1.86</dataservice-client.version>
        <cf-finance-client.version>3.0.53</cf-finance-client.version>
        <cos_api.version>5.6.28</cos_api.version>
        <cos-sts-java.version>3.0.5</cos-sts-java.version>
        <shuidi-cipher.version>3.1.24</shuidi-cipher.version>
        <kratos-client.version>3.0.1</kratos-client.version>
	    <cf-finance-feign-client.version>1.0.45</cf-finance-feign-client.version>
        <cg-casesrc-client.version>1.0.5</cg-casesrc-client.version>
        <cf-risk-rpc-client.version>1.0.127</cf-risk-rpc-client.version>
        <cf-ugc-client.version>9.0.75</cf-ugc-client.version>
        <ai-push-client.version>0.0.20</ai-push-client.version>
        <msg-server-client.version>0.0.13</msg-server-client.version>
        <cf-admin-api-pure-client.version>9.0.104</cf-admin-api-pure-client.version>
        <cf-client-base.version>9.0.81</cf-client-base.version>
        <cf-api-pure-client.version>1.0.6</cf-api-pure-client.version>
        <account-wecom-client.version>0.0.1</account-wecom-client.version>
        <cf-admin-model.version>3.5.395</cf-admin-model.version>
        <pf-tools.version>0.0.9</pf-tools.version>
        <shuidi-auth-saas-client.version>0.0.17</shuidi-auth-saas-client.version>
        <pr-common-client.version>0.0.11</pr-common-client.version>
        <pr-patient-client.version>0.0.83</pr-patient-client.version>
        <msg-send-common.verion>1.0.98</msg-send-common.verion>
        <org.mapstruct.version>1.5.2.Final</org.mapstruct.version>
        <org.projectlombok.version>1.18.24</org.projectlombok.version>
        <org.projectlombok.lombok-mapstruct-binding.version>0.2.0</org.projectlombok.lombok-mapstruct-binding.version>

        <cf-finance-common-model.version>1.1.70</cf-finance-common-model.version>
        <cf-material-client.version>9.0.107</cf-material-client.version>
        <cf-api.version>3.6.549</cf-api.version>
        <dmsp-client-version>0.0.31</dmsp-client-version>
        <cf-data-platform-client.version>9.0.90</cf-data-platform-client.version>
        <cf-enhancer-starter.version>1.0.94</cf-enhancer-starter.version>
        <charity-rpc-client.version>0.0.118</charity-rpc-client.version>
        <cf-tog-client.version>0.0.8</cf-tog-client.version>

    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.shuidihuzhu.account</groupId>
                <artifactId>account-custom-starter</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-api-pure-client</artifactId>
                <version>${cf-api-pure-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${org.projectlombok.lombok-mapstruct-binding.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.infra</groupId>
                <artifactId>cos-spring-boot-starter</artifactId>
                <version>0.0.7</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_pushgateway</artifactId>
                <version>0.9.0</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.shuidihuzhu.common</groupId>-->
<!--                <artifactId>web-core-v2</artifactId>-->
<!--                <version>2.0.23</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.shuidihuzhu.data</groupId>
                <artifactId>servicelog-meta-cf</artifactId>
                <version>${servicelog-meta-cf.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.shuidihuzhu.data</groupId>-->
<!--                <artifactId>servicelog-sdk-java</artifactId>-->
<!--                <version>${servicelog-sdk-java.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.shuidihuzhu.msg</groupId>
                <artifactId>msg-rpc-client</artifactId>
                <version>${msg-rpc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>${jsonassert-version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin-java-mp.version}</version>
            </dependency>


            <dependency>
                <groupId>com.shuidihuzhu.account</groupId>
                <artifactId>account-grpc-client</artifactId>
                <version>${account-grpc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.baseservice</groupId>
                <artifactId>baseservice-client</artifactId>
                <version>${baseservice-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.frame</groupId>
                <artifactId>frame-client</artifactId>
                <version>${frame-client.version}</version>
            </dependency>
            <!--先指定引用   后面删除-->

            <dependency>
                <groupId>org.unidal.framework</groupId>
                <artifactId>foundation-service</artifactId>
                <version>4.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${org-json.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-client</artifactId>
                <version>${cf-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-api-client</artifactId>
                <version>${cf-api-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>pf-common-v2</artifactId>
                        <groupId>com.shuidihuzhu.pf</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.wx</groupId>
                <artifactId>wx-grpc-client</artifactId>
                <version>${wx-grpc-client-version}</version>
            </dependency>


            <dependency>
                <groupId>com.shuidihuzhu.eb</groupId>
                <artifactId>schedule-mq-client</artifactId>
                <version>${schedule.mq.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.datawarehouse</groupId>
                <artifactId>shuidi-es-client</artifactId>
                <version>${shuidi-es-client.version}</version>
                <exclusions> <!-- 如果项目中没有引入es组件，则将 exclusions 部分去掉 ，防止与项目中已经使用的 es 模块版本冲突-->
                    <exclusion>
                        <artifactId>elasticsearch</artifactId>
                        <groupId>org.elasticsearch</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.dataservice</groupId>
                <artifactId>dataservice-client</artifactId>
                <version>${dataservice-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.msg</groupId>
                        <artifactId>msg-rpc-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-finance-client</artifactId>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.data</groupId>
                <artifactId>phone-parse-util</artifactId>
                <version>1.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos_api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.json</groupId>
                        <artifactId>json</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tencent.cloud</groupId>
                <artifactId>cos-sts-java</artifactId>
                <version>${cos-sts-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>shuidi-cipher</artifactId>
                <version>${shuidi-cipher.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.kratos</groupId>
                <artifactId>kratos-client</artifactId>
                <version>${kratos-client.version}</version>
            </dependency>
	        <dependency>
		        <groupId>com.shuidihuzhu.cf</groupId>
		        <artifactId>cf-finance-feign-client</artifactId>
		        <version>${cf-finance-feign-client.version}</version>
		        <exclusions>
			        <exclusion>
				        <artifactId>cf-model</artifactId>
				        <groupId>com.shuidihuzhu.cf</groupId>
			        </exclusion>
			        <exclusion>
				        <groupId>com.shuidihuzhu.cf</groupId>
				        <artifactId>cf-admin-model</artifactId>
			        </exclusion>
		        </exclusions>
	        </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.consult</groupId>
                <artifactId>cg-casesrc-client</artifactId>
                <version>${cg-casesrc-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf-risk</groupId>
                <artifactId>cf-risk-rpc-client</artifactId>
                <version>${cf-risk-rpc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-ugc-client</artifactId>
                <version>${cf-ugc-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.shuidihuzhu.common</groupId>
                        <artifactId>web-util</artifactId>
                    </exclusion>
                </exclusions>

            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.2.6</version>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.client</groupId>
                <artifactId>ai-push-client</artifactId>
                <version>${ai-push-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ti-net</groupId>
                <artifactId>clink-serversdk</artifactId>
                <version>2.0.30</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.account</groupId>
                <artifactId>account-wecom-client</artifactId>
                <version>${account-wecom-client.version}</version>
            </dependency>

            <!--标记30分钟&3小时处理流程接口依赖-->
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-material-client</artifactId>
                <version>${cf-material-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.op</groupId>
                <artifactId>shuidi-auth-saas-client</artifactId>
                <version>${shuidi-auth-saas-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.pr</groupId>
                <artifactId>pr-patient-info-client</artifactId>
                <version>${pr-patient-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.pr</groupId>
                <artifactId>pr-common-client</artifactId>
                <version>${pr-common-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-enhancer-starter</artifactId>
                <version>${cf-enhancer-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>web-model</artifactId>
                        <groupId>com.shuidihuzhu.common</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-common-dependencies</artifactId>
                <version>RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <artifactId>cf-finance-common-model</artifactId>
                <groupId>com.shuidihuzhu.cf</groupId>
                <version>${cf-finance-common-model.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.frame</groupId>
                <artifactId>charity-rpc-client</artifactId>
                <version>${charity-rpc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-tog-client</artifactId>
                <version>${cf-tog-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>outboundbot20191226</artifactId>
                <version>1.1.0</version>
            </dependency>
   </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-data-platform-client</artifactId>
            <version>${cf-data-platform-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-model</artifactId>
            <version>${cf-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>pay-rpc-client</artifactId>
                    <groupId>com.shuidihuzhu.pay</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-finance-common-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-material-client</artifactId>
            <version>${cf-material-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>verifycode-client</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-pure-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>msg-send-common</artifactId>
                    <groupId>com.shuidihuzhu.msg</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.infra</groupId>
            <artifactId>cos-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_pushgateway</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>

        <!-- 以下依赖如果自定义了版本，请删除自定义的版本-->
        <dependency>
            <groupId>com.shuidihuzhu.infra</groupId>
            <artifactId>mail-spring-boot-starter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.shuidihuzhu.common</groupId>-->
<!--            <artifactId>web-core-v2</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-meta-cf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tencent.cloud</groupId>
            <artifactId>cos-sts-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>phone-parse-util</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.shuidihuzhu.data</groupId>-->
<!--            <artifactId>servicelog-sdk-java</artifactId>-->
<!--            <version>3.1.41</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.skyscreamer</groupId>
            <artifactId>jsonassert</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>android-json</artifactId>
                    <groupId>com.vaadin.external.google</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jolokia</groupId>
            <artifactId>jolokia-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.frame</groupId>
            <artifactId>frame-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.unidal.framework</groupId>
            <artifactId>foundation-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.thetransactioncompany</groupId>
            <artifactId>cors-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-grpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>swagger-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.wx</groupId>
            <artifactId>wx-grpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.eb</groupId>
            <artifactId>schedule-mq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.datawarehouse</groupId>
            <artifactId>shuidi-es-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.dataservice</groupId>
            <artifactId>dataservice-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-client</artifactId>
            <version>${cf-finance-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-admin-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-finance-common-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>shuidi-cipher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.kratos</groupId>
            <artifactId>kratos-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

	    <dependency>
		    <groupId>com.shuidihuzhu.cf</groupId>
		    <artifactId>cf-finance-feign-client</artifactId>
	    </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.consult</groupId>
            <artifactId>cg-casesrc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf-risk</groupId>
            <artifactId>cf-risk-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-ugc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.client</groupId>
            <artifactId>ai-push-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.msg</groupId>
            <artifactId>msg-server-client</artifactId>
            <version>${msg-server-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>msg-send-common</artifactId>
                    <groupId>com.shuidihuzhu.msg</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
            <version>${cf-admin-api-pure-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client-base</artifactId>
            <version>${cf-client-base.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.shuidihuzhu.cf</groupId>-->
<!--            <artifactId>cf-common-dependencies</artifactId>-->
<!--            <version>RELEASE</version>-->
<!--            <type>pom</type>-->
<!--            <scope>import</scope>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.ti-net</groupId>
            <artifactId>clink-serversdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-wecom-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-model</artifactId>
            <version>${cf-admin-model.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>cf-material-client</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.pay</groupId>
                    <artifactId>pay-rpc-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pf</groupId>
            <artifactId>pf-tools</artifactId>
            <version>${pf-tools.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-custom-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pr</groupId>
            <artifactId>pr-patient-info-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>cf-model</artifactId>
                    <groupId>com.shuidihuzhu.cf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pr</groupId>
            <artifactId>pr-common-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-enhancer-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.patient</groupId>
            <artifactId>pt-track-client</artifactId>
            <version>0.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>


        <dependency>
            <groupId>com.shuidihuzhu.msg</groupId>
            <artifactId>msg-send-common</artifactId>
            <version>${msg-send-common.verion}</version>
        </dependency>


        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-cupid-client</artifactId>
            <version>0.0.10</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.3.3</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.msg</groupId>
            <artifactId>msg-rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.kratos</groupId>
            <artifactId>kratos-super-client</artifactId>
            <version>2.0.49</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.data-api</groupId>
            <artifactId>dmsp-client</artifactId>
            <version>${dmsp-client-version}</version>
        </dependency>

        <dependency>
            <artifactId>cf-finance-common-model</artifactId>
            <groupId>com.shuidihuzhu.cf</groupId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.frame</groupId>
            <artifactId>charity-rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>outboundbot20191226</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!-- 阿里云RocketMQ客户端 -->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <version>1.8.8.Final</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-tog-client</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <rules>
                                <valueRule implementation="com.shuidihuzhu.customrule.ValueRule" />
                            </rules>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0</version>
                    </dependency>
                    <dependency>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>custom-rule</artifactId>
                        <version>1.0.14</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1.1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${org.projectlombok.lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>



  <scm>
      <connection>scm:git:http://git.shuiditech.com/cf/cf-clewtrack-api</connection>
      <developerConnection>scm:git:**********************:cf/cf-clewtrack-api.git</developerConnection>
      <url>http://git.shuiditech.com/cf/cf-clewtrack-api</url>
      <tag>3.0.768</tag>
  </scm>
</project>
