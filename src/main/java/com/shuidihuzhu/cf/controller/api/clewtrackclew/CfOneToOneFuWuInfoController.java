package com.shuidihuzhu.cf.controller.api.clewtrackclew;

import brave.Tracing;
import com.alibaba.fastjson.*;
import com.google.common.collect.*;
import com.shuidihuzhu.cf.clewtrack.configure.validator.ValidatorUtil;
import com.shuidihuzhu.cf.clewtrack.constants.CfClewCommonCons;
import com.shuidihuzhu.cf.clewtrack.delegate.IRecruitDelegate;
import com.shuidihuzhu.cf.clewtrack.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.*;
import com.shuidihuzhu.cf.clewtrack.enums.*;
import com.shuidihuzhu.cf.clewtrack.facade.*;
import com.shuidihuzhu.cf.clewtrack.facade.impl.CfClewBaseLayerFacade;
import com.shuidihuzhu.cf.clewtrack.facade.impl.CfOneToOneFuWuInfoQueryFacade;
import com.shuidihuzhu.cf.clewtrack.model.*;
import com.shuidihuzhu.cf.clewtrack.model.admin.AdminUserAccountModel;
import com.shuidihuzhu.cf.clewtrack.model.vo.FollowUpTaskPhaseVO;
import com.shuidihuzhu.cf.clewtrack.param.*;
import com.shuidihuzhu.cf.clewtrack.param.qywechat.TaskStatisticsParam;
import com.shuidihuzhu.cf.clewtrack.result.*;
import com.shuidihuzhu.cf.clewtrack.service.*;
import com.shuidihuzhu.cf.clewtrack.service.clewindex.ICfClewTaskAttachIndexService;
import com.shuidihuzhu.cf.clewtrack.service.impl.OperateLogService;
import com.shuidihuzhu.cf.clewtrack.utils.CrowdfundingUtil;
import com.shuidihuzhu.cf.clewtrack.utils.DateLimitUtils;
import com.shuidihuzhu.cf.clewtrack.utils.excel.ExcelUtil;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewNoticeRecordsDao;
import com.shuidihuzhu.cf.data.platform.client.ExportLargeExcelClient;
import com.shuidihuzhu.cf.data.platform.enums.FileType;
import com.shuidihuzhu.cf.data.platform.model.SmartExportParam;
import com.shuidihuzhu.cf.enhancer.utils.MaskUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.KongmingDataApiClient;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.dto.ExternalLoginRequest;
import com.shuidihuzhu.client.dataservice.kongmingdata.v1.dto.GuanDataSSOLoginVO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.*;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageSearchResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 1v1 微信服务 查询、报表等相关接口
 */
@Controller
@RequestMapping(path = "/api/cf/clewtrack/one-to-one-fuwu-clew",method = RequestMethod.POST)
@Slf4j
@RefreshScope
public class CfOneToOneFuWuInfoController {
    @Resource(name = "cfOneToOneFuWuInfoQueryFacade")
    private CfOneToOneFuWuInfoQueryFacade cfClewQueryDataFacade;

    @Autowired
    private ICfOneToOneDataQueryFacade cfOneToOneDataQueryFacade;

    @Autowired
    private Tracing tracing;
    @Autowired
    private IClewUserInfoFacade clewUserInfoFacade;
    @Autowired
    private ICfOneToOneFuWuInfoService cfOneToOneFuWuInfoServiceImpl;
    @Autowired
    private ITaskHandlerFacade taskHandlerFacade;
    @Autowired
    private CfClewPacketService cfClewPacketService;
    @Autowired
    private ICfClewWorkContentTypeService cfClewWorkContentTypeServiceImpl;

    private ExecutorService doRequestForLeaderExecutor;
    @Autowired
    private IClewInfoReadFacade iClewInfoReadFacade;

    @Autowired
    private CfClewAnnotationRecordsService clewAnnotationRecordsService;

    @Autowired
    private CfClewNoticeRecordsDao cfClewNoticeRecordsDao;

    @Autowired
    private CfClewBaseLayerFacade cfClewBaseLayerFacade;
    @Autowired
    private OperateLogService operateLogService;
    @Autowired
    private CfClewAnnouncementService clewAnnouncementService;
    @Autowired
    private IClewUserOrgService clewUserOrgService;
    @Autowired
    private CfClewManualAssignFacade manualAssignFacade;
    @Autowired
    private CfClewManualAssignInfoService manualAssignInfoService;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ICfClewTaskReadService cfClewTaskReadService;
    @Autowired
    private CfClewCallRecordsExtService callRecordsExtService;
    @Autowired
    private CfClewCallRecordsExtFacade cfClewCallRecordsExtFacade;
    @Autowired
    private TransformTaskFacade transformTaskFacade;

    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler redissonHandler;

    @Autowired
    private IRecruitDelegate recruitDelegate;

    @Resource
    private KongmingDataApiClient kongmingDataApiClient;

    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;

    @Resource
    private ExportLargeExcelClient exportLargeExcelClient;

    @Resource
    private MaskUtil maskUtil;

    @Autowired
    private ICfMyClewQueryFacade clewQueryFacade;

    @Autowired
    private ICfClewTaskAttachIndexService clewTaskAttachIndexService;


    @PostConstruct
    public void init(){
        doRequestForLeaderExecutor=tracing.currentTraceContext().executorService( Executors.newFixedThreadPool(10));
    }
    /**
     * 服务列表数据--组员
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/query-need-call-data-for-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CFClewInfoModelResult<CFClewInfoModel>> queryNeedCallDataForZY(@RequestBody CfFuWuClewForFWQueryParam queryParam){
        queryParam.buildDefaultIfTimeIsNull(true);
        queryParam.buildAmoutDonateAreaEnum();
        queryParam.setLoginClewUserId(ContextUtil.getAll().get(CfClewCommonCons.LOGIN_CLEW_USER_ID).toString());
        OpResult<CFClewInfoModelResult<CFClewInfoModel>> opResult = cfClewQueryDataFacade.queryNeedCallDataForZY(queryParam);
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }


    /**
     * 已结束服务列表数据--组员
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/query-handled-data-for-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CFClewInfoModelResult<CFClewInfoModel>> queryHandledDataForZY(@RequestBody CfFuWuClewForFWQueryParam queryParam){
        queryParam.buildDefaultIfTimeIsNull(true);
        queryParam.buildAmoutDonateAreaEnum();
        queryParam.setLoginClewUserId(ContextUtil.getAll().get(CfClewCommonCons.LOGIN_CLEW_USER_ID).toString());
        OpResult<CFClewInfoModelResult<CFClewInfoModel>> opResult = cfClewQueryDataFacade.queryHandledDataForZY(queryParam);
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    /**
     * 服务列表数据--组长
     * @param queryParam
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @RequestMapping(path = "/query-need-call-data-for-leader", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CFClewInfoModelResult<CFClewInfoModel>> queryNeedCallDataForZZ(@RequestBody CfFuWuClewForFWQueryParam queryParam){
        queryParam.buildDefaultIfTimeIsNull(true);
        queryParam.setFuWuNeedCall(true);
        queryParam.buildAmoutDonateAreaEnum();
        if(queryParam.getTaskStatus() == null){
            queryParam.setTaskStatus(CfClewTaskEnums.TaskStatusEnum.ASSGINED_UNHANDLE.getStatus());
        }
        //当登录用户是外包且没有选择组织或者人员查询时，需要设置一个默认的orgId
        if (queryParam.getUserId() != 0 && StringUtils.isBlank(queryParam.getServiceUserId()) && (queryParam.getOrgId() == null || queryParam.getOrgId() == 0)) {
            //判断下是否是外包账号
            Integer outerOrgId = clewUserOrgService.getOuterOrgId(queryParam.getUserId());
            if (outerOrgId != null) {
                queryParam.setOrgId(outerOrgId);
            }
        }
        Future<OpResult<CFClewInfoModelResult<CFClewInfoModel>>> result = doRequestForLeaderExecutor.submit(()->cfClewQueryDataFacade.queryNeedCallDataForZZ(queryParam));

        OpResult<CFClewInfoModelResult<CFClewInfoModel>> opResult;
        try {
            opResult = result.get(2, TimeUnit.SECONDS);
        } catch (Exception e) {
            return NewResponseUtil.makeError(CfClewErrorCode.DO_QUERY_TIME_OUT);
        }
        if(opResult == null){
            return NewResponseUtil.makeError(CfClewErrorCode.CAN_NOT_FIND);
        }
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    /**
     * 已服务列表数据--组长
     * @param queryParam
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @RequestMapping(path = "/query-handled-data-for-leader", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CFClewInfoModelResult<CFClewInfoModel>> queryHandledDataForZZ(@RequestBody CfFuWuClewForFWQueryParam queryParam){
        queryParam.buildDefaultIfTimeIsNull(true);
        queryParam.buildAmoutDonateAreaEnum();
        //当登录用户是外包且没有选择组织或者人员查询时，需要设置一个默认的orgId
        if (queryParam.getUserId() != 0 && StringUtils.isBlank(queryParam.getServiceUserId()) && (queryParam.getOrgId() == null || queryParam.getOrgId() == 0)) {
            //判断下是否是外包账号
            Integer outerOrgId = clewUserOrgService.getOuterOrgId(queryParam.getUserId());
            if (outerOrgId != null) {
                queryParam.setOrgId(outerOrgId);
            }
        }
        Future<OpResult<CFClewInfoModelResult<CFClewInfoModel>> > result = doRequestForLeaderExecutor.submit(()->cfClewQueryDataFacade.queryHandledDataForZZ(queryParam));

        OpResult<CFClewInfoModelResult<CFClewInfoModel>> opResult;
        try {
            opResult = result.get(2, TimeUnit.SECONDS);
        } catch (Exception e) {
            return NewResponseUtil.makeError(CfClewErrorCode.DO_QUERY_TIME_OUT);
        }
        if(opResult == null){
            return NewResponseUtil.makeError(CfClewErrorCode.DO_QUERY_TIME_OUT);
        }
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    /**
     * 查询服务线索跟进提醒--组员
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/get-clew-remind-for-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CFClewRemindModelResult<CFOneToOneClewRemindModel>> getClewRemindForMember(@RequestBody CfFuWuClewRemindQueryParam queryParam){
        if(queryParam.checkGetParams()) {
            return NewResponseUtil.makeError(CfClewErrorCode.TRANSRULE_PARAM_ERROR);
        }
        OpResult<CFClewRemindModelResult<CFOneToOneClewRemindModel>> opResult = cfOneToOneDataQueryFacade.queryClewRemindForMember(queryParam);
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    /**
     * 修改服务线索跟进提醒--组员
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/put-clew-remind-for-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CFClewRemindModelResult<CFOneToOneClewRemindModel>> putClewRemindForMember(@RequestBody CfFuWuClewRemindQueryParam queryParam){
        if(queryParam.checkPutParams()) {
            return NewResponseUtil.makeError(CfClewErrorCode.TRANSRULE_PARAM_ERROR);
        }
        OpResult<CFClewRemindModelResult<CFOneToOneClewRemindModel>> opResult = cfOneToOneDataQueryFacade.insertOrUpdateClewRemind(queryParam);
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    /**
     * 查询服务线索跟进提醒数量--组员
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/get-clew-remind-count-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<Integer> getClewRemindCountMember(@RequestBody CfFuWuClewRemindQueryParam queryParam){
        if(queryParam.checkGetCountParams()) {
            return NewResponseUtil.makeError(CfClewErrorCode.TRANSRULE_PARAM_ERROR);
        }
        OpResult<Integer> opResult = cfOneToOneDataQueryFacade.getClewRemindCountMember(queryParam);
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    /**
     * 查询服务线索详情(根据taskId)
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/get-clew-info-model", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CFClewInfoModel> getClewInfoModelByTaskId(@RequestBody CfFuWuClewRemindQueryParam queryParam){
        if(queryParam.getTaskId() == null) {
            return NewResponseUtil.makeError(CfClewErrorCode.TRANSRULE_PARAM_ERROR);
        }
        OpResult<CFClewInfoModel> opResult = cfOneToOneDataQueryFacade.getClewInfoModelByTaskId(queryParam);
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }


    /**
     * 服务手动待分配列表数据--组长
     */
    @RequestMapping(path = "/distribution-by-leader", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<Void> distributionByLeader(@RequestBody ClewAllocationParam clewAllocationParam){
        Integer workBenchType = WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH.getType();
        Integer workContentType = clewAllocationParam.getWorkContentType() == null ? cfClewWorkContentTypeServiceImpl.getWorkContentTypeByEnum("DRFAT_TEST")
                : clewAllocationParam.getWorkContentType();
        clewAllocationParam.setWorkBenchType(workBenchType);
        clewAllocationParam.setWorkContentType(workContentType);
        clewAllocationParam.setClewTypeEnum(CfClewBaseInfoEnums.ClewTypeEnum.CLEW_TYPE_2);
        clewAllocationParam.setTrackerUserTypeEnum(CfClewTrackUserInfoEnums.TrackerUserTypeEnum.BD_FUWU);
        OpResult<Void> opResult = taskHandlerFacade.distributionByLeader(clewAllocationParam);
        if (opResult.isSuccess()){
            return NewResponseUtil.makeSuccess(opResult.getData());
        }else{
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
    }

    /**
     * 获取服务人员信息
     */
    @RequestMapping(path = "/get-clewuser", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<List<JSONObject>> getClewUser(@RequestBody QueryClewUserInfoParam queryClewUserInfoParam){
        Integer workBenchType = WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH.getType();
        Integer workContentType = queryClewUserInfoParam.getWorkContentType() == null ? cfClewWorkContentTypeServiceImpl.getWorkContentTypeByEnum("DRFAT_TEST")
                : queryClewUserInfoParam.getWorkContentType();
        queryClewUserInfoParam.setWorkBenchType(workBenchType);
        queryClewUserInfoParam.setWorkContentType(workContentType);
        OpResult<List<JSONObject>> opResult = clewUserInfoFacade.getClewUsersByQueryParam(queryClewUserInfoParam);
        if (opResult.isSuccess()){
            return NewResponseUtil.makeSuccess(opResult.getData());
        }else{
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
    }

    /**
     * 获取待分配数据
     */
    @RequestMapping(path = "/get-need-assign-data-for-leader", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<List<CfClewLevelAssignCountModel>> queryNeedAssignDataForZZ(@RequestBody NeedAssignFuWuClewQueryParam needAssignFuWuClewQueryParam){
        Date endDate = DateUtil.addDay(DateUtil.getCurrentDate(),1);
        Date startDate = DateUtil.addDay(DateUtil.getCurrentDate(),-6);
        if (needAssignFuWuClewQueryParam.getTaskHandleDate() != null){
            DateQueryParam dateQueryParam = needAssignFuWuClewQueryParam.getTaskHandleDate();
            CfClewErrorCode cfClewErrorCode = dateQueryParam.dateRegionIsValid();
            if (cfClewErrorCode == CfClewErrorCode.SUCCESS){
                startDate = DateUtil.parseDateTime(dateQueryParam.getStartTime());
                endDate = DateUtil.parseDateTime(dateQueryParam.getEndTime());
            }
        }
        Integer workBenchType = WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH.getType();
        Integer workContentType = needAssignFuWuClewQueryParam.getWorkContentType() == null ? cfClewWorkContentTypeServiceImpl.getWorkContentTypeByEnum("DRFAT_TEST")
                : needAssignFuWuClewQueryParam.getWorkContentType();
        needAssignFuWuClewQueryParam.setWorkBenchType(workBenchType);
        needAssignFuWuClewQueryParam.setWorkContentType(workContentType);
        needAssignFuWuClewQueryParam.setStartDate(startDate);
        needAssignFuWuClewQueryParam.setEndDate(endDate);
        needAssignFuWuClewQueryParam.setTaskType(CfClewTaskEnums.TaskTypeEnum.FUWU_TRACK_TYPE.getType());
        needAssignFuWuClewQueryParam.setDelayHandleList(Lists.newArrayList(CfClewTaskEnums.DelayHandleEnum.DELAY_HANDLE_0.getCode()));
        needAssignFuWuClewQueryParam.setTaskStatusList(Lists.newArrayList(CfClewTaskEnums.TaskStatusEnum.NEW.getStatus()));
        needAssignFuWuClewQueryParam.setPacketId(cfClewPacketService.getPacketIdByType(workBenchType));
        OpResult<List<CfClewLevelAssignCountModel>> opResult = cfOneToOneFuWuInfoServiceImpl.queryNeedAssignDataForZZ(needAssignFuWuClewQueryParam);
        if (opResult.isSuccess()){
            return NewResponseUtil.makeSuccess(opResult.getData());
        }else{
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
    }

    /**
     * 获取可分配夜间线索的服务工作内容
     */
    @RequestMapping(path = "/get-fuwu-workcontent", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<List<JSONObject>> getFuwuWorkContent(){
        OpResult<List<JSONObject>> result = cfOneToOneFuWuInfoServiceImpl.getFuwuWorkContent(WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH.getType());
        if(result.isSuccess()){
            return NewResponseUtil.makeSuccess(result.getData());
        }else{
            return NewResponseUtil.makeError(result.getErrorCode());
        }
    }

    /**
     * 服务组内待分配量
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/get-wait-assign-count", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<Integer> queryWaitAssignCount(@RequestBody CfFuwuWaitAssignCountQueryParam queryParam){
        if(queryParam.checkParams()) {
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }

        OpResult<Integer> opResult = cfOneToOneDataQueryFacade.queryWaitAssignCount(queryParam);

        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    @RequestMapping(path = "/query-qywxchat", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("clewtrack:sensitive-data")
    @ResponseBody
    public Response<ChatMessageSearchResult> queryQywxchat(@RequestBody QueryQyWxChatParam queryParam){
        DateQueryParam dateQueryParam = queryParam.getDateQueryParam();
        CfClewErrorCode dateQueryError = dateQueryParam.dateRegionIsValid();
        if (dateQueryError != CfClewErrorCode.SUCCESS){
            return NewResponseUtil.makeError(dateQueryError);
        }
        queryParam.calcuOffset();
        OpResult<ChatMessageSearchResult> opResult = cfOneToOneDataQueryFacade.queryQywxchatList(queryParam);
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        ChatMessageSearchResult data = opResult.getData();
        Optional.ofNullable(data)
                .filter(r -> CollectionUtils.isNotEmpty(r.getMessageList()))
                .ifPresent(r -> r.getMessageList().forEach(item -> {
                    item.setSenderId(StringUtils.EMPTY);
                    item.setReceiverId(StringUtils.EMPTY);
                }));
        return NewResponseUtil.makeSuccess(data);
    }

    @RequestMapping(path = "/query-followuptaskphase", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<FollowUpTaskPhaseVO> queryFollowUpTaskPhase(){
        OpResult<FollowUpTaskPhaseVO> opResult = cfOneToOneDataQueryFacade.queryFollowUpTaskPhase();
        if(opResult.isFail()){
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    @RequestMapping(path = "/query-count-taskphase", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<FollowUpTaskPhaseVO> queryCountByTaskphase(@RequestBody CfFuWuClewForFWQueryParam queryParam){
        queryParam.buildDefaultIfTimeIsNull(true);
        queryParam.buildAmoutDonateAreaEnum();
        //当登录用户是外包且没有选择组织或者人员查询时，需要设置一个默认的orgId
        if (queryParam.getUserId() != 0 && StringUtils.isBlank(queryParam.getServiceUserId()) && (queryParam.getOrgId() == null || queryParam.getOrgId() == 0) && queryParam.getIsManager()) {
            //判断下是否是外包账号
            Integer outerOrgId = clewUserOrgService.getOuterOrgId(queryParam.getUserId());
            if (outerOrgId != null) {
                queryParam.setOrgId(outerOrgId);
            }
        }
        queryParam.setLoginClewUserId(ContextUtil.getAll().get(CfClewCommonCons.LOGIN_CLEW_USER_ID).toString());
        OpResult<FollowUpTaskPhaseVO> opResult = cfClewQueryDataFacade.queryCountByTaskphase(queryParam);
        if (opResult.isFail()) {
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }
        return NewResponseUtil.makeSuccess(opResult.getData());
    }

    /**
     * 根据任务id查询信息
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/query-info-by-taskid", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @RequiresPermission("clewtrack:sensitive-data")
    @ResponseBody
    public Response<CFClewInfoModel> queryClewInfoById(@RequestBody WhQueryBindFuwuInfoParam queryParam){
        queryParam.setLoginClewUserId(ContextUtil.getAll().get(CfClewCommonCons.LOGIN_CLEW_USER_ID).toString());
        queryParam.setWorkbenchType(WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH.getType());
        OpResult<CFClewInfoModel> result = cfClewQueryDataFacade.getClewInfoByTaskId(queryParam);
        if(result.isSuccess()){
            return NewResponseUtil.makeSuccess(result.getData());
        }else{
            return NewResponseUtil.makeError(result.getErrorCode());
        }
    }

    /**
     *
     * @param query
     * @return
     */
    @ApiOperation("分配明细查询接口")
    @RequestMapping(path = "/list-manual-assign", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonResultModel> listManualAssign(@RequestBody CfClewManualAssignParam query){
        if (query.getStartTime() == null){
            String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
            query.setStartTime(todayStart);
        }
        log.info("listManualAssign param:{}",JSONObject.toJSON(query));
        OpResult<CommonResultModel> result = manualAssignFacade.queryManualAssignByPage(query);
        if (result.isSuccess()){
            return NewResponseUtil.makeSuccess(result.getData());
        }else {
            return NewResponseUtil.makeFail(result.getMessage());
        }
    }

    /**
     *
     * @param id
     * @return
     */
    @ApiOperation("导出分配明细")
    @RequestMapping(path = "/export-manual-assign-info", produces = "application/json;charset=UTF-8", method = RequestMethod.GET)
    @ResponseBody
    public Response exportManualAssignInfo(@RequestParam(required = true) Long id,HttpServletResponse response){
        List<CfClewManualAssignInfoDO> assignInfoDOList = manualAssignInfoService.queryAssignByAssignId(id);
        List<ExportManualAssignModel> data = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(assignInfoDOList)){
            data = assignInfoDOList.stream().map(s -> ExportManualAssignModel.doCovertModel(s, shuidiCipher)).collect(Collectors.toList());
        }
        String fileName = "分配明细-"+DateUtil.getYMDStringByDate(new Date());
        exportLargeExcelClient.writeExcelSmart(ContextUtil.getAdminLongUserId(), SmartExportParam.create(
                ExcelUtil.convertEasyExcelHeaders(ExportManualAssignModel.class),
                (List) data,
                fileName, FileType.EXCEL
        ));
        return NewResponseUtil.makeSuccess();
//        String sheetName = "分配明细";
//        try {
//            EasyExcelUtil.exportByModel(response,fileName,sheetName,data, ExportManualAssignModel.class);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    @ApiOperation("通话记录打分")
    @RequestMapping(path = "/save-score", produces = "application/json;charset=UTF-8", method = RequestMethod.GET)
    @ResponseBody
    public Response<Void> saveScore(@RequestParam(required = true) Integer id,
                                    @RequestParam(required = true) String score,
                                    @RequestParam(required = true) String clewUserId) {
        return scoreCall(id, score, clewUserId, 0);
    }

    @NotNull
    private Response<Void> scoreCall(Integer id, String score, String clewUserId, int callResource) {
        log.info("saveScore id:{},score:{},userId:{}", id, score, clewUserId);
        if (!this.checkFloatDecimal2Num(score)) {
            return NewResponseUtil.makeFail("评分保留两位小数");
        }
        CfClewCallRecordsExtDO recordsExtDO = new CfClewCallRecordsExtDO();
        recordsExtDO.setCallId(id);
        recordsExtDO.setCreateTime(new Date());
        recordsExtDO.setScore((int) (Float.parseFloat(score) * 100));
        recordsExtDO.setScoreMis(clewUserId);
        recordsExtDO.setCallResource(callResource);
        OpResult<ClewTrackerUserInfoModel> userInfo = clewUserInfoFacade.getClewUserInfoDetailWithBenchType(clewUserId, CfClewTrackUserInfoEnums.TrackerUserTypeEnum.BD_FUWU, WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH);
        if (userInfo.isFail()) {
            return NewResponseUtil.makeFail(userInfo.getMessage());
        }
        recordsExtDO.setScoreName(userInfo.getData().getUserName());
        recordsExtDO.setScoreDate(new Date());
        int num = callRecordsExtService.saveRecordExt(recordsExtDO);
        if (num > 0) {
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeFail(ErrorCode.SYSTEM_ERROR.getMsg());
    }


    @ApiOperation("企信通话记录打分")
    @GetMapping(path = "/save-qi-work-score")
    @ResponseBody
    public Response<Void> saveQiWorkScore(@RequestParam Integer id,
                                          @RequestParam String score,
                                          @RequestParam String clewUserId) {
        return scoreCall(id, score, clewUserId, 1);
    }




    private boolean checkFloatDecimal2Num(String score){
        if (StringUtils.isNotEmpty(score)){
            if((score).contains(".")){
                String decimal= (score).split("\\.")[1];
                if (decimal.length()>2){
                    return false;
                }
            }
        }
        return true;
    }

    @ApiOperation("通话记录打分列表")
    @RequestMapping(path = "/list-score", produces = "application/json;charset=UTF-8", method = RequestMethod.GET)
    @ResponseBody
    public Response<CommonModelResult> listScore(CfClewCallExtParam param){

        log.info("listScore param:{}",JSONObject.toJSON(param));
        if (DateLimitUtils.checkTimeIsTooLong(DateUtil.getDateFromLongString(param.getScoreStartTime()),
                DateUtil.getDateFromLongString(param.getScoreEndTime()), 13)){
            String msg = String.format(CfClewErrorCode.TIME_TOO_LONG.getMsg(),14);
            return NewResponseUtil.makeResponse(CfClewErrorCode.TIME_TOO_LONG.getCode(),msg,null);
        }
        if (param.getScoreFrom()!=null){
            param.setScoreFrom(param.getScoreFrom()*100);
        }
        if (param.getScoreTo()!=null){
            param.setScoreTo(param.getScoreTo()*100);
        }
        param.setWorkbenchType(WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH);
        OpResult<CommonModelResult> result = cfClewCallRecordsExtFacade.listTopScoreByParam(param);
        if (result.isSuccess()){
            return NewResponseUtil.makeSuccess(result.getData());
        }else {
            return NewResponseUtil.makeFail(result.getMessage());
        }
    }
    /**
     * 智能外呼
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/call_robot_event", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CallRobotModel> callRobotEvent(@RequestBody CfClewCallRobotParam queryParam){
        log.info("callRobotEvent start,param:{}", JSON.toJSONString(queryParam));
        String taskId = queryParam.getTaskId();
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(queryParam.getPhoneType())){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        OpResult<Long> booleanOpResult = iClewInfoReadFacade.doCallRobotEvent(queryParam);
        if (booleanOpResult.isSuccess()){
            CallRobotModel model = new CallRobotModel();
            model.setAvailable(false);
            model.setId(booleanOpResult.getData());
            return NewResponseUtil.makeSuccess(model);
        }else {
            return NewResponseUtil.makeError(booleanOpResult.getErrorCode());
        }

    }

    /**
     * 检查智能外呼能否触发
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/check_call_robot_event", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CallRobotModel> checkCallRobotEvent(@RequestBody CfClewCallRobotParam queryParam){
        log.info("callRobotEvent start,param:{}", JSON.toJSONString(queryParam));
        String taskId = queryParam.getTaskId();
        if (StringUtils.isEmpty(taskId) ){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        OpResult<CallRobotModel> booleanOpResult = iClewInfoReadFacade.doCheckCallRobotEvent(queryParam);
        if (booleanOpResult.isSuccess()){
            return NewResponseUtil.makeSuccess(booleanOpResult.getData());
        }else {
            return NewResponseUtil.makeError(booleanOpResult.getErrorCode());
        }

    }

    /**
     * 组长获取通话记录
     *
     * 该接口用于组长查询通话记录，支持权限控制和数据脱敏处理
     *
     * @param queryParam 通话记录查询参数，包含以下字段：
     *                   - clewId: 线索ID
     *                   - taskId: 任务ID
     *                   - queryType: 手机号类型查询类型(1:原始,2:换号)
     *                   - dateQueryParam: 查询时间范围
     *                   - tab: 查询类型(0:全部 1:有录音 2:无录音)
     *                   - phone: 查询手机号
     * @return Response<List<ClewCallRecordModel>> 通话记录列表响应
     *         - 成功时返回通话记录列表，手机号已脱敏处理
     *         - 失败时返回错误码和错误信息
     */
    @RequestMapping(path = "/list-call-records", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<List<ClewCallRecordModel>> listCallRecords(@RequestBody CfCallRecordQueryParam queryParam){
        // 1. 从上下文中获取当前登录用户的线索用户ID
        // ContextUtil.getAll()返回当前线程的上下文信息Map
        // CfClewCommonCons.LOGIN_CLEW_USER_ID是存储登录用户ID的常量key
        String clewUserId = ContextUtil.getAll().get(CfClewCommonCons.LOGIN_CLEW_USER_ID).toString();

        // 2. 检查当前用户是否为管理员（组长权限）
        // clewUserInfoFacade.checkIsManager()用于验证用户在指定工作台类型下是否具有管理员权限
        // WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH表示服务工作台类型
        // 返回OpResult<Boolean>，data为true表示是管理员，false表示普通成员
        OpResult<Boolean> opResult = clewUserInfoFacade.checkIsManager(clewUserId, clewUserId, WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH.getType());
        if (opResult.isFail()) {
            // 权限检查失败，返回错误响应
            return NewResponseUtil.makeError(opResult.getErrorCode());
        }

        // 3. 调用数据查询门面服务获取通话记录
        // cfClewQueryDataFacade.listCallRecords()根据查询参数获取通话记录列表
        OpResult<List<ClewCallRecordModel>> result = cfClewQueryDataFacade.listCallRecords(queryParam);

        // 4. 对查询结果进行手机号脱敏处理
        // 使用Optional链式调用确保安全处理null值和空集合
        Optional.ofNullable(result)
                .filter(OpResult::isSuccess)  // 过滤成功的结果
                .filter(r -> CollectionUtils.isNotEmpty(r.getData()))  // 过滤非空数据
                .ifPresent(r -> r.getData().forEach(item -> {
                    // 获取加密的客户手机号
                    String encryptCustomerNumber = item.getEncryptCustomerNumber();
                    // 使用maskUtil工具对手机号进行脱敏处理，生成掩码显示格式
                    // buildByDecryptPhone()方法会先解密手机号，然后生成脱敏格式（如：138****1234）
                    item.setEncryptCustomerNumberMask(maskUtil.buildByDecryptPhone(encryptCustomerNumber));
                    // 清空原始加密手机号，避免敏感信息泄露
                    item.setEncryptCustomerNumber(null);
                }));

        // 5. 根据查询结果和用户权限返回相应数据
        if(result.isSuccess()){
            // 查询成功，根据用户权限返回数据
            if (opResult.getData()) {
                // 用户是管理员（组长），返回所有通话记录
                return NewResponseUtil.makeSuccess(result.getData());
            }
            // 用户是普通成员，只返回该用户自己的通话记录
            // 通过userId过滤，确保普通用户只能看到自己的通话记录
            return NewResponseUtil.makeSuccess(result.getData().stream()
                    .filter(item -> item.getUserId().equals(clewUserId))
                    .collect(Collectors.toList()));
        }else{
            // 查询失败，返回错误响应
            return NewResponseUtil.makeError(result.getErrorCode());
        }
    }

    /**
     * 批注列表
     * 获取批注记录
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/list-annotation-records", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> listAnnotationRecords(@RequestBody CfClewAnnotationParam queryParam) {
        log.info("listAnnotationRecords start param:{}", JSON.toJSONString(queryParam));
        if (null == queryParam) {
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        OpResult<CommonModelResult> result = clewAnnotationRecordsService.queryAnnotationRecordsByPage(queryParam);
        if (result.isSuccess()) {
            return NewResponseUtil.makeSuccess(result.getData());
        } else {
            return NewResponseUtil.makeError(result.getErrorCode());
        }

    }

    /**
     * 组长今日代办和详情页
     * 获取批注记录列表
     *
     * @param queryParam
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @RequestMapping(path = "/get-annotation-leader", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> getAnnotationLeader( @RequestBody CfClewAnnotationParam queryParam) {
        log.info("getAnnotationLeader start, param:{}", JSON.toJSONString(queryParam));
        if (StringUtils.isEmpty(queryParam.getTaskId())) {
            //今日代办也没批注列表条件
            queryParam.setClewMis(queryParam.getClewUserId());
        }
        String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
        queryParam.setStartTime(todayStart);
        OpResult<CommonModelResult> result = clewAnnotationRecordsService.queryAnnotationRecordsByPage(queryParam);
        if (result.isSuccess()) {
            CommonModelResult data = result.getData();
            return NewResponseUtil.makeSuccess(data);
        } else {
            return NewResponseUtil.makeError(result.getErrorCode());
        }

    }

    /**
     * 组员今日代办和详情页
     * 获取批注记录列表
     *
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/get-annotation-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> getAnnotationMember( @RequestBody CfClewAnnotationParam queryParam) {
        log.info("getAnnotationMember start， param:{}", JSON.toJSONString(queryParam));
        if (StringUtils.isEmpty(queryParam.getTaskId())) {
            //今日代办也没批注列表条件
            queryParam.setClewMis(queryParam.getClewUserId());
        }
        String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
        queryParam.setStartTime(todayStart);
        OpResult<CommonModelResult> result = clewAnnotationRecordsService.queryAnnotationRecordsByPage(queryParam);
        if (result.isSuccess()) {
            CommonModelResult data = result.getData();
            return NewResponseUtil.makeSuccess(data);
        } else {
            return NewResponseUtil.makeError(result.getErrorCode());
        }

    }

    /**
     * 更新批注记录
     *
     * @param queryParam
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @RequestMapping(path = "/modify-annotation-record", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> modifyAnnotationRecords( @RequestBody CfClewAnnotationParam queryParam) {
        log.info("modifyAnnotationRecords start para:{}", JSON.toJSONString(queryParam));
        Map<String, StringBuffer> check = ValidatorUtil.validate(queryParam);
        if (null != check) {
            return NewResponseUtil.makeResponse(CfClewErrorCode.SYSTEM_PARAM_ERROR.getCode(), JSON.toJSONString(check), null);
        }
        queryParam.setAnnotatorMis(queryParam.getClewUserId());
        OpResult<Integer> result = null;
        //新增
        if (null == queryParam.getId()) {
            result = clewAnnotationRecordsService.saveAnnotations(queryParam, ContextUtil.getAdminUserId());
        } else {//修改
            result = clewAnnotationRecordsService.modifyAnnotationById(queryParam, ContextUtil.getAdminUserId());
        }
        if (result.isSuccess()) {
            return NewResponseUtil.makeSuccess("success");
        } else {
            return NewResponseUtil.makeError(result.getErrorCode());
        }

    }

    /**
     * 确认处理 批注
     *
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/confirm-annotation", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> confirmAnnotation(@RequestBody CfClewAnnotationParam queryParam) {
        log.info("confirmAnnotation start param:{}",JSON.toJSONString(queryParam));
        Long id = queryParam.getId();
        String clewUserId = queryParam.getClewUserId();
        OpResult<Boolean> result = clewAnnotationRecordsService.confirmAnnotationRecord(id, clewUserId);
        if (result.isSuccess()) {
            return NewResponseUtil.makeSuccess("success");
        } else {
            return NewResponseUtil.makeError(result.getErrorCode());
        }

    }

    /**
     * 逻辑删除 批注
     *
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/delete-annotation-record", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> deleteAnnotationRecord(@RequestBody CfClewAnnotationParam queryParam) {
        log.info("deleteAnnotationRecord start param:{}", JSONObject.toJSON(queryParam));
        Long id = queryParam.getId();
        String clewUserId = queryParam.getClewUserId();
        OpResult<Boolean> result = clewAnnotationRecordsService.deleteAnnotationRecord(id, clewUserId, ContextUtil.getAdminUserId());
        if (result.isSuccess()) {
            return NewResponseUtil.makeSuccess("success");
        } else {
            return NewResponseUtil.makeError(result.getErrorCode());
        }

    }

    /**
     * 导出 批注
     *
     * @param params
     * @return
     */
//    @RequiresPermission("clew-info:download-annotation-records")
    @RequestMapping(path = "/download-annotation-records", method = RequestMethod.GET)
    @ResponseBody
    public void downloadAnnotationRecord(HttpServletRequest request ,HttpServletResponse response, @RequestParam(value = "params") String params) {
        log.info("downloadAnnotationRecord start param:{}",params);
        CfClewAnnotationParam queryParam = JSONObject.parseObject(params,CfClewAnnotationParam.class);
        if (StringUtils.isEmpty(queryParam.getStartTime())){
            String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
            queryParam.setStartTime(todayStart);
        }
        String misName = request.getParameter("misName");
        if (StringUtils.isEmpty(misName)){
            misName = queryParam.getClewUserId();
        }
        OpResult<List<CfClewAnnotationRecordsDO>> result = clewAnnotationRecordsService.queryAnnotationRecordsByParam(queryParam);
        if (result.isSuccess()) {
            String fileName ="批注-"+misName+"-"+DateUtil.formatDateTimeNew(new Date());
            String[] headers = {"批注id", "被批注人", "关联手机号", "问题类型", "备注", "批注时间", "批注人", "状态", "业务类型", "期望完成时间"};
            List<Object> collections = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(result.getData())) {
                for (CfClewAnnotationRecordsDO model : result.getData()) {
                    Map<String, Object> s1 = Maps.newHashMap();
                    s1.put(headers[0], model.getId());
                    s1.put(headers[1], model.getBeAnnotator());
                    s1.put(headers[2], CrowdfundingUtil.getTelephoneMask(model.getDecryptPhone()));
                    s1.put(headers[3], CfClewAnnotation.QuestionTypeEnums.getDescByCode(model.getQuestionType()));
                    s1.put(headers[4], model.getRemark());
                    s1.put(headers[5], DateUtil.formatDateTime(model.getCreateTime()));
                    s1.put(headers[6], model.getAnnotator());
                    s1.put(headers[7], model.getStatus() == 0 ? "未确认" : "已确认");
                    s1.put(headers[8], WorkbenchEnums.WorkbenchTypeEnum.getByType(model.getWorkbenchType()).getDesc());
                    s1.put(headers[9], DateUtil.formatDateTime(model.getExpectCompleteTime()));
                    collections.add(s1);
                }
            }
            exportLargeExcelClient.writeExcelSmart(ContextUtil.getAdminLongUserId(), SmartExportParam.create(
                    Arrays.stream(headers).map(r -> SmartExportParam.Header.builder().key(r).name(r).build()).collect(Collectors.toList()),
                    collections,
                    fileName, FileType.EXCEL
            ));


//            try {
//                String fileName ="批注-"+misName+"-"+DateUtil.formatDateTimeNew(new Date()) + ".xls";
//                response.setHeader(HttpHeaderNames.CONTENT_DISPOSITION.toString(),
//                        CfClewCommonCons.CONTENT_DISPOSITION_PREFIX_VALUE + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
//                String[] headers = {"批注id", "被批注人", "关联手机号", "问题类型", "备注", "批注时间", "批注人", "状态", "业务类型"};
//                List<Map<String, Object>> collections = Lists.newArrayList();
//                if (CollectionUtils.isNotEmpty(result.getData())) {
//                    for (CfClewAnnotationRecordsDO model : result.getData()) {
//                        Map<String, Object> s1 = Maps.newHashMap();
//                        s1.put(headers[0], model.getId());
//                        s1.put(headers[1], model.getBeAnnotator());
//                        s1.put(headers[2], CrowdfundingUtil.getTelephoneMask(model.getDecryptPhone()));
//                        s1.put(headers[3], CfClewAnnotation.QuestionTypeEnums.getDescByCode(model.getQuestionType()));
//                        s1.put(headers[4], model.getRemark());
//                        s1.put(headers[5], DateUtil.formatDateTime(model.getCreateTime()));
//                        s1.put(headers[6], model.getAnnotator());
//                        s1.put(headers[7], model.getStatus() == 0 ? "未确认" : "已确认");
//                        s1.put(headers[8], WorkbenchEnums.WorkbenchTypeEnum.getByType(model.getWorkbenchType()).getDesc());
//                        collections.add(s1);
//                    }
//                }
//                OutputStream outputStream = response.getOutputStream();
//                ExcelUtil.exportExcel(headers, collections, outputStream);
//            } catch (IOException e) {
//                log.error(this.getClass().getSimpleName() + "downloadAnnotationRecord", e);
//            }
        }
    }

    /**
     * 今日代办 服务阶段枚举
     *
     * @return
     */
    @RequestMapping(path = "/get-service-phase", produces = "application/json;charset=UTF-8", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<JSONObject>> getServicePhase() {
        log.info("getServicePhase start ");
        List<JSONObject> array = new ArrayList<>();
        for (FollowUpTaskEnums.TaskPhaseEnum taskPhaseEnum : FollowUpTaskEnums.TaskPhaseEnum.values()) {
            JSONObject object = new JSONObject();
            if (FollowUpTaskEnums.TaskPhaseEnum.NEW.getCode().equals(taskPhaseEnum.getCode())){
                continue;
            }
            if (!WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH.equals(taskPhaseEnum.getWorkbenchTypeEnum())){
                continue;
            }
            object.put("value", taskPhaseEnum.getCode());
            object.put("label", taskPhaseEnum.getDesc());
            object.put("orderNumber",taskPhaseEnum.getOrderNum());
            array.add(object);
        }
        List<JSONObject> collect = array.stream().sorted(((o1, o2) -> o1.getInteger("orderNumber").compareTo(o2.getInteger("orderNumber")))).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(collect);

    }

    /**
     * 组长
     * 查询登陆员工的今日任务提醒
     * @param queryParam
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @RequestMapping(path = "/get-notice-records-leader", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> getNoticeRecordsLeader( @RequestBody SimplePageParam queryParam) {
        queryParam.setUserId(queryParam.getClewUserId());
        log.info("getNoticeRecordsLeader start param:{}", JSONObject.toJSON(queryParam));
        if (null == queryParam || StringUtils.isEmpty(queryParam.getUserId())) {
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        CommonModelResult pageModel = new CommonModelResult();
        String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
        int offSet = (queryParam.getPageNo() - 1) * queryParam.getPageSize();
        List<CfClewNoticeRecordsDO> cfClewNoticeRecordsDOS = cfClewNoticeRecordsDao.selectNoticeByPage(queryParam.getUserId(), todayStart, offSet, queryParam.getPageSize());
        List<Long> taskIdList = cfClewNoticeRecordsDOS.stream().filter(s -> s.getTaskId() != null).map(CfClewNoticeRecordsDO::getTaskId).collect(Collectors.toList());
        List<CfClewTaskDO> taskByTaskIds = cfClewTaskReadService.getTaskByTaskIds(Sets.newHashSet(taskIdList));
        Map<Long, Integer> taskIdMap = taskByTaskIds.stream().collect(Collectors.toMap(CfClewTaskDO::getId, CfClewTaskDO::getWorkbenchType));
        List<CfClewNoticeRecordsModel> recordsModels = CollectionUtils.isNotEmpty(cfClewNoticeRecordsDOS) ? cfClewNoticeRecordsDOS.stream().
                map(record -> CfClewNoticeRecordsModel.buildNoticeModel(record,taskIdMap)).collect(Collectors.toList()) : null;
        pageModel.setData(recordsModels);
        int count = cfClewNoticeRecordsDao.selectCountNoticeByUserId(queryParam.getClewUserId(), todayStart);
        pageModel.setTotalCount(count);
        return NewResponseUtil.makeSuccess(pageModel);
    }

    /**
     * 组员
     * 查询登陆员工的今日任务提醒
     *
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/get-notice-records-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> getNoticeRecordsMember(@RequestBody SimplePageParam queryParam) {
        queryParam.setUserId(queryParam.getClewUserId());
        log.info("getNoticeRecordsMember start param:{}", JSONObject.toJSON(queryParam));
        if (null == queryParam || StringUtils.isEmpty(queryParam.getUserId())) {
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        CommonModelResult pageModel = new CommonModelResult();
        String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
        int offSet = (queryParam.getPageNo() - 1) * queryParam.getPageSize();
        List<CfClewNoticeRecordsDO> cfClewNoticeRecordsDOS = cfClewNoticeRecordsDao.selectNoticeByPage(queryParam.getUserId(), todayStart, offSet, queryParam.getPageSize());
        List<Long> taskIdList = cfClewNoticeRecordsDOS.stream().filter(s -> s.getTaskId() != null).map(CfClewNoticeRecordsDO::getTaskId).collect(Collectors.toList());
        List<CfClewTaskDO> taskByTaskIds = cfClewTaskReadService.getTaskByTaskIds(Sets.newHashSet(taskIdList));
        Map<Long, Integer> taskIdMap = taskByTaskIds.stream().collect(Collectors.toMap(CfClewTaskDO::getId, CfClewTaskDO::getWorkbenchType));
        List<CfClewNoticeRecordsModel> recordsModels = CollectionUtils.isNotEmpty(cfClewNoticeRecordsDOS) ? cfClewNoticeRecordsDOS.stream().
                map(record -> CfClewNoticeRecordsModel.buildNoticeModel(record,taskIdMap)).collect(Collectors.toList()) : null;
        pageModel.setData(recordsModels);
        int count = cfClewNoticeRecordsDao.selectCountNoticeByUserId(queryParam.getClewUserId(), todayStart);
        pageModel.setTotalCount(count);
        return NewResponseUtil.makeSuccess(pageModel);
    }

    /**
     * 查看批注/公告操作记录
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/get-operate-records", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult<List<AnnotationOperatorLogModel>>> getOperateRecords(@RequestBody SimplePageParam queryParam) {
        queryParam.setUserId(queryParam.getClewUserId());
        log.info("getOperateRecords start param:{}", JSONObject.toJSON(queryParam));
        if (null == queryParam) {
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        OperateLogDTO operateLogDTO = new OperateLogDTO();
        operateLogDTO.setPageSize(queryParam.getPageSize());
        operateLogDTO.setPageNo(queryParam.getPageNo());
        if (queryParam.getType() == 1){ //批注
            operateLogDTO.setOperateType(CfOperatingRecordDO.OperateTypeEnum.ANNOTATION_RECORD.getType());
        }else {
            operateLogDTO.setOperateType(CfOperatingRecordDO.OperateTypeEnum.ANNOUNCEMENT_RECORD.getType());
        }
        int adminUserId = ContextUtil.getAdminUserId();
        if (adminUserId>100000000){
            operateLogDTO.setIsOuter(1);
        }
        CommonModelResult<List<AnnotationOperatorLogModel>> commonModelResult = new CommonModelResult<>();
        List<CfOperatingRecordDO> operatelogList = operateLogService.getOperatelogList(operateLogDTO);
        long count = operateLogService.getOperatelogCount(operateLogDTO);
        commonModelResult.setData(operatelogList.stream().map(cfOperatingRecordDO -> AnnotationOperatorLogModel.buildOperatorLogVO(cfOperatingRecordDO)).collect(Collectors.toList()));
        commonModelResult.setTotalCount(count);
        return NewResponseUtil.makeSuccess(commonModelResult);
    }

    /**
     *  问题类型枚举
     *
     * @return
     */
    @RequestMapping(path = "/get-question-type", produces = "application/json;charset=UTF-8", method = RequestMethod.GET)
    @ResponseBody
    public Response<JSONArray> getQuestionType() {
        log.info("getQuestionType start ");
        JSONArray array = new JSONArray();
        for (CfClewAnnotation.QuestionTypeEnums question : CfClewAnnotation.QuestionTypeEnums.values()) {
            JSONObject object = new JSONObject();
            object.put("value", question.getCode());
            object.put("label", question.getDesc());
            array.add(object);
        }
        return NewResponseUtil.makeSuccess(array);

    }

    /**
     * 公告列表
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/list-announcement-records", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> listAnnouncementRecords(@RequestBody CfClewAnnouncementParam queryParam){
        log.info("listAnnouncementRecords start,param:{}",JSONObject.toJSON(queryParam));
        if (null == queryParam){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        OpResult<CommonModelResult> announcementPageByParam = clewAnnouncementService.getAnnouncementPageByParam(queryParam);
        if (announcementPageByParam.isSuccess()){
            return NewResponseUtil.makeSuccess(announcementPageByParam.getData());
        }else {
            return NewResponseUtil.makeError(announcementPageByParam.getErrorCode());
        }

    }

    /**
     * 今日待办，组长接口
     * @param queryParam
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @RequestMapping(path = "/list-announcement-leader", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> listAnnouncementLeader(@RequestBody CfClewAnnouncementParam queryParam){
        log.info("listAnnouncementLeader start,param:{}",JSONObject.toJSON(queryParam));
        if (null == queryParam){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfClewAnnouncementParam param = new CfClewAnnouncementParam();
        param.setWorkbenchType(queryParam.getWorkbenchType());
        param.setPageNo(queryParam.getPageNo());
        param.setPageSize(queryParam.getPageSize());
        String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
        param.setStartTime(todayStart);
        param.setAnnouncementRecipientMis(queryParam.getClewUserId());
        param.setClewUserId(queryParam.getClewUserId());
        OpResult<CommonModelResult> announcementPageByParam = clewAnnouncementService.getAnnouncementTodayPageByParam(param);
        if (announcementPageByParam.isSuccess()){
            return NewResponseUtil.makeSuccess(announcementPageByParam.getData());
        }else {
            return NewResponseUtil.makeError(announcementPageByParam.getErrorCode());
        }

    }

    /**
     * 今日待办，组员接口
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/list-announcement-member", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<CommonModelResult> listAnnouncementMember(@RequestBody CfClewAnnouncementParam queryParam){
        log.info("listAnnouncementMember start,param:{}",JSONObject.toJSON(queryParam));
        if (null == queryParam){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfClewAnnouncementParam param = new CfClewAnnouncementParam();
        param.setWorkbenchType(queryParam.getWorkbenchType());
        param.setPageNo(queryParam.getPageNo());
        param.setPageSize(queryParam.getPageSize());
        String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
        param.setStartTime(todayStart);
        param.setAnnouncementRecipientMis(queryParam.getClewUserId());
        param.setClewUserId(queryParam.getClewUserId());
        OpResult<CommonModelResult> announcementPageByParam = clewAnnouncementService.getAnnouncementTodayPageByParam(param);
        if (announcementPageByParam.isSuccess()){
            return NewResponseUtil.makeSuccess(announcementPageByParam.getData());
        }else {
            return NewResponseUtil.makeError(announcementPageByParam.getErrorCode());
        }
    }

    /**
     *公告编辑和新增接口
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/modify-announcement-record", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> modifyAnnouncementRecord(@RequestBody CfClewAnnouncementParam queryParam){
        log.info("listAnnouncementMember start,param:{}",JSONObject.toJSON(queryParam));
        if (null == queryParam){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        OpResult<Boolean> booleanOpResult = null;
        if (null == queryParam.getId()){
            booleanOpResult = clewAnnouncementService.addAnnouncement(queryParam, ContextUtil.getAdminUserId());
        }else {
            booleanOpResult = clewAnnouncementService.modifyAnnouncement(queryParam, ContextUtil.getAdminUserId());
        }
        if (booleanOpResult.isSuccess()){
            return NewResponseUtil.makeSuccess("success");
        }else {
            return NewResponseUtil.makeError(booleanOpResult.getErrorCode());
        }
    }

    /**
     *公告删除接口
     * @param queryParam
     * @return
     */
    @RequestMapping(path = "/delete-announcement-record", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> deleteAnnouncementRecord(@RequestBody CfClewAnnouncementParam queryParam) {
        log.info("deleteAnnouncementRecord start,param:{}", JSONObject.toJSON(queryParam));
        if (null == queryParam || null == queryParam.getId()) {
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        OpResult<Boolean> booleanOpResult = clewAnnouncementService.deleteAnnouncement(queryParam, ContextUtil.getAdminUserId());
        if (booleanOpResult.isSuccess()) {
            return NewResponseUtil.makeSuccess("success");
        } else {
            return NewResponseUtil.makeError(booleanOpResult.getErrorCode());
        }
    }

//    @RequiresPermission("clew-info:download-announcement-records")
    @RequestMapping(path = "/download-announcement-records", method = RequestMethod.GET)
    @ResponseBody
    public void downloadAnnouncementRecords(HttpServletRequest request,HttpServletResponse response, @RequestParam(value = "params") String params) {
        log.info("downloadAnnouncementRecords start param:{}",params);
        CfClewAnnouncementParam queryParam = JSONObject.parseObject(params,CfClewAnnouncementParam.class);
        if (StringUtils.isEmpty(queryParam.getStartTime())){
            String todayStart = DateUtil.formatDateTime(DateUtil.parseDate(DateUtil.formatDate(new Date())));
            queryParam.setStartTime(todayStart);
        }
        String misName = request.getParameter("misName");
        if (StringUtils.isEmpty(misName)){
            misName = queryParam.getClewUserId();
        }
        OpResult<List<CfClewAnnouncementRecordsDO>> result = clewAnnouncementService.queryAnnouncementRecordsByParam(queryParam);
        if (result.isSuccess()) {
            String fileName ="公告-"+misName+"-"+DateUtil.formatDateTime(new Date());
            String[] headers = {"公告id", "内容类型", "备注", "时间", "编辑人", "接收人员", "业务类型"};
            List<Object> collections = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(result.getData())) {
                for (CfClewAnnouncementRecordsDO model : result.getData()) {
                    Map<String, Object> s1 = Maps.newHashMap();
                    s1.put(headers[0], model.getId());
                    s1.put(headers[1], CfClewAnnotation.ContentTypeEnum.getDescByCode(model.getContentType()));
                    s1.put(headers[2], model.getRemark());
                    s1.put(headers[3], DateUtil.formatDateTime(model.getCreateTime()));
                    s1.put(headers[4], model.getAnnouncementCreator());
                    s1.put(headers[5], model.getAnnouncementRecipientList());
                    s1.put(headers[6], WorkbenchEnums.WorkbenchTypeEnum.getByType(model.getWorkbenchType()  ).getDesc());
                    collections.add(s1);
                }
            }
            exportLargeExcelClient.writeExcelSmart(ContextUtil.getAdminLongUserId(), SmartExportParam.create(
                    Arrays.stream(headers).map(r -> SmartExportParam.Header.builder().key(r).name(r).build()).collect(Collectors.toList()),
                    collections,
                    fileName, FileType.EXCEL
            ));
//            try {
//                String fileName ="公告-"+misName+"-"+DateUtil.formatDateTime(new Date()) + ".xls";
//                response.setHeader(HttpHeaderNames.CONTENT_DISPOSITION.toString(),
//                        CfClewCommonCons.CONTENT_DISPOSITION_PREFIX_VALUE + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()));
//                OutputStream outputStream = response.getOutputStream();
//                ExcelUtil.exportExcel(headers, collections, outputStream);
//            } catch (IOException e) {
//                log.error(this.getClass().getSimpleName() + "downloadAnnouncementRecords", e);
//            }
        }
    }

    /**
     * 公告 内容类型枚举
     *
     * @return
     */
    @RequestMapping(path = "/get-content-type", produces = "application/json;charset=UTF-8", method = RequestMethod.GET)
    @ResponseBody
    public Response<JSONArray> getContentType() {
        log.info("getContentType start ");
        JSONArray array = new JSONArray();
        for (CfClewAnnotation.ContentTypeEnum questionTypeEnums : CfClewAnnotation.ContentTypeEnum.values()) {
            JSONObject object = new JSONObject();
            object.put("value", questionTypeEnums.getCode());
            object.put("label", questionTypeEnums.getDesc());
            array.add(object);
        }
        return NewResponseUtil.makeSuccess(array);

    }

    /**
     * 根据案例查询服务信息
     * @param queryParam
     * @return
     * todo delete
     */
    @RequestMapping(path = "/query-clewInfo-by-case", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public Response<CFClewInfoModelResult<CFClewInfoModel>> queryClewInfoByCase(@RequestBody TaskStatisticsParam queryParam){
//        if (CollectionUtils.isEmpty(queryParam.getSearchUserIds()) && Objects.isNull(queryParam.getOrgId())){
//            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
//        }
//        //当登录用户是外包且没有选择组织或者人员查询时，需要设置一个默认的orgId
//        if (queryParam.getUserId() != 0 ) {
//            //判断下是否是外包账号
//            Integer outerOrgId = clewUserOrgService.getOuterOrgId(queryParam.getUserId());
//            if (outerOrgId != null) {
//                queryParam.setOrgId(outerOrgId);
//            }
//        }
//        if (CollectionUtils.isEmpty(queryParam.getSearchUserIds())){
//            queryParam.setSearchUserIds(clewUserOrgService.listMisByOrgId(queryParam.getOrgId()));
//        }
//        queryParam.setOffSet();
//        OpResult<CFClewInfoModelResult<CFClewInfoModel>> opResult = cfClewQueryDataFacade.queryClewInfoByCase(queryParam);
//        if(opResult.isFail()){
//            return NewResponseUtil.makeError(opResult.getErrorCode());
//        }
//        return NewResponseUtil.makeSuccess(opResult.getData());
        return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
    }

    /**
     * 根据案例查询服务总数
     * @param queryParam
     * @return
     * todo delete
     */
    @Deprecated
    @RequestMapping(path = "/query-clewInfo-count-by-case", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    @ResponseBody
    public Response<List<JSONObject>> queryClewInfoCountByCase(@RequestBody TaskStatisticsParam queryParam){
//        if (CollectionUtils.isEmpty(queryParam.getSearchUserIds()) && Objects.isNull(queryParam.getOrgId())){
//            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
//        }
//        //当登录用户是外包且没有选择组织或者人员查询时，需要设置一个默认的orgId
//        if (queryParam.getUserId() != 0 ) {
//            //判断下是否是外包账号
//            Integer outerOrgId = clewUserOrgService.getOuterOrgId(queryParam.getUserId());
//            if (outerOrgId != null) {
//                queryParam.setOrgId(outerOrgId);
//            }
//        }
//        if (CollectionUtils.isEmpty(queryParam.getSearchUserIds())){
//            queryParam.setSearchUserIds(clewUserOrgService.listMisByOrgId(queryParam.getOrgId()));
//        }
//        queryParam.setOffSet();
//        OpResult<List<JSONObject>> opResult = cfClewQueryDataFacade.queryClewInfoCountByCase(queryParam);
//        if(opResult.isFail()){
//            return NewResponseUtil.makeError(opResult.getErrorCode());
//        }
//        return NewResponseUtil.makeSuccess(opResult.getData());
        return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
    }


    /**
     * 查询统计
     * @param searchModel
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @PostMapping(path = "/query-task-case-statistics")
    @ResponseBody
    public Response<List<CfTaskCaseStatisticsVO>> queryTaskCaseStatistics(@RequestBody TaskCaseStatisticsSearchModel searchModel){
        int adminUserId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(cfClewQueryDataFacade.queryTaskCaseStatistics(searchModel, adminUserId));
    }

    /**
     * 查询统计
     * @param searchModel
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @PostMapping(path = "/query-task-case-statistics-org")
    @ResponseBody
    public Response<List<CfTaskCaseStatisticsVO>> queryTaskCaseStatisticsOrg(@RequestBody TaskCaseStatisticsSearchModel searchModel){
        int adminUserId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(cfClewQueryDataFacade.queryTaskCaseStatisticsGroupOrg(searchModel, adminUserId));
    }
    /**
     * 查询 统计的案例详情
     * @param searchModel
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @PostMapping(path = "/query-task-case-detail")
    @ResponseBody
    public Response<List<CFClewInfoModel>> queryTaskCaseDetail(@RequestBody TaskCaseStatisticsSearchModel searchModel){
        return NewResponseUtil.makeSuccess(cfClewQueryDataFacade.queryTaskCaseDetail(searchModel));
    }
    /**
     * 查询统计的 分组条件
     * @param dateTime
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @PostMapping(path = "/query-org-for-statistics")
    @ResponseBody
    public Response<List<OptionModel>> queryOrgForStatistics(@RequestParam("dateTime") String dateTime){
        int adminUserId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(cfClewQueryDataFacade.queryOrgForStatistics(dateTime, adminUserId));
    }
    /**
     * 查询统计的 人员条件
     * @param dateTime
     * @return
     */
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    @PostMapping(path = "/query-user-for-statistics")
    @ResponseBody
    public Response<List<OptionModel>> queryUserForStatistics(@RequestParam("dateTime") String dateTime){
        int adminUserId = ContextUtil.getAdminUserId();
        return NewResponseUtil.makeSuccess(cfClewQueryDataFacade.queryUserForStatistics(dateTime, adminUserId));
    }


    @PostMapping("transform-task")
    @ResponseBody
    public Response<Void> transformTask(@RequestParam long taskId, @RequestParam String toUserId) {
        RLock lock = redissonHandler.getLock("cf-clew-transform-task-" + taskId);
        if (lock == null || !lock.tryLock()) {
            return NewResponseUtil.makeFail("请稍后修改");
        }
        try {
            return transformTaskFacade.transformTask(Lists.newArrayList(taskId), toUserId, WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH);
        } catch (Exception e) {
            log.error("转单失败", e);
            return NewResponseUtil.makeFail(e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    @PostMapping("transform-task-batch")
    @ResponseBody
    public Response<Void> transformTaskBatch(@RequestParam List<Long> taskIds, @RequestParam String toUserId) {
        RLock lock = redissonHandler.getLock("cf-clew-transform-task-batch");
        if (lock == null || !lock.tryLock()) {
            return NewResponseUtil.makeFail("请稍后修改");
        }
        if (CollectionUtils.isEmpty(taskIds)) {
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        try {
            return transformTaskFacade.transformTask(taskIds, toUserId, WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH);
        } catch (Exception e) {
            log.error("转单失败", e);
            return NewResponseUtil.makeFail(e.getMessage());
        } finally {
            lock.unlock();
        }
    }


    @PostMapping("judge-recruit-clew")
    @ResponseBody
    public Response<Boolean> judgeRecruitClew(@RequestParam long clewId) {
        //判断是否是招募线索
        boolean hadClew = recruitDelegate.hadClew(clewId);
        return NewResponseUtil.makeSuccess(hadClew);
    }

    @GetMapping("get-fuwu-kanban-data-url")
    @ApiOperation("孔明看板链接获取")
    @ResponseBody
    public Response<String> getFuWuKanBanDataUrl(@RequestParam(required = false, defaultValue = "0", value = "appType") Integer appType) {
        long adminUserId = AuthSaasContext.getAuthSaasUserId();
        Response<AdminUserAccountModel> userAccountInfo = seaAccountServiceDelegate.getValidUserAccountById((int) adminUserId);
        if (userAccountInfo.notOk()) {
            log.info(this.getClass().getSimpleName() + " 获取用户信息失败:{}", userAccountInfo);
            return NewResponseUtil.makeFail(CfClewErrorCode.SYSTEM_ERROR.getMsg());
        }
        AdminUserAccountModel accountInfoData = userAccountInfo.getData();
        ExternalLoginRequest request = new ExternalLoginRequest();
        request.setBiz("cf");
        request.setUsername(accountInfoData.getMis());
        request.setTimestamp(System.currentTimeMillis());
        String requestUrl = "https://kongming.shuiditech.com/page/w01113cae7c404da5a47e16d?ps=iframe";
        // 区分设备类型
        if (appType == 1) {
            requestUrl = "https://kongming.shuiditech.com/m/page/w01113cae7c404da5a47e16d?pref.HostNavOnly=true";
        }
        request.setPageUrl(requestUrl);
        String appKey = "747c6f5f93450208";
        String sign = MD5Util.getMD5HashValue(appKey + request.getBiz() + request.getUsername() + request.getTimestamp() + request.getPageUrl());
        request.setAuthSign(sign);
        com.shuidihuzhu.client.model.Response<GuanDataSSOLoginVO> response = kongmingDataApiClient.externalLogin(request);
        if (Objects.nonNull(response) && Objects.nonNull(response.getData())) {
            return NewResponseUtil.makeSuccess(response.getData().getRedirectUrl());
        }
        return NewResponseUtil.makeSuccess("");
    }

    @ApiOperation("1v1新列表组员查询")
    @PostMapping("my-clew-query-member")
    @ResponseBody
    public Response<CFClewInfoModelResult<CFClewInfoModel>> myClewQueryMember(@RequestBody CfMyClewQueryParam param) {
        // 设置当前登录用户组员只能查询自己
        String userId = ContextUtil.getAll().get(CfClewCommonCons.LOGIN_CLEW_USER_ID).toString();
        if (StringUtils.isBlank(userId)) {
            return NewResponseUtil.makeFail(CfClewErrorCode.UNAUTHORIZED.getMsg());
        }
        param.setIsManager(false);
        param.setServiceUserId(ContextUtil.getAll().get(CfClewCommonCons.LOGIN_CLEW_USER_ID).toString());
        param.setOrgId(null);
        CFClewInfoModelResult<CFClewInfoModel> result = clewQueryFacade.queryMyClew(param);
        return NewResponseUtil.makeSuccess(result);
    }


    @ApiOperation("1v1新列表组长查询")
    @PostMapping("my-clew-query-leader")
    @ResponseBody
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    public Response<CFClewInfoModelResult<CFClewInfoModel>> myClewQueryLeader(@RequestBody CfMyClewQueryParam param) {
        param.setIsManager(true);
        CFClewInfoModelResult<CFClewInfoModel> result = clewQueryFacade.queryMyClew(param);
        return NewResponseUtil.makeSuccess(result);
    }

    @ApiOperation("组长关注功能")
    @PostMapping("leader-follow-clew")
    @ResponseBody
    @RequiresPermission("clewtrack:fuwu_zuzhang")
    public Response<Void> leaderFollowClew(@ApiParam("任务id") @RequestParam Long taskId,
                                           @ApiParam("是否关注 0否1是") @RequestParam Integer isFollow) {
        try {
            clewTaskAttachIndexService.updateIsFollow(taskId, isFollow);
        } catch (Exception e) {
            log.error("leaderFollowClew error", e);
            return NewResponseUtil.makeError(CfClewErrorCode.OPERATOR_FAIL);
        }
        return NewResponseUtil.makeSuccess();
    }


}
