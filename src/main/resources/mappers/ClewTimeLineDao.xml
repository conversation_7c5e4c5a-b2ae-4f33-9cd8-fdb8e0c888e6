<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.ClewTimeLineDao">

    <sql id="tableName">
        clew_time_line
    </sql>
    
    <sql id="selectFields">
        id,
        encrypt_phone,
        clew_id,
        task_id,
        `server_name`,
        comment,
        link,
        happen_time,
        ext_info,
        create_time,
        update_time,
        is_delete    
    </sql>
    

    <!--查询单个-->
    <select id="queryById" resultType="com.shuidihuzhu.cf.clewtrack.domain.ClewTimeLineDO">
        select
          <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and id = #{id}
    </select>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="encryptPhone != null and encryptPhone != ''">
            encrypt_phone,
        </if>
        <if test="clewId != null">
            clew_id,
        </if>
        <if test="taskId != null">
            task_id,
        </if>
        <if test="serverName != null and serverName != ''">
            `server_name`,
        </if>
        <if test="comment != null and comment != ''">
            comment,
        </if>
        <if test="link != null">
            link,
        </if>
        <if test="happenTime != null">
            happen_time,
        </if>
        <if test="extInfo != null and extInfo != ''">
            ext_info,
        </if>
        </trim>
        values 
         <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="encryptPhone != null and encryptPhone != ''">
          #{encryptPhone},
        </if>
        <if test="clewId != null">
          #{clewId},
        </if>
        <if test="taskId != null">
          #{taskId},
        </if>
        <if test="serverName != null and serverName != ''">
          #{serverName},
        </if>
        <if test="comment != null and comment != ''">
          #{comment},
        </if>
        <if test="link != null">
          #{link},
        </if>
        <if test="happenTime != null">
          #{happenTime},
        </if>
        <if test="extInfo != null and extInfo != ''">
          #{extInfo},
        </if>
        </trim>
    </insert>


    <!--通过主键删除-->
    <update id="deleteById">
        update <include refid = "tableName"/>
        <set>
            is_delete = 1
        </set>
        where id = #{id}
    </update>

</mapper>

