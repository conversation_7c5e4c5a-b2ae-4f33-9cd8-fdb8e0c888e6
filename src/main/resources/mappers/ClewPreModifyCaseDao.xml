<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.ClewPreModifyCaseDao">

    <sql id="tableName">
        shuidi_cf_clewtrack.clew_pre_modify_case
    </sql>
    
    <sql id="selectFields">
            id,
        case_id,
        reject_item,
        opt_mis,
        handle_pattern,
        create_time,
        update_time,
        is_delete    
        </sql>
    

    <!--查询单个-->
    <select id="queryById" resultType="com.shuidihuzhu.cf.clewtrack.domain.ClewPreModifyCaseDO">
        select
          <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and id = #{id}
    </select>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="caseId != null">
            case_id,
        </if>
        <if test="rejectItem != null and rejectItem != ''">
            reject_item,
        </if>
        <if test="optMis != null and optMis != ''">
            opt_mis,
        </if>
        <if test="handlePattern != null">
            handle_pattern,
        </if>
        </trim>
        values 
         <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="caseId != null">
          #{caseId},
        </if>
        <if test="rejectItem != null and rejectItem != ''">
          #{rejectItem},
        </if>
        <if test="optMis != null and optMis != ''">
          #{optMis},
        </if>
        <if test="handlePattern != null">
          #{handlePattern},
        </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="updateByCaseId">
        update <include refid = "tableName"/>
        <set>
            <if test="rejectItem != null and rejectItem != ''">
                reject_item = #{rejectItem},
            </if>
            <if test="optMis != null and optMis != ''">
                opt_mis = #{optMis},
            </if>
            <if test="handlePattern != null">
                handle_pattern = #{handlePattern},
            </if>
        </set>
        where case_id = #{caseId}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update <include refid = "tableName"/>
        <set>
            is_delete = 1
        </set>
        where id = #{id}
    </update>


    <select id="listByCaseIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.ClewPreModifyCaseDO">
        select
          <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and case_id
        in
        <foreach collection="caseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>