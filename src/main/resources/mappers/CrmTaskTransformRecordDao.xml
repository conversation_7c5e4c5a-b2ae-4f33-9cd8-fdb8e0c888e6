<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CrmTaskTransformRecordDao">

    <sql id="tableName">
       crm_task_transform_record
    </sql>
    
    <sql id="selectFields">
        id,
        from_task_id,
        to_task_id,
        from_user_id,
        to_user_id,
        create_time,
        update_time,
        is_delete,
        transform_type
    </sql>
    

    <!--查询单个-->
    <select id="queryById" resultType="com.shuidihuzhu.cf.clewtrack.domain.CrmTaskTransformRecordDO">
        select
          <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and id = #{id}
    </select>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="fromTaskId != null">
            from_task_id,
        </if>
        <if test="toTaskId != null">
            to_task_id,
        </if>
        <if test="fromUserId != null and fromUserId != ''">
            from_user_id,
        </if>
        <if test="toUserId != null and toUserId != ''">
            to_user_id,
        </if>
        <if test="transformType != null">
            transform_type,
        </if>
        </trim>
        values 
         <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="fromTaskId != null">
          #{fromTaskId},
        </if>
        <if test="toTaskId != null">
          #{toTaskId},
        </if>
        <if test="fromUserId != null and fromUserId != ''">
          #{fromUserId},
        </if>
        <if test="toUserId != null and toUserId != ''">
          #{toUserId},
        </if>
        <if test="transformType != null">
          #{transformType},
        </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update <include refid = "tableName"/>
        <set>
            <if test="fromTaskId != null">
                from_task_id = #{fromTaskId},
            </if>
            <if test="toTaskId != null">
                to_task_id = #{toTaskId},
            </if>
            <if test="fromUserId != null and fromUserId != ''">
                from_user_id = #{fromUserId},
            </if>
            <if test="toUserId != null and toUserId != ''">
                to_user_id = #{toUserId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update <include refid = "tableName"/>
        <set>
            is_delete = 1
        </set>
        where id = #{id}
    </update>

</mapper>

