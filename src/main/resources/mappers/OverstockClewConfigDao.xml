<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.OverstockClewConfigDao">

    <sql id="tableName">
        overstock_clew_config
    </sql>
    
    <sql id="selectFields">
        id,
        primary_channel,
        start_time,
        end_time,
        create_time,
        update_time,
        is_delete    
    </sql>
    

    <!--查询单个-->
    <select id="queryById" resultType="com.shuidihuzhu.cf.clewtrack.domain.OverstockClewConfigDO">
        select
          <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and id = #{id}
    </select>


    <select id="queryAll" resultType="com.shuidihuzhu.cf.clewtrack.domain.OverstockClewConfigDO">
        select
        <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0
    </select>


    <!--通过主键修改数据-->
    <update id="update">
        update <include refid = "tableName"/>
        <set>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
        </set>
        where id = #{id}
    </update>


</mapper>

