<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewRegisterExtDao">

    <sql id="tableName">
        shuidi_cf_clewtrack.cf_clew_register_ext
    </sql>
    
    <sql id="selectFields">
        id,
        clew_id,
        encrypt_phone,
        city,
        hospital_name,
        patient_name,
        four_patient_id_card,
        create_time,
        update_time,
        is_delete    
    </sql>
    

    <!--查询单个-->
    <select id="queryById" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRegisterExtDO">
        select
          <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and id = #{id}
    </select>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="clewId != null">
            clew_id,
        </if>
        <if test="encryptPhone != null and encryptPhone != ''">
            encrypt_phone,
        </if>
        <if test="city != null and city != ''">
            city,
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            hospital_name,
        </if>
        <if test="patientName != null and patientName != ''">
            patient_name,
        </if>
        <if test="fourPatientIdCard != null and fourPatientIdCard != ''">
            four_patient_id_card,
        </if>
        </trim>
        values 
         <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="clewId != null">
          #{clewId},
        </if>
        <if test="encryptPhone != null and encryptPhone != ''">
          #{encryptPhone},
        </if>
        <if test="city != null and city != ''">
          #{city},
        </if>
        <if test="hospitalName != null and hospitalName != ''">
          #{hospitalName},
        </if>
        <if test="patientName != null and patientName != ''">
          #{patientName},
        </if>
        <if test="fourPatientIdCard != null and fourPatientIdCard != ''">
          #{fourPatientIdCard},
        </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update <include refid = "tableName"/>
        <set>
            <if test="clewId != null">
                clew_id = #{clewId},
            </if>
            <if test="encryptPhone != null and encryptPhone != ''">
                encrypt_phone = #{encryptPhone},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                hospital_name = #{hospitalName},
            </if>
            <if test="patientName != null and patientName != ''">
                patient_name = #{patientName},
            </if>
            <if test="fourPatientIdCard != null and fourPatientIdCard != ''">
                four_patient_id_card = #{fourPatientIdCard},
            </if>
        </set>
        where id = #{id}
    </update>


</mapper>

