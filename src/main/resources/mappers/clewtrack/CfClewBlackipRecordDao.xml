<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewBlackipRecordDao">
    <sql id="fields">
    id,client_ip,channel,account,keyword,create_time,selftag,update_time,is_delete
    </sql>

    <sql id="tableName">cf_clew_blackip_record</sql>
    <insert id="insertClientIp">
        insert into <include refid="tableName"/>
        (client_ip,channel,account,keyword,encrypt_phone,selftag) value (#{clientIp},#{channel},#{account},#{keyword},#{encryptPhone},#{selftag})
    </insert>
</mapper>