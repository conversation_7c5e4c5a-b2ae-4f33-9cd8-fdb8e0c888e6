<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfDonateUserFactorResultDao">
    <sql id="tableName">
        `cf_clew_donate_grade_score_result`
    </sql>

    <sql id="fileds">
        `id`,
        `stat_dt`,
        `emp_id`,
        `entry_time`,
        `emp_level`,
        `work_emp_level`,
        `is_join`,
        `value_json`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statDt != null and statDt != ''">
                `stat_dt`,
            </if>
            <if test="empId != null and empId != ''">
                `emp_id`,
            </if>
            <if test="entryTime != null">
                `entry_time`,
            </if>
            <if test="empLevel != null and empLevel != ''">
                `emp_level`,
            </if>
            <if test="workEmpLevel != null and workEmpLevel != ''">
                `work_emp_level`,
            </if>
            <if test="isJoin != null">
                `is_join`,
            </if>
            <if test="valueJson != null and valueJson != ''">
                `value_json`,
            </if>

        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statDt != null and statDt != ''">
                #{statDt},
            </if>
            <if test="empId != null and empId != ''">
                #{empId},
            </if>
            <if test="entryTime != null">
                #{entryTime},
            </if>
            <if test="empLevel != null and empLevel != ''">
                #{empLevel},
            </if>
            <if test="workEmpLevel != null and workEmpLevel != ''">
                #{workEmpLevel},
            </if>
            <if test="isJoin != null">
                #{isJoin},
            </if>
            <if test="valueJson != null and valueJson != ''">
                #{valueJson},
            </if>
        </trim>
    </insert>

    <select id="getLatestDate" resultType="java.lang.String">
        select MAX(stat_dt)
        from <include refid="tableName"/>
        where is_delete = 0
    </select>

    <select id="getDonateUserFactorResultListByDate" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateUserFactorResultDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and stat_dt = #{date}
    </select>

    <select id="getUserFactorResultByUserIdAndStatDt" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateUserFactorResultDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and stat_dt = #{statDt}
        and emp_id = #{userId}
        limit 1
    </select>


</mapper>
