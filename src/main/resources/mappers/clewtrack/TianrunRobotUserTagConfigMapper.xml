<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.TianrunRobotUserTagConfigDao">
    <sql id="tableName">tianrun_robot_user_tag_config</sql>

    <sql id="fields">
        `id`,
        `tag_name`,
        `tag_type`,
        `owner`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <select id="listRobotUserTag" resultType="com.shuidihuzhu.cf.clewtrack.domain.TianrunRobotUserTagConfigDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and tag_type = #{tagType}
    </select>

    <select id="listAllRobotUserTag" resultType="com.shuidihuzhu.cf.clewtrack.domain.TianrunRobotUserTagConfigDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
    </select>

</mapper>
