<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfAiDonateTaskLimitConfigDao">
    <sql id="tableName">
        `cf_ai_donate_task_limit_config`
    </sql>
    <sql id="fileds">
        `id`,
        `ai_tag_config`,
        `ai_period`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insertCfAiDonateTaskLimitConfig">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiTagConfig != null and aiTagConfig != ''">
                ai_tag_config,
            </if>
            <if test="aiPeriod != null and aiPeriod != ''">
                ai_period,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiTagConfig != null and aiTagConfig != ''">
                #{aiTagConfig},
            </if>
            <if test="aiPeriod != null and aiPeriod != ''">
                #{aiPeriod},
            </if>
        </trim>
    </insert>

    <update id="updateCfAiDonateTaskLimitConfig">
        update
        <include refid="tableName"/>
        <set>
            <if test="aiTagConfig != null and aiTagConfig != ''">
                ai_tag_config = #{aiTagConfig},
            </if>
            <if test="aiPeriod != null and aiPeriod != ''">
                ai_period = #{aiPeriod},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getCfAiDonateTaskLimitConfig" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfAiDonateTaskLimitConfigDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where is_delete = 0
        limit 1
    </select>

</mapper>
