<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfForecastCallBaseDataDao">

    <sql id="tableName">cf_forecast_call_base_data</sql>

    <sql id="baseResult">
        `id`,
	    `clew_id`,
	    `clew_task_id`,
	    `encrypt_phone`,
	    `forecast_task_id`,
	    `forecast_task_type`,
	    `is_import`,
	    `user_tag`,
	    `redial_num`,
	    `primary_channel`,
        `priority`,
        `create_time`,
        `update_time`,
	    `is_delete`
    </sql>

    <sql id="BATCH_INSERT_FIELDS">
	    `clew_id`,
	    `clew_task_id`,
        `encrypt_phone`
    </sql>


    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="clewId!=null">`clew_id`,</if>
            <if test="clewTaskId != null ">`clew_task_id`,</if>
            <if test="encryptPhone != null and encryptPhone != ''">`encrypt_phone`,</if>
            <if test="forecastTaskId != null">`forecast_task_id`,</if>
            <if test="forecastTaskType != null">`forecast_task_type`,</if>
            <if test="isImport != null">`is_import`,</if>
            <if test="userTag != null and userTag != ''">`user_tag`,</if>
            <if test="primaryChannel != null and primaryChannel != ''">`primary_channel`,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="clewId!=null">#{clewId} ,</if>
            <if test="clewTaskId != null ">#{clewTaskId} ,</if>
            <if test="encryptPhone != null and encryptPhone != ''">#{encryptPhone} ,</if>
            <if test="forecastTaskId != null">#{forecastTaskId} ,</if>
            <if test="forecastTaskType != null">#{forecastTaskType} ,</if>
            <if test="isImport != null">#{isImport} ,</if>
            <if test="userTag != null and userTag != ''">#{userTag},</if>
            <if test="primaryChannel != null and primaryChannel != ''">#{primaryChannel},</if>
        </trim>
    </insert>


    <insert id="batchAddOverstock">
        insert into <include refid="tableName"/>
            (`clew_id`, `clew_task_id`, `encrypt_phone`,`primary_channel`, `priority`)
        values
        <foreach collection="overstockClewTasks" item="overstockTask" separator=",">
            (#{overstockTask.clewId},#{overstockTask.clewTaskId},#{overstockTask.encryptPhone},
            #{overstockTask.primaryChannel}, #{overstockTask.priority})
        </foreach>
    </insert>

    <update id="batchUpdateImport">
        update <include refid="tableName"/>
        <set>
            forecast_task_id = #{forecastTaskId} ,
            forecast_task_type = #{forecastTaskType} ,
            is_import = 1
        </set>
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateUserTagByClewId">
        update <include refid="tableName"/>
        <set>
            `user_tag` = #{userTag}
        </set>
        where clew_id = #{clewId}
    </update>

    <select id="getRealTimePredictiveCallPhone" resultType="com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel">
        select id as recordId, encrypt_phone as encrypt_phone, clew_id as clewId, clew_task_id as taskId
        from <include refid="tableName"/>
        where is_delete = 0
        and is_import = 0
        and redial_num = 0
        and create_time >= #{startTime}
        order by id desc
        limit #{importPhoneNums}
    </select>

    <select id="getLatelyRecordByEncryptPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallBaseDataDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete = 0 and encrypt_phone = #{encryptPhone}
        order by id desc limit 1
    </select>

    <select id="getBaseDataById" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallBaseDataDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete = 0 and id = #{id}
    </select>

    <update id="updateRedialNumById">
        update <include refid="tableName"/>
        <set>
            `is_import` = 0,
            `redial_num` = `redial_num`+1
        </set>
        where id = #{id}
    </update>

    <select id="getRedialPredictiveCallPhone" resultType="com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel">
        select id as recordId, encrypt_phone as encrypt_phone, clew_id as clewId, clew_task_id as taskId
        from <include refid="tableName"/>
        where is_delete = 0
        and is_import = 0
        and redial_num between 1 and #{robotcallMaxRedialNum}
        and create_time >= #{startTime}
        order by redial_num,create_time desc
        limit #{importPhoneNums}
    </select>

    <select id="getPredictCallPhoneWithChannel" resultType="com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel">
        select id as recordId, encrypt_phone as encrypt_phone, clew_id as clewId, clew_task_id as taskId,
               priority as priority
        from <include refid="tableName"/>
        where is_delete = 0
        and is_import = 0
        and redial_num = 0
        and primary_channel = #{primaryChannel}
        and create_time >= #{startTime}
        order by create_time desc
        limit #{importPhoneNums}
    </select>

    <select id="getRedialPredictCallPhoneWithChannel"
            resultType="com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel">
        select id as recordId, encrypt_phone as encrypt_phone, clew_id as clewId, clew_task_id as taskId
        from <include refid="tableName"/>
        where is_delete = 0
        and is_import = 0
        and primary_channel = #{primaryChannel}
        and redial_num between 1 and #{maxRedialNum}
        and create_time >= #{startTime}
        order by redial_num,create_time desc
        limit #{importPhoneNums}
    </select>

    <select id="listClueWithTime" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallBaseDataDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete = 0 and create_time between #{startDate} and #{endDate}
    </select>

    <update id="updatePrimaryChannel">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="primary_channel =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.primaryChannel !=null">
                        when id=#{item.id} then #{item.primaryChannel}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="items" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>
