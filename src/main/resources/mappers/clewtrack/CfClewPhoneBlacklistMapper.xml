<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewPhoneBlacklistDao">

    <sql id="TABLE">cf_clew_phone_blacklist</sql>
    <sql id="FILED">
        id,encrypt_phone,create_time,update_time,is_delete
    </sql>
    <insert id="insertEncryptPhoneBatch" parameterType="java.lang.String">
        insert into <include refid="TABLE"/>
        (encrypt_phone)
        values
        <foreach collection="encryptPhones" item="encryptPhone" separator="," >
            (#{encryptPhone})
        </foreach>
    </insert>
    <select id="getBlacklistByEncryptPhone" resultType="java.lang.Integer">
        select count(1)
        from <include refid="TABLE"/>
        where encrypt_phone = #{encryptPhone}  and is_delete = 0
    </select>
    <update id="deleteEncryptPhoneFromBlacklist">
        update <include refid="TABLE"/> set is_delete=1 where encrypt_phone in
        <foreach collection="encryptPhones" item="encryptPhone" open="(" separator="," close=")">
            #{encryptPhone}
        </foreach>
    </update>
</mapper>
