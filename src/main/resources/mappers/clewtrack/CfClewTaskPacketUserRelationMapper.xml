<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTaskPacketUserRelationDao">


    <sql id="baseResult">
        id,create_time,task_type,packet_id,user_id
    </sql>
    <sql id="tableName">cf_clew_task_packet_user_relation</sql>

    <select id="getRelationListByUserId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskPakcetUserRelationDO">
      select
        pur.id as id,pur.create_time as create_time,pur.task_type as task_type,
        pur.packet_id as packet_id,pur.user_id as user_id,pm.member_role as member_role
      from <include refid="tableName"/> as pur
      join cf_clew_packet_member pm on pur.user_id=pm.user_id and pur.packet_id=pm.packet_id
      join cf_clew_track_user_info tui on pm.cf_clew_tracker_id=tui.id
      where pur.user_id in
        <foreach collection="useridList" item="userid" open="(" separator="," close=")">
            #{userid}
        </foreach>
      and tui.tracker_type = #{trackUserType}
    </select>
    <select id="getRelationListByUserIdAndTypeAndPacket"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskPakcetUserRelationDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_type=#{taskType} and user_id=#{userId} and packet_id=#{packetId}
        limit 1
    </select>
</mapper>
