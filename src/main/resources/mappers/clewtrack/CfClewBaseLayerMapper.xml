<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseLayerDao">
    <sql id="tableName">cf_clew_base_layer</sql>
    <sql id="field">
        `id`,
        `clew_layer_name`,
        `is_award`,
        `last_operator`,
        `is_show`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <select id="getAwardClewlayerList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0 and is_show = 1 and is_award = 1
    </select>

    <select id="queryClewlayer" resultType="com.shuidihuzhu.cf.clewtrack.model.vo.ClewBaseLayerVO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_show=1 and is_delete = 0
    </select>

    <update id="updateClewlayerById">
        update <include refid="tableName"/>
        <set>
            <if test="isAward != null ">
                is_award = #{isAward} ,
            </if>
            <if test="lastOperator != null and lastOperator != ''">
                last_operator = #{lastOperator} ,
            </if>
            <if test="isShow != null ">
                is_show = #{isShow},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getCfClewBaseLayerDOById" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where id = #{id} and is_delete =0
    </select>

    <select id="getAllClewlayerVOList"
            resultType="com.shuidihuzhu.cf.clewtrack.model.vo.ClewBaseLayerVO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0
    </select>

</mapper>
