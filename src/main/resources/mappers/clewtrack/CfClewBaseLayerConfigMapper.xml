<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseLayerConfigDao">
    <sql id="tableName">cf_clew_base_layer_config</sql>
    <sql id="field">
        `id`,
        `clew_layer_id`,
        `clew_type`,
        `channels`,
        `disease`,
        `age_min`,
        `age_max`,
        `clew_score_min`,
        `clew_score_max`,
        `registe_start_int`,
        `registe_end_int`,
        `clew_registe_start_time`,
        `clew_registe_end_time`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <select id="getClewlayerConfigList"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerConfigDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0
        and clew_layer_id in <foreach collection="clewLayerIdList" item="clewLayerId" open="(" separator="," close=")">#{clewLayerId}</foreach>
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerConfigDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="clewLayerId !=null">
                clew_layer_id,
            </if>
            <if test="clewType != null ">
                clew_type,
            </if>
            <if test="channels!=null and channels!=''">
                channels,
            </if>
            <if test="disease!=null and disease!=''">
                disease,
            </if>
            <if test="ageMin!=null" >
                age_min,
            </if>
            <if test="ageMax != null">
                age_max,
            </if>
            <if test="clewScoreMin != null ">
                clew_score_min,
            </if>
            <if test="clewScoreMax != null ">
                clew_score_max,
            </if>
            <if test="registeStartInt != null">
                registe_start_int,
            </if>
            <if test="registeEndInt != null">
                registe_end_int,
            </if>
            <if test="clewRegisteStartTime != null and clewRegisteStartTime != ''">
                clew_registe_start_time,
            </if>
            <if test="clewRegisteEndTime != null and clewRegisteEndTime != ''">
                clew_registe_end_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="clewLayerId !=null">
                #{clewLayerId} ,
            </if>
            <if test="clewType != null ">
                #{clewType} ,
            </if>
            <if test="channels!=null and channels!=''">
                #{channels} ,
            </if>
            <if test="disease!=null and disease!=''">
                #{disease} ,
            </if>
            <if test="ageMin!=null" >
                #{ageMin} ,
            </if>
            <if test="ageMax != null">
                #{ageMax} ,
            </if>
            <if test="clewScoreMin != null ">
                #{clewScoreMin} ,
            </if>
            <if test="clewScoreMax != null ">
                #{clewScoreMax} ,
            </if>
            <if test="registeStartInt != null">
                #{registeStartInt} ,
            </if>
            <if test="registeEndInt != null">
                #{registeEndInt} ,
            </if>
            <if test="clewRegisteStartTime != null and clewRegisteStartTime != ''">
                #{clewRegisteStartTime} ,
            </if>
            <if test="clewRegisteEndTime != null and clewRegisteEndTime != ''">
                #{clewRegisteEndTime} ,
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerConfigDO">
        update <include refid="tableName"/>
        <set>
            <if test="clewLayerId !=null">
                clew_layer_id = #{clewLayerId} ,
            </if>
            <if test="clewType != null ">
                clew_type = #{clewType} ,
            </if>
            <if test="channels!=null">
                channels = #{channels} ,
            </if>
            <if test="disease!=null">
                disease = #{disease} ,
            </if>
            <if test="ageMin!=null" >
                age_min = #{ageMin} ,
            </if>
            <if test="ageMax != null">
                age_max = #{ageMax} ,
            </if>
            <if test="clewScoreMin != null ">
                clew_score_min = #{clewScoreMin} ,
            </if>
            <if test="clewScoreMax != null ">
                clew_score_max = #{clewScoreMax} ,
            </if>
            <if test="registeStartInt != null">
                registe_start_int = #{registeStartInt} ,
            </if>
            <if test="registeEndInt != null">
                registe_end_int = #{registeEndInt} ,
            </if>
            <if test="clewRegisteStartTime != null">
                clew_registe_start_time = #{clewRegisteStartTime} ,
            </if>
            <if test="clewRegisteEndTime != null">
                clew_registe_end_time = #{clewRegisteEndTime} ,
            </if>
        </set>
        where id =#{id}
    </update>

    <update id="deleteByClewLayerId">
        update <include refid="tableName"/>
        <set>
            is_delete = 1
        </set>
        where clew_layer_id = #{clewLayerId}
    </update>

    <update id="deleteById">
        update <include refid="tableName"/>
        <set>
            is_delete = 1
        </set>
        where id = #{id}
    </update>

    <select id="queryLayerconfig" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerConfigDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0 and clew_layer_id = #{configVO.clewLayerId}
    </select>

    <select id="queryAllLayerconfig" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerConfigDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0
    </select>
    <select id="getClewlayerConfig" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerConfigDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0 and id = #{id}
    </select>


</mapper>
