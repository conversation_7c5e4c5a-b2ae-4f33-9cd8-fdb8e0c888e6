<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewAttachInfoDao">

    <sql id="baseResult">
        `id`,
        `clew_id`,
        `create_time`,
        `update_time`,
        `user_id`,
        `user_name`,
        `attach_member_type`,
        `task_type`,
        `clew_assgin_time`,
        `request_status`,
        `attach_status`,
        `is_delete`,
        `wechat_valid`,
        `wechat_pass`,
        `user_flag`,
        `first_tag`,
        `second_tag`,
        `wechat_reject_reason`,
        `notcirculate_reason`,
        `service_status`,
        `fuwu_remark`,
        `trans_work_content_type`,
        `encrypt_wechat_id`,
        `age`,
        `physical`,
        `independent_walk`,
        `treatment_stage`,
        `patient_city`,
        `recruit_attitude`
    </sql>
    <sql id="bdCrmRequestStatusModel">
        `clew_id`,
        `request_status`
    </sql>
    <sql id="tableName">cf_clew_attach_info</sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clewId!=null">
                clew_id,
            </if>
            <if test="userId!=null">
                user_id,
            </if>
            <if test="userName!=null and userName!=''">
                user_name,
            </if>
            <if test="attachMemberType!=null">
                attach_member_type,
            </if>
            <if test="taskType!=null">
                task_type,
            </if>
            <if test="clewAssginTime!=null">
                clew_assgin_time,
            </if>
            <if test="requestStatus!=null">
                request_status,
            </if>
            <if test="attachStatus!=null">
                attach_status,
            </if>
            <if test="encryptWechatId!=null and encryptWechatId!=''">
                encrypt_wechat_id,
            </if>
            <if test="wechatValid!=null">
                wechat_valid,
            </if>
            <if test="wechatPass!=null">
                wechat_pass,
            </if>
            <if test="userFlag!=null">
                user_flag,
            </if>
            <if test="serviceStatus!=null">
                service_status,
            </if>
            <if test="firstTag!=null">
                first_tag,
            </if>
            <if test="secondTag!=null">
                second_tag,
            </if>
            <if test="wechatRejectReason!=null">
                wechat_reject_reason,
            </if>
            <if test="notcirculateReason!=null">
                notcirculate_reason,
            </if>
            <if test="fuwuRemark!=null and fuwuRemark!=''">
                fuwu_remark,
            </if>
            <if test="transWorkContentType != null">
                trans_work_content_type,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="physical != null and physical!=''">
                physical,
            </if>
            <if test="independentWalk != null">
                independent_walk,
            </if>
            <if test="treatmentStage != null">
                treatment_stage,
            </if>
            <if test="patientCity != null and patientCity!=''">
                patient_city,
            </if>
            <if test="recruitAttitude != null">
                recruit_attitude,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clewId!=null">
                #{clewId},
            </if>
            <if test="userId!=null">
                #{userId},
            </if>
            <if test="userName!=null and userName!=''">
                #{userName},
            </if>
            <if test="attachMemberType!=null">
                #{attachMemberType},
            </if>
            <if test="taskType!=null">
                #{taskType},
            </if>
            <if test="clewAssginTime!=null">
                #{clewAssginTime},
            </if>
            <if test="requestStatus!=null">
                #{requestStatus},
            </if>
            <if test="attachStatus!=null">
                #{attachStatus},
            </if>
            <if test="wechatId!=null and wechatId!=''">
                #{wechatId},
            </if>
            <if test="encryptWechatId!=null and encryptWechatId!=''">
                #{encryptWechatId},
            </if>
            <if test="wechatValid!=null">
                #{wechatValid},
            </if>
            <if test="wechatPass!=null">
                #{wechatPass},
            </if>
            <if test="userFlag!=null">
                #{userFlag},
            </if>
            <if test="serviceStatus!=null">
                #{serviceStatus},
            </if>
            <if test="firstTag!=null">
                #{firstTag},
            </if>
            <if test="secondTag!=null">
                #{secondTag},
            </if>
            <if test="wechatRejectReason!=null">
                #{wechatRejectReason},
            </if>
            <if test="notcirculateReason!=null">
                #{notcirculateReason},
            </if>
            <if test="fuwuRemark!=null and fuwuRemark!=''">
                #{fuwuRemark},
            </if>
            <if test="transWorkContentType != null">
                #{transWorkContentType}  ,
            </if>
            <if test="age != null">
                #{age},
            </if>
            <if test="physical != null and physical!=''">
                #{physical},
            </if>
            <if test="independentWalk != null">
                #{independentWalk},
            </if>
            <if test="treatmentStage != null">
                #{treatmentStage},
            </if>
            <if test="patientCity != null and patientCity!=''">
                #{patientCity},
            </if>
            <if test="recruitAttitude != null">
                #{recruitAttitude},
            </if>
        </trim>
    </insert>
    <update id="updateClewAttachInfo"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
    update <include refid="tableName"/>
        <set>
          <if test="userId != null">
              user_id=#{userId},
          </if>
          <if test="userName != null">
              user_name=#{userName},
          </if>
          <if test="attachMemberType != null">
              attach_member_type = #{attachMemberType},
          </if>
          <if test="taskType != null">
              task_type = #{taskType},
          </if>
          <if test="clewAssginTime != null">
              clew_assgin_time = #{clewAssginTime},
          </if>
          <if test="requestStatus != null" >
              request_status = #{requestStatus},
          </if>
          <if test="attachStatus != null">
              attach_status = #{attachStatus},
          </if>
            <if test="encryptWechatId!=null">
                encrypt_wechat_id=#{encryptWechatId},
            </if>
            <if test="wechatValid != null">
                wechat_valid = #{wechatValid},
            </if>
            <if test="wechatPass != null">
                wechat_pass = #{wechatPass},
            </if>
            <if test="userFlag != null">
                user_flag = #{userFlag},
            </if>
            <if test="serviceStatus!=null">
                service_status = #{serviceStatus},
            </if>
            <if test="firstTag!=null">
                first_tag = #{firstTag},
            </if>
            <if test="secondTag!=null">
                second_tag = #{secondTag},
            </if>
            <if test="wechatRejectReason!=null">
                wechat_reject_reason = #{wechatRejectReason},
            </if>
            <if test="notcirculateReason!=null">
                notcirculate_reason = #{notcirculateReason},
            </if>
            <if test="fuwuRemark!=null and fuwuRemark!=''">
                fuwu_remark = #{fuwuRemark},
            </if>
            <if test="transWorkContentType!=null">
                trans_work_content_type = #{transWorkContentType} ,
            </if>
            <if test="age != null">
                age= #{age},
            </if>
            <if test="physical != null">
                physical = #{physical},
            </if>
            <if test="independentWalk != null">
                independent_walk = #{independentWalk},
            </if>
            <if test="treatmentStage != null">
                treatment_stage = #{treatmentStage},
            </if>
            <if test="patientCity != null and patientCity!=''">
                patient_city = #{patientCity},
            </if>
            <if test="recruitAttitude != null">
                recruit_attitude = #{recruitAttitude},
            </if>
        </set>
     where
          id=#{id}
    </update>

    <select id="getAttachByIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by create_time desc
    </select>
    <select id="getAttachInfoIdByUserIdAndCLewId" resultType="java.lang.Long">
        select max(id) from <include refid="tableName"/>
        where
        1=1
        <if test="userIds!=null and userIds.size()>0">
            and user_id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        and clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and task_type in
        <foreach collection="taskTypeList" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
        group by clew_id
    </select>
    <select id="getAttachById"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where  id = #{id}
    </select>
    <select id="getAttachByUserIdAndClewIdAndUserType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
    select <include refid="baseResult"/>
    from <include refid="tableName"/>
    where clew_id=#{clewId} and attach_member_type=#{userType} and task_type=#{taskType}
    order by create_time desc
    limit 1;
    </select>
    <select id="getRequestStatusByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and task_type in
        <foreach collection="taskTypeList" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
    </select>
    <select id="getAttachByClewIdAndTaskType" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where clew_id=#{clewId}
        and task_type=#{taskType}
        limit 1
    </select>

    <select id="getAttachIdByTagId" resultType="java.lang.Long">
        select id from
        <include refid="tableName"/>
        where first_tag = #{firstTagId}
        and second_tag = #{secondTagId}
        order by id desc
        limit 1
    </select>

    <select id="getAttachIdByTagMaxId" resultType="java.lang.Long">
        select max(id) from
        <include refid="tableName"/>
        where first_tag = #{firstTagId}
    </select>

    <select id="getAttachByClewIdsAndTaskType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where
        clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and task_type = #{type}
    </select>
    <select id="getBdCrmRequestStatusModelByClewIdsAndTaskTypeAndRequestStatus"
            resultType="com.shuidihuzhu.cf.clewtrack.model.bdcrm.BdCrmRequestStatusModel">
        select <include refid="bdCrmRequestStatusModel"/>
        from <include refid="tableName"/>
        where
        clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and task_type = #{taskType}
        <if test="requestStatus!=null">
            and request_status = #{requestStatus}
        </if>
    </select>
    <select id="getAttachByClewIdAndTaskTypeFromMaster"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where clew_id=#{clewId}
        and task_type=#{taskType}
        limit 1
    </select>
    <select id="getAttachByClewIdAndTaskTypeFromMasterByClewIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </select>

    <select id="getUserCurrentWxTransferClewByCurrentDate"
            resultType="com.shuidihuzhu.cf.clewtrack.model.CFUserClewmanageModel">
        SELECT
        user_id as clewUserId,
        SUM(1) as wxTranferClew
        from <include refid="tableName"/>
        where create_time >#{currentDate} and task_type=1 and trans_work_content_type != -1
        <if test="firstTags!=null and firstTags.size()>0">
            and `first_tag` in
            <foreach collection="firstTags" item="firstTag" open="(" close=")" separator=",">
                #{firstTag}
            </foreach>
        </if>
        <if test="userIds!=null and userIds.size()>0">
            and user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        GROUP BY user_id
    </select>

    <update id="updateClewAttachInfoWithTransWorkContentType">
        update <include refid="tableName"/>
        <set>
            <if test="transWorkContentType!=null">
                trans_work_content_type = #{transWorkContentType} ,
            </if>
        </set>
        where
        id=#{id}
    </update>

    <select id="getCfClewAttachInfoDOByClewIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </select>
</mapper>
