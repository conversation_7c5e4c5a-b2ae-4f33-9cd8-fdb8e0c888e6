<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfBindServiceInfoDao">

    <sql id="baseResult">
        `id`,
        `user_id`,
        `bound_user_ids`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <sql id="tableName">cf_bind_service_info</sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfBindServiceInfoDo"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="boundUserIds != null and boundUserIds != ''">
                bound_user_ids,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete!=null">
                is_delete,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                #{userId} ,
            </if>
            <if test="boundUserIds != null and boundUserIds != ''">
                #{boundUserIds},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDelete!=null">
                #{isDelete},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfBindServiceInfoDo">
        update <include refid="tableName"/>
        <set>
            <if test="boundUserIds != null and boundUserIds != ''">
                bound_user_ids=#{boundUserIds},
            </if>
            <if test="isDelete != null ">
                is_delete=#{isDelete},
            </if>
        </set>
        where id=#{id}
    </update>

    <select id="getCfBindServiceInfoByUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfBindServiceInfoDo">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and user_id = #{userId}
    </select>
</mapper>