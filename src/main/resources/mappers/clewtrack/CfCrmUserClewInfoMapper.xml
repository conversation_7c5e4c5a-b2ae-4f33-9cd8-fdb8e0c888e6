<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfCrmUserClewInfoDao">
    <sql id="tableName">cf_crm_user_clew_info</sql>
    <sql id="fields">
      id,
      create_time,
      update_time,
      is_delete,
      in_call_count,
      out_call_count,
      latest_in_call_time,
      latest_in_call_ask_type,
      phone,
      second_phone,
      register_count,
      case_status,
      person_id,
      uuid,
      latest_register_time,
      source_type,
      province,
      city,
      user_name
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfCrmUserClewInfoDO" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="createTime!=null">
                create_time,
            </if>
            <if test="inCallCount!=null">
                in_call_count,
            </if>
            <if test="outCallCount!=null">
                out_call_count,
            </if>
            <if test="latestInCallTime!=null">
                latest_in_call_time,
            </if>
            <if test="latestInCallAskType!=null">
                latest_in_call_ask_type,
            </if>
            <if test="phone!=null">
                phone,
            </if>
            <if test="secondPhone!=null">
                second_phone,
            </if>
            <if test="registerCount!=null">
                register_count,
            </if>
            <if test="caseStatus!=null">
                case_status,
            </if>
            <if test="personId!=null">
                person_id,
            </if>
            <if test="uuid!=null">
                uuid,
            </if>
            <if test="latestRegisterTime!=null">
                latest_register_time,
            </if>
            <if test="sourceType!=null">
                source_type,
            </if>
            <if test="province!=null">
                province,
            </if>
            <if test="city!=null">
                city,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="createTime!=null">
                #{createTime},
            </if>
            <if test="inCallCount!=null">
                #{inCallCount},
            </if>
            <if test="outCallCount!=null">
                #{outCallCount},
            </if>
            <if test="latestInCallTime!=null">
                #{latestInCallTime},
            </if>
            <if test="latestInCallAskType!=null">
                #{latestInCallAskType},
            </if>
            <if test="phone!=null">
                #{phone},
            </if>
            <if test="secondPhone!=null">
                #{secondPhone},
            </if>
            <if test="registerCount!=null">
                #{registerCount},
            </if>
            <if test="caseStatus!=null">
                #{caseStatus},
            </if>
            <if test="personId!=null">
                #{personId},
            </if>
            <if test="uuid!=null">
                #{uuid},
            </if>
            <if test="latestRegisterTime!=null">
                #{latestRegisterTime},
            </if>
            <if test="sourceType!=null">
                #{sourceType},
            </if>
            <if test="province!=null">
                #{province},
            </if>
            <if test="city!=null">
                #{city},
            </if>
        </trim>
    </insert>
    <update id="updateCaseStatusByPersonId">
        update <include refid="tableName"/>
        set case_status=#{caseStatus}
        where person_id=#{personId}
    </update>
    <update id="updateUuidByPersonId">
        update <include refid="tableName"/>
        set uuid=#{uuid}
        where person_id=#{personId}
    </update>
    <update id="updateByCfCrmUserClewInfoDO">
        update <include refid="tableName"/>
        <set>
            <if test="inCallCount!=null">
                in_call_count=#{inCallCount},
            </if>
            <if test="outCallCount!=null">
                out_call_count=#{outCallCount},
            </if>
            <if test="latestInCallTime!=null">
                latest_in_call_time=#{latestInCallTime},
            </if>
            <if test="latestInCallAskType!=null">
                latest_in_call_ask_type=#{latestInCallAskType},
            </if>
            <if test="secondPhone!=null and secondPhone!=''">
                second_phone=#{secondPhone},
            </if>
            <if test="registerCount!=null">
                register_count=#{registerCount},
            </if>
            <if test="caseStatus!=null">
                case_status=#{caseStatus},
            </if>
            <if test="personId!=null and personId!=''">
                person_id=#{personId},
            </if>
            <if test="uuid!=null and uuid!=''">
                uuid=#{uuid},
            </if>
            <if test="latestRegisterTime!=null">
                latest_register_time=#{latestRegisterTime},
            </if>
            <if test="sourceType!=null">
                source_type=#{sourceType},
            </if>
            <if test="province!=null">
                province=#{province},
            </if>
            <if test="city!=null">
                city=#{city},
            </if>
        </set>
        where id=#{id}
    </update>
    <select id="getCrmUserClewInfoByPersonId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCrmUserClewInfoDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where person_id=#{personId}
        order by id desc limit 1
    </select>
    <select id="incrRegisterCount" resultType="java.lang.Integer">
        update <include refid="tableName"/>
        set register_count=register_count+1
        where id=#{id}
    </select>
    <select id="getCrmUserClewInfoByUuid" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCrmUserClewInfoDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where uuid=#{uuid}
    </select>
    <select id="getCrmClewUserInfoByPersonIdAndUuid"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCrmUserClewInfoDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where uuid=#{uuid} and person_id=#{personId}
    </select>

    <update id="updateNameByUuid">
        update <include refid="tableName"/>
        set user_name=#{name}
        where uuid=#{uuid}
    </update>

    <update id="updateNameByPersonId">
        update <include refid="tableName"/>
        set user_name=#{name}
        where person_id=#{personId}
    </update>
    <select id="getCrmUserInfosByUuIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCrmUserClewInfoDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and uuid in
        <foreach collection="uuidList" item="uuid" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </select>

    <select id="getCrmUserInfosByPersonIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCrmUserClewInfoDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and person_id in
        <foreach collection="personIdList" item="personId" open="(" separator="," close=")">
            #{personId}
        </foreach>
    </select>
</mapper>