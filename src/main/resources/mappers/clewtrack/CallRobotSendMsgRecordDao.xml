<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CallRobotSendMsgRecordDao">

    <sql id="tableName">
        call_robot_send_msg_record
    </sql>
    
    <sql id="selectFields">
        id,
        robot_uuid,
        case_id,
        encrypt_phone,
        call_task_status,
        model_num,
        msg_send_code,
        create_time,
        update_time,
        is_delete,
        hit_recruit,
        recruit_model_num,
        recruit_msg_send_code,
        scene
    </sql>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="robotUuid != null and robotUuid != ''">
            robot_uuid,
        </if>
        <if test="caseId != null">
            case_id,
        </if>
        <if test="encryptPhone != null and encryptPhone != ''">
            encrypt_phone,
        </if>
        <if test="hitRecruit != null">
            hit_recruit,
        </if>
        <if test="scene != null">
            scene,
        </if>
        </trim>
        values 
         <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="robotUuid != null and robotUuid != ''">
          #{robotUuid},
        </if>
        <if test="caseId != null">
          #{caseId},
        </if>
        <if test="encryptPhone != null and encryptPhone != ''">
          #{encryptPhone},
        </if>
        <if test="hitRecruit != null">
          #{hitRecruit},
        </if>
         <if test="scene != null">
          #{scene},
         </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update <include refid = "tableName"/>
        <set>
            <if test="robotUuid != null and robotUuid != ''">
                robot_uuid = #{robotUuid},
            </if>
            <if test="caseId != null">
                case_id = #{caseId},
            </if>
            <if test="encryptPhone != null and encryptPhone != ''">
                encrypt_phone = #{encryptPhone},
            </if>
            <if test="callTaskStatus != null">
                call_task_status = #{callTaskStatus},
            </if>
            <if test="modelNum != null and modelNum != ''">
                model_num = #{modelNum},
            </if>
            <if test="msgSendCode != null and msgSendCode != ''">
                msg_send_code = #{msgSendCode},
            </if>
        </set>
        where id = #{id}
    </update>


    <select id="queryByRecordUuid" resultType="com.shuidihuzhu.cf.clewtrack.domain.CallRobotSendMsgRecordDO">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `robot_uuid` = #{recordUuid} limit 1
    </select>


    <update id="updateCallTaskStatus">
        update <include refid = "tableName"/>
        <set>
            <if test="callTaskStatus != null">
                call_task_status = #{callTaskStatus},
            </if>
        </set>
        where robot_uuid = #{recordUuid}
    </update>

    <update id="updateMsgSend">
        update <include refid = "tableName"/>
        <set>
            <if test="modelNum != null and modelNum != ''">
                model_num = #{modelNum},
            </if>
            <if test="msgSendCode != null and msgSendCode != ''">
                msg_send_code = #{msgSendCode},
            </if>
            <if test="recruitModelNum != null and recruitModelNum != ''">
                recruit_model_num = #{recruitModelNum},
            </if>
            <if test="recruitMsgSendCode != null and recruitMsgSendCode != ''">
                recruit_msg_send_code = #{recruitMsgSendCode},
            </if>
        </set>
        where id = #{id}
    </update>


</mapper>

