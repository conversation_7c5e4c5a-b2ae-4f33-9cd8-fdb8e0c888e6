<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfShuidichouWechatFriendDao">
	<sql id="table_name">
		cf_shuidichou_wechat_friend
	</sql>
	<sql id="fields">
		`id`,
		`qy_wechat_user_id`,
		`union_id`,
		`external_userid`,
		`encrypt_phone`,
		`is_service_user`,
		`pass_time`,
		`create_time`,
		`update_time`,
		`is_delete`,
		`callback_id`,
		`remark`
	</sql>

	<select id="getCfShuidichouWechatFriendByExternalUseridWithQyWechatUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfShuidichouWechatFriendDO">
		select <include refid="fields"/>
		from <include refid="table_name"/>
		where qy_wechat_user_id = #{qyWechatUserId} and external_userid=#{externalUserid} and is_delete=0
	</select>

	<select id="getCfShuidichouWechatFriendByQyWechatUserIds"
			resultType="com.shuidihuzhu.cf.clewtrack.domain.CfShuidichouWechatFriendDO">
		select <include refid="fields"/>
		from <include refid="table_name"/>
		where is_delete = 0
		and qy_wechat_user_id in
		<foreach collection="qyWechatUserIds" item="qyWechatUserId" open="(" close=")" separator=",">
			#{qyWechatUserId}
		</foreach>
		and create_time between #{startDate} and #{endDate}
	</select>

	<select id="getFriendInfoByExternalUserid" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfShuidichouWechatFriendDO">
		select <include refid="fields"/>
		from <include refid="table_name"/>
		where is_delete = 0
		and qy_wechat_user_id = #{qyWechatUserId}
		and external_userid = #{ExternalUserid}
	</select>

</mapper>
