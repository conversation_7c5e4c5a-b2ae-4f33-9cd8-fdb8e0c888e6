<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfRecruitFollowupVisitTaskPoolDao">
    <resultMap type="com.shuidihuzhu.cf.clewtrack.domain.CfRecruitFollowupVisitTaskPoolDO"
               id="CfRecruitFollowupVisitTaskPoolMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="clewId" column="clew_id" jdbcType="INTEGER"/>
        <result property="taskId" column="task_id" jdbcType="INTEGER"/>
        <result property="assignStatus" column="assign_status" jdbcType="INTEGER"/>
        <result property="diseaseName" column="disease_name" jdbcType="VARCHAR"/>
        <result property="age" column="age" jdbcType="INTEGER"/>
        <result property="physical" column="physical" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="taskType" column="task_type" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="field">
        `clew_id`,         `task_id`, `task_type`,        `assign_status`,         `disease_name`,         `age`,         `physical`,         `create_time`,         `update_time`,         `is_delete`
    </sql>
    <sql id="tableName">
        cf_recruit_followup_visit_task_pool
    </sql>
    <!--查询单个-->
    <select id="queryByClewId" resultMap="CfRecruitFollowupVisitTaskPoolMap">
        select
        <include refid="field"/>
        from
        <include refid="tableName"/>
        where clew_id = #{clewId} and is_delete =0
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into<include refid="tableName"/>(clew_id, task_id,task_type, assign_status, disease_name, age, physical)
        values (#{clewId}, #{taskId},#{taskType}, #{assignStatus}, #{diseaseName}, #{age}, #{physical})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update
        <include refid="tableName"/>
        <set>
            <if test="clewId != null">
                clew_id = #{clewId},
            </if>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="assignStatus != null">
                assign_status = #{assignStatus},
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                disease_name = #{diseaseName},
            </if>
            <if test="age != null">
                age = #{age},
            </if>
            <if test="physical != null and physical != ''">
                physical = #{physical},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateAssignStatus">
        update  <include refid="tableName"/> set assign_status = #{assignStatus} where clew_id=#{clewId}
    </update>

    <select id="getCanAssignTaskLimit100" resultMap="CfRecruitFollowupVisitTaskPoolMap">
        select
        <include refid="field"/>
        from
        <include refid="tableName"/>
        where assign_status=0 and is_delete =0 and create_time>#{createTime}
        limit 100
    </select>
</mapper>
