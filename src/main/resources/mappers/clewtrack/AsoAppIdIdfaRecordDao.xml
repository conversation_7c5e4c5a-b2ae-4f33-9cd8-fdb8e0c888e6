<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.AsoAppIdIdfaRecordDao">

    <sql id="table_name">
        `aso_appid_idfa_record`
    </sql>

    <sql id="insert_fields">
        `app_id`,
        `idfa`,
        `ip`,
        `status`,
        `call_back_uri`,
        `biz_type`
    </sql>

    <sql id="select_fields">
        `id`,
        `app_id`,
        `idfa`,
        `ip`,
        `status`,
        `call_back_uri`,
        `biz_type`,
        `a_id`,
        `aid_name`,
        `campaign_id`,
        `c_id`,
        `csite`,
        `imei`,
        `os`
    </sql>


    <select id="selectRecordByIdfa" resultType="com.shuidihuzhu.cf.clewtrack.domain.AsoAppIdIdfaRecord">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where `status`= 1 and `idfa`=#{idfa} and biz_type !=8
        limit 1
    </select>


    
</mapper>