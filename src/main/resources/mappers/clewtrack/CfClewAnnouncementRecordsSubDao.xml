<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewAnnouncementRecordsSubDao">

  <sql id="tableFiled">
    id, announcement_records_id, announcement_recipient, announcement_recipient_mis,
    announcement_recipient_org_id, announcement_recipient_org_name, is_delete, create_time,
    update_time
  </sql>
  <sql id="tableName">
    cf_clew_announcement_records_sub
  </sql>
  <delete id="deleteBatchByIds">
    update  <include refid="tableName"/> set is_delete =1
    where id in
    <foreach collection="list" item="item" index="index" separator="," close=")" open="(">
      #{item}
    </foreach>
  </delete>
  <delete id="enableMisByIds">
    update  <include refid="tableName"/> set is_delete =0
    where id in
    <foreach collection="list" item="item" index="index" separator="," close=")" open="(">
      #{item}
    </foreach>
  </delete>
    <delete id="deleteByRecordId">
        update <include refid="tableName"/>
        set is_delete =1
        where announcement_records_id = #{recordId}
    </delete>
  <select id="selectByRecordsId" parameterType="java.lang.Long" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsSubDO" >
    select
    <include refid="tableFiled" />
    from <include refid="tableName"/>
    where announcement_records_id = #{recordId}
  </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsSubDO" useGeneratedKeys="true">
    insert into  <include refid="tableName"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="announcementRecordsId != null">
        announcement_records_id,
      </if>
      <if test="announcementRecipient != null">
        announcement_recipient,
      </if>
      <if test="announcementRecipientMis != null">
        announcement_recipient_mis,
      </if>
      <if test="announcementRecipientOrgId != null">
        announcement_recipient_org_id,
      </if>
      <if test="announcementRecipientOrgName != null">
        announcement_recipient_org_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="announcementRecordsId != null">
        #{announcementRecordsId},
      </if>
      <if test="announcementRecipient != null">
        #{announcementRecipient},
      </if>
      <if test="announcementRecipientMis != null">
        #{announcementRecipientMis},
      </if>
      <if test="announcementRecipientOrgId != null">
        #{announcementRecipientOrgId},
      </if>
      <if test="announcementRecipientOrgName != null">
        #{announcementRecipientOrgName},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    INSERT into
    <include refid="tableName"/>
    (announcement_records_id,announcement_recipient,announcement_recipient_mis,
    announcement_recipient_org_id,announcement_recipient_org_name)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.announcementRecordsId},#{item.announcementRecipient},#{item.announcementRecipientMis},
      #{item.announcementRecipientOrgId},#{item.announcementRecipientOrgName}
      )
    </foreach>
  </insert>


</mapper>