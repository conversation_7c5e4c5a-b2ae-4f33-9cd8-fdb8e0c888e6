<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfForecastCallTaskDao">

    <sql id="tableName">cf_forecast_call_task</sql>

    <sql id="baseResult">
      `id`,
      `task_id`,
      `task_status`,
      `task_description`,
      `start_time`,
      `end_time`,
      `create_time`,
      `update_time`,
	  `is_delete`,
	  `task_type`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallTaskDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="taskId != null">
                task_id,
            </if>
            <if test="taskStatus != null">
                task_status,
            </if>
            <if test="taskDescription != null">
                task_description,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="taskStatus != null">
                #{taskStatus},
            </if>
            <if test="taskDescription != null">
                #{taskDescription},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="taskType != null">
                #{taskType} ,
            </if>
        </trim>
    </insert>

    <update id="updateTaskInfo">
        update
        <include refid="tableName"/>
        <set>
            <if test="description != null">
                task_description=#{description} ,
            </if>
            <if test="status != null">
                task_status=#{status} ,
            </if>
            <if test="endTime != null">
                end_time=#{endTime},
            </if>
        </set>
        where task_id=#{taskId}
    </update>

    <select id="getCfForecastCallTaskByTaskStatusAndTime"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_status=#{status}
        and create_time between #{startTime} and #{endTime}
    </select>

    <select id="getCfForecastCallTaskByTaskId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_id=#{taskId}
        order by id desc limit 1
    </select>

    <update id="updateTaskStatus">
        update <include refid="tableName"/>
        <set>
            <if test="status != null">
                task_status=#{status} ,
            </if>
        </set>
        where task_id=#{taskId}
    </update>

    <select id="getTaskIdByTaskTypeAndDateTime" resultType="java.lang.Integer">
        select task_id
        from <include refid="tableName"/>
        where create_time between #{startTime} AND #{endTime}
        <if test="taskType != null">
            and  task_type = #{taskType}
        </if>
        order by create_time desc
        limit 1
    </select>

    <select id="getCfForecastCallAllTaskIdByTaskTypeAndDateTime" resultType="java.lang.Integer">
        select task_id
        from <include refid="tableName"/>
        where create_time between #{startTime} AND #{endTime}
        <if test="taskType != null">
            and  task_type = #{taskType}
        </if>
    </select>

</mapper>
