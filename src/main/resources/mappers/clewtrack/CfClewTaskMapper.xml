<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTaskDao">

    <sql id="tableName">cf_clew_task</sql>

    <sql id="baseResult">
      id,
      clew_id,
      packet_id,
      create_time,
      update_time,
      task_status,
      delay_handle,
      task_type,
      expect_last_handle_time,
      expect_last_assgin_time,
      assgin_time,
      last_handle_time,
      user_id,
      user_name,
      is_delete,
      work_order_id,
      is_recycle,
      source_type,
      eagle_handle,
      work_content_type,
      priority,
      is_closed,
      business_tags,
      workbench_type,
      clew_layer_name,
      is_award,
      clew_layer_config_id,
      last_assign_type,
      crowd_packet_id,
      score,
      closed_type,
      end_reason_remark
    </sql>
    <sql id="whereCondition">
        where 1=1 and ct.is_delete=0
        <if test="taskTypeList != null and taskTypeList.size()>0">
            and ct.task_type in
            <foreach collection="taskTypeList" item="taskType" open="(" separator="," close=")">
                #{taskType}
            </foreach>
        </if>
        <if test="taskStatusList != null and taskStatusList.size()>0">
            and ct.task_status in
            <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
                #{taskStatus}
            </foreach>
        </if>
        <if test="packetIdList != null and packetIdList.size()>0">
            and ct.packet_id in
            <foreach collection="packetIdList" item="packetId" open="(" separator="," close=")">
                #{packetId}
            </foreach>
        </if>
        <if test="delayHandle!=null">
            and ct.delay_handle = #{delayHandle}
        </if>
        <if test="userIdList != null and userIdList.size()>0">
            and ct.user_id in
            <foreach collection="userIdList" item="userid" separator="," open="(" close=")">
                #{userid}
            </foreach>
        </if>
        <if test="clewStatus != null">
            and cb.status = #{clewStatus}
        </if>
        <if test="phoneStatus != null">
            and cb.phone_status = #{phoneStatus}
        </if>
        <if test="taskId != null">
            and ct.id = #{taskId}
        </if>
        <if test="startTime != null and endTime !=null">
            and cb.create_time between #{startTime} and #{endTime}
        </if>
        <if test="handleStartTime != null and handleEndTime != null">
            and ct.update_time between #{handleStartTime} and #{handleEndTime}
        </if>
        <if test="primaryChannelList != null and primaryChannelList.size()>0">
            and cb.primary_channel in
            <foreach collection="primaryChannelList" item="primaryChannel" separator="," open="(" close=")">
                #{primaryChannel}
            </foreach>
        </if>
        <if test="clewType!=null">
            and cb.clew_type = #{clewType}
        </if>
        <if test="assignTimeStart!=null and assignTimeEnd!=null">
            and ct.assgin_time between #{assignTimeStart} and #{assignTimeEnd}
        </if>
        <if test="isRecycle!=null">
            and ct.is_recycle=#{isRecycle}
        </if>
        <if test="displayid!=null and displayid!=''">
            and cb.display_id=#{displayid}
        </if>
    </sql>

    <sql id="whereCondition4WaitAssign">
        where 1=1 and ct.is_delete=0
        <if test="phoneStatus != null">
            and cb.phone_status = #{phoneStatus}
        </if>
        <if test="taskTypeList != null and taskTypeList.size()>0">
            and ct.task_type in
            <foreach collection="taskTypeList" item="taskType" open="(" separator="," close=")">
                #{taskType}
            </foreach>
        </if>
        <if test="taskStatusList != null and taskStatusList.size()>0">
            and ct.task_status in
            <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
                #{taskStatus}
            </foreach>
        </if>
        <if test="packetIdList != null and packetIdList.size()>0">
            and ct.packet_id in
            <foreach collection="packetIdList" item="packetId" open="(" separator="," close=")">
                #{packetId}
            </foreach>
        </if>
        <if test="delayHandle!=null">
            and ct.delay_handle = #{delayHandle}
        </if>
        <if test="listType != null">
            and ct.delay_handle = 1
        </if>
        <if test="clewStatus != null">
            and cb.status = #{clewStatus}
        </if>
        <if test="taskId != null">
            and ct.id = #{taskId}
        </if>
        <if test="startTime != null and endTime !=null ">
            and cb.create_time between #{startTime} and #{endTime}
        </if>
        <if test="waitAssignCountFlag != null">
            and ct.create_time between #{createTimeStart} and #{createTimeEnd}
        </if>
        <if test="primaryChannelList != null and primaryChannelList.size()>0">
            and cb.primary_channel in
            <foreach collection="primaryChannelList" item="primaryChannel" separator="," open="(" close=")">
                #{primaryChannel}
            </foreach>
        </if>
        <if test="clewType!=null">
            and cb.clew_type = #{clewType}
        </if>
        <if test="isRecycle!=null">
            and ct.is_recycle=#{isRecycle}
        </if>
        <if test="workContentType != null">
            and ct.work_content_type = #{workContentType}
        </if>
    </sql>
    <sql id="fuwuWhereCondition">
        where 1=1 and ct.is_delete=0 and ct.delay_handle=0
        <if test="serviceUserId!=null and serviceUserId!=''">
            and cai.user_id=#{serviceUserId}
        </if>
        <if test="userid!=null and userid!=''">
            and ct.user_id=#{userid}
        </if>
        <if test="taskTypes != null and taskTypes.size()>0">
            and ct.task_type in
            <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
                #{taskType}
            </foreach>
        </if>
        <if test="taskStatusList != null and taskStatusList.size()>0">
            and ct.task_status in
            <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
                #{taskStatus}
            </foreach>
        </if>
        <if test="clewTypes != null and clewTypes.size()>0">
            and cb.clew_type in
            <foreach collection="clewTypes" item="clewType" open="(" separator="," close=")">
                #{clewType}
            </foreach>
        </if>
        <if test="packetIdList != null and packetIdList.size>0">
            and ct.packet_id in
            <foreach collection="packetIdList" item="packetId" open="(" separator="," close=")">
                #{packetId}
            </foreach>
        </if>
        <if test="userFlag != null">
            and cai.user_flag = #{userFlag}
        </if>
        <if test="wechatPass != null">
            and cai.wechat_pass = #{wechatPass}
        </if>
        <if test="cfStatus !=null">
            and cb.status=#{cfStatus}
        </if>
        <if test="primaryChannel !=null and primaryChannel!=''">
            and cb.primary_channel=#{primaryChannel}
        </if>
        <if test="handleStartTime != null and handleEndTime != null">
            and (ct.update_time between #{handleStartTime} and #{handleEndTime}) and ct.task_status!=1
        </if>
        <if test="startTime != null and endTime != null">
            and (ct.create_time between #{startTime} and #{endTime})
        </if>
        <if test="cfStartTime != null and cfEndTime != null">
            and (cb.system_handle_time between #{cfStartTime} and #{cfEndTime}) and cb.status=2
        </if>
        <if test="amountStart != null and amountEnd != null">
            and (cca.amount between #{amountStart} and #{amountEnd})
        </if>
        <if test="serviceStatus != null">
            and cai.service_status = #{serviceStatus}
        </if>
        <if test="assignStartTime != null and assignEndTime !=null">
            and (ct.assgin_time between #{assignStartTime} and #{assignEndTime})
        </if>
        <if test="isRecycle!=null">
            and ct.is_recycle = #{isRecycle}
        </if>
        <if test="userFirstTag != null">
            and cai.first_tag = #{userFirstTag}
        </if>
        <if test="userSecondTag != null">
            and cai.second_tag = #{userSecondTag}
        </if>
        <if test="firstApproveStatus != null">
            and cb.first_approve_status = #{firstApproveStatus}
        </if>
        <if test="infoId != null">
            and cb.info_id = #{infoId}
        </if>
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        (clew_id,packet_id,task_status,delay_handle,task_type,expect_last_handle_time,
        expect_last_assgin_time,assgin_time,last_handle_time,user_id,user_name
        <if test="workOrderId != null">
          ,work_order_id
        </if>
        <if test="createTime != null">
            ,create_time
        </if>
        <if test="updateTime != null">
            ,update_time
        </if>
        <if test="isRecycle != null">
            ,is_recycle
        </if>
        <if test="sourceType != null">
            ,source_type
        </if>
        <if test="eagleHandle != null">
            ,eagle_handle
        </if>
        <if test="workContentType != null">
            ,work_content_type
        </if>
        <if test="priority != null">
            ,priority
        </if>
        <if test="workbenchType != null">
            ,workbench_type
        </if>
        <if test="clewLayerName != null and clewLayerName != ''">
            ,clew_layer_name
        </if>
        <if test="isAward != null ">
            ,is_award
        </if>
        <if test="clewLayerConfigId != null ">
            ,clew_layer_config_id
        </if>
        <if test="score != null and score!=''">
            ,score
        </if>
        <if test="isClosed != null">
            ,is_closed
        </if>
        <if test="closedType != null">
            ,closed_type
        </if>
        <if test="isDelete != null">
            ,is_delete
        </if>
        )
        values
        (#{clewId},#{packetId},#{taskStatus},#{delayHandle},#{taskType},#{expectLastHandleTime},
        #{expectLastAssginTime},#{assginTime},#{lastHandleTime},#{userId},#{userName}
        <if test="workOrderId != null">
            ,#{workOrderId}
        </if>
        <if test="createTime != null">
            ,#{createTime}
        </if>
        <if test="updateTime != null">
            ,#{updateTime}
        </if>
        <if test="isRecycle != null">
            ,#{isRecycle}
        </if>
        <if test="sourceType != null">
            ,#{sourceType}
        </if>
        <if test="eagleHandle != null">
            ,#{eagleHandle}
        </if>
        <if test="workContentType != null">
            ,#{workContentType}
        </if>
        <if test="priority != null">
            ,#{priority}
        </if>
        <if test="workbenchType != null">
            ,#{workbenchType}
        </if>
        <if test="clewLayerName != null and clewLayerName != ''">
            ,#{clewLayerName}
        </if>
        <if test="isAward != null ">
            ,#{isAward}
        </if>
        <if test="clewLayerConfigId != null ">
            ,#{clewLayerConfigId}
        </if>
        <if test="score != null and score!=''">
            ,#{score}
        </if>
        <if test="isClosed != null">
            ,#{isClosed}
        </if>
        <if test="closedType != null">
            ,#{closedType}
        </if>
        <if test="isDelete != null">
            ,#{isDelete}
        </if>
        )
    </insert>

    <update id="updateTaskStatus">
        update
        <include refid="tableName"/>
        set task_status = #{targetStatus}
        where id=#{taskId} and task_status=#{sourceStatus}
    </update>

    <update id="updateTaskInfo"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        update
        <include refid="tableName"/>
        <set>
            <if test="clewId != null">
                clew_id=#{clewId},
            </if>
            <if test="packetId != null">
                packet_id=#{packetId},
            </if>
            <if test="taskStatus != null">
                task_status=#{taskStatus},
            </if>
            <if test="delayHandle != null">
                delay_handle=#{delayHandle},
            </if>
            <if test="taskType != null">
                task_type=#{taskType},
            </if>
            <if test="expectLastHandleTime != null">
                expect_last_handle_time=#{expectLastHandleTime},
            </if>
            <if test="expectLastAssginTime != null">
                expect_last_assgin_time=#{expectLastAssginTime},
            </if>
            <if test="assginTime != null">
                assgin_time=#{assginTime},
            </if>
            <if test="lastHandleTime != null">
                last_handle_time=#{lastHandleTime},
            </if>
            <if test="userId != null and userId!=''">
                user_id=#{userId},
            </if>
            <if test="userName != null and userName !=''">
                user_name=#{userName},
            </if>
            <if test="isDelete != null">
                is_delete=#{isDelete},
            </if>
            <if test="workOrderId != null">
                work_order_id=#{workOrderId},
            </if>
            <if test="sourceType != null">
                source_type=#{sourceType},
            </if>
            <if test="priority != null">
                priority = #{priority},
            </if>
            <if test="workContentType != null">
                work_content_type=#{workContentType},
            </if>
            <if test="eagleHandle != null">
                eagle_handle=#{eagleHandle} ,
            </if>
            <if test="lastAssignType != null">
                last_assign_type=#{lastAssignType} ,
            </if>
            <if test="crowdPacketId != null">
                crowd_packet_id=#{crowdPacketId} ,
            </if>
            <if test="isClosed != null">
                is_closed = #{isClosed},
            </if>
            <if test="closedType != null">
                closed_type = #{closedType},
            </if>
        </set>
        where id=#{id}
    </update>

    <update id="updateExpectLastAssginTime">
        update
        <include refid="tableName"/>
        set expect_last_assgin_time=#{expectLastAssginTime}
        where id = #{id}
    </update>
    <update id="updateTaskHandelFlag">
        update
        <include refid="tableName"/>
        set delay_handle=#{delayHandleFlag}
        where id=#{id}
    </update>

    <update id="updateTaskHandelFlagWithPriorityWithTaskStatus">
        update
        <include refid="tableName"/>
        set delay_handle=#{delayHandleFlag},priority=#{priority},task_status=#{taskStatus}
        where id=#{id}
    </update>
    <select id="getTaskByTaskIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select
        <include refid="baseResult"/>
        from cf_clew_task
        where id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="getTaskByTaskIdsFromMaster"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select
        <include refid="baseResult"/>
        from cf_clew_task
        where id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="getUnHandlerTaskCount" resultType="java.lang.Integer">
        select count(1)
        from
        <include refid="tableName"/>
        where user_id=#{userid} and delay_handle = 0 and is_delete=0 and task_type in
        <foreach collection="taskTypeList" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
        and packet_id in
        <foreach collection="packetIdList" item="packetId" open="(" separator="," close=")">
            #{packetId}
        </foreach>
    </select>
    <select id="getClewInfoModelSearchParam"
            parameterType="com.shuidihuzhu.cf.dao.clewtrack.param.TaskSearchParam"
            resultType="com.shuidihuzhu.cf.clewtrack.model.CFClewInfoModel">
        select
          cb.id as clew_id,
          cb.status as clew_status_code,
          cb.phone_status as phone_status_code,
          cb.disease_name as disease_name,
          cb.expect_contact_time as expect_contact_time,
          cb.province as province,
          cb.city as city,
          cb.province_id as province_id,
          cb.city_id as city_id,
          cb.address as address,
          cb.hosptial_name as hosptial_name,
          cb.wh_remark_content as special_mark,
          cb.display_id as display_id,
          cb.fundraising_object as fundraising_object,
          cb.is_follow as is_follow,
          cb.primary_channel as primary_channel,
          cb.not_initiate_reason as not_initiate_reason,
          DATE_FORMAT(cb.create_time,'%Y-%m-%d %H:%i:%s') as clew_create_time,
          cb.register_name as register_name,
          cb.register_mode as register_mode,
          ct.user_name as handle_name,
          DATE_FORMAT(ct.update_time,'%Y-%m-%d %H:%i:%s') as task_modify_time,
          ct.user_name as clew_create_user_name,
          ct.task_type as task_type,
          ct.user_id as user_id,
          ct.id as task_id,
          ct.task_status as task_status,
          cb.cf_title as cf_title,
          cb.cf_content as cf_content,
          cb.cf_target_amount as cf_target_amount,
          DATE_FORMAT(ct.assgin_time,'%Y-%m-%d %H:%i:%s') as assign_time,
          cb.phone_is_repetitive as phone_is_repetitive
        from cf_clew_task ct
        join cf_clew_base_info cb on cb.id=ct.clew_id
        <include refid="whereCondition"/>
        <choose>
            <when test="orderBySign == 0">
                ORDER BY ct.assgin_time asc
            </when>
            <when test="orderBySign == 2">
                ORDER BY cb.create_time asc
            </when>
            <otherwise>
                ORDER BY ct.update_time desc
            </otherwise>
        </choose>
        <if test="offset != null">
            limit #{offset},#{pageSize}
        </if>
    </select>
    <select id="getClewInfoModelCounttBySearchParam"
            parameterType="com.shuidihuzhu.cf.dao.clewtrack.param.TaskSearchParam"
            resultType="java.lang.Integer">
        select
        count(1)
        from cf_clew_task ct
        join cf_clew_base_info cb on cb.id=ct.clew_id
        <include refid="whereCondition"/>
    </select>

    <select id="getFuWuClewInfoModelCountBySearchParam"
            parameterType="com.shuidihuzhu.cf.clewtrack.param.ClewInfoForFuwuSearchParam"
            resultType="java.lang.Integer">
        select
        count(1)
        from cf_clew_task ct
        join cf_clew_attach_info cai on cai.task_type=ct.task_type and cai.clew_id = ct.clew_id
        join cf_clew_base_info cb on cb.id=cai.clew_id
        <if test="amountStart != null and amountEnd != null">
            left join cf_clew_case_amount cca on cb.info_uuid = cca.info_uuid
        </if>
        <include refid="fuwuWhereCondition"/>
    </select>

    <select id="getFuWuClewInfoModelSearchParam"
            parameterType="com.shuidihuzhu.cf.clewtrack.param.ClewInfoForFuwuSearchParam"
            resultType="com.shuidihuzhu.cf.clewtrack.model.CFClewInfoModel">
        select
        cb.id as clew_id,
        cb.info_uuid as info_uuid,
        cb.info_id as info_id,
        cb.first_approve_status as first_approve_status,
        if(cb.status=2,cb.cf_base_status,-1) as cf_base_status,
        cai.id as attach_id,
        cai.request_status as clew_confirm_status_code,
        cb.phone_status as phone_status_code,
        cb.disease_name as disease_name,
        cb.expect_contact_time as expect_contact_time,
        cb.province as province,
        cb.city as city,
        cb.province_id as province_id,
        cb.city_id as city_id,
        cb.address as address,
        cb.hosptial_name as hosptial_name,
        cb.wh_remark_content as special_mark,
        cb.display_id as display_id,
        cb.fundraising_object as fundraising_object,
        cb.is_follow as is_follow,
        cb.primary_channel as primary_channel,
        cb.not_initiate_reason as not_initiate_reason,
        if(cb.status=2,DATE_FORMAT(cb.system_handle_time,'%Y-%m-%d %H:%i:%s'),null) as clew_create_time,
        cb.register_name as register_name,
        cb.register_mode as register_mode,
        ct.user_name as handle_name,
        if(ct.task_status=1,null,DATE_FORMAT(ct.update_time,'%Y-%m-%d %H:%i:%s')) as task_modify_time,
        ct.user_name as clew_create_user_name,
        ct.task_type as task_type,
        ct.user_id as user_id,
        DATE_FORMAT(ct.create_time,'%Y-%m-%d %H:%i:%s') as task_create_time,
        ct.id as task_id,
        ct.task_status as task_status,
        cb.cf_title as cf_title,
        cb.cf_content as cf_content,
        cb.cf_target_amount as cf_target_amount,
        cai.user_flag as user_flag,
        cai.wechat_id as wechat_id,
        cai.service_status as service_status,
        DATE_FORMAT(ct.assgin_time,'%Y-%m-%d %H:%i:%s') as assign_time,
        wechat_valid,
        wechat_pass,
        ct.is_recycle,
        cai.first_tag as first_tag,
        cai.second_tag as second_tag,
        cai.wechat_reject_reason as wechat_reject_reason,
        ct.source_type as source_type,
        cai.age as age,
        cai.physical as physical
        from cf_clew_task ct
        join cf_clew_attach_info cai on cai.task_type=ct.task_type and cai.clew_id = ct.clew_id
        join cf_clew_base_info cb on cb.id=cai.clew_id
        <if test="amountStart != null and amountEnd != null">
            left join cf_clew_case_amount cca on cb.info_uuid = cca.info_uuid
        </if>
        <include refid="fuwuWhereCondition"/>
        <choose>
            <when test="orderBySign == 0">
                ORDER BY ct.assgin_time desc
            </when>
            <otherwise>
                ORDER BY ct.task_status asc,ct.update_time desc
            </otherwise>
        </choose>
        <if test="offset != null">
            limit #{offset},#{pageSize}
        </if>
    </select>

    <select id="getClewInfoModelSearchParamForClewPool"
            parameterType="com.shuidihuzhu.cf.dao.clewtrack.param.TaskSearchParam"
            resultType="com.shuidihuzhu.cf.clewtrack.model.CFClewInfoModel">
        select
        cb.id as clew_id,
        cb.status as clew_status_code,
        cai.request_status as clew_confirm_status_code,
        cb.phone_status as phone_status_code,
        cb.disease_name as disease_name,
        cb.expect_contact_time as expect_contact_time,
        cb.province as province,
        cb.city as city,
        cb.province_id as province_id,
        cb.city_id as city_id,
        cb.address as address,
        cb.hosptial_name as hosptial_name,
        cb.wh_remark_content as special_mark,
        cb.display_id as display_id,
        cb.fundraising_object as fundraising_object,
        cb.is_follow as is_follow,
        cb.primary_channel as primary_channel,
        cb.not_initiate_reason as not_initiate_reason,
        DATE_FORMAT(cb.create_time,'%Y-%m-%d %H:%i:%s') as clew_create_time,
        cb.register_name as register_name,
        cb.register_mode as register_mode,
        cai.user_name as handle_name,
        DATE_FORMAT(ct.update_time,'%Y-%m-%d %H:%i:%s') as task_modify_time,
        ct.user_name as clew_create_user_name,
        ct.task_type as taskType,
        ct.user_id as user_id,
        ct.id as task_id,
        ct.task_status as task_status,
        cb.cf_title as cf_title,
        cb.cf_content as cf_content,
        cb.cf_target_amount as cf_target_amount,
        cai.first_tag as first_tag,
        cai.second_tag as second_tag,
        cb.phone_is_repetitive as phone_is_repetitive,
        cai.age as age,
        cai.physical as physical
        from cf_clew_base_info cb
        left join cf_clew_attach_info cai on cb.id=cai.clew_id
        left join cf_clew_task ct on cai.task_type=ct.task_type and cai.clew_id = ct.clew_id
        <include refid="whereCondition"/>
        <choose>
            <when test="orderBySign == 0">
                ORDER BY cb.create_time desc
            </when>
            <otherwise>
                ORDER BY ct.update_time desc
            </otherwise>
        </choose>
        <if test="offset != null">
            limit #{offset},#{pageSize}
        </if>
    </select>

    <select id="getClewInfoModelCounttBySearchParamForClewPool"
            parameterType="com.shuidihuzhu.cf.dao.clewtrack.param.TaskSearchParam"
            resultType="java.lang.Integer">
        select count(1)
        from cf_clew_base_info cb
        left join cf_clew_attach_info cai on cb.id=cai.clew_id
        left join cf_clew_task ct on cai.task_type=ct.task_type and cai.clew_id = ct.clew_id
        <include refid="whereCondition"/>
    </select>


    <update id="shutDownTaskByTaskIds">
        update
        <include refid="tableName"/>
        set task_status=6
        where id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>

    </update>


    <select id="getWaitAssignClewInfoModelSearchParam"
            parameterType="com.shuidihuzhu.cf.dao.clewtrack.param.TaskSearchParam"
            resultType="com.shuidihuzhu.cf.clewtrack.model.CFClewInfoModel">
        select
        cb.id as clew_id,
        cb.status as clew_status_code,
        cb.phone_status as phone_status_code,
        cb.disease_name as disease_name,
        cb.expect_contact_time as expect_contact_time,
        cb.province as province,
        cb.city as city,
        cb.province_id as province_id,
        cb.city_id as city_id,
        cb.address as address,
        cb.hosptial_name as hosptial_name,
        cb.wh_remark_content as special_mark,
        cb.display_id as display_id,
        cb.fundraising_object as fundraising_object,
        cb.is_follow as is_follow,
        cb.primary_channel as primary_channel,
        cb.not_initiate_reason as not_initiate_reason,
        DATE_FORMAT(cb.create_time,'%Y-%m-%d %H:%i:%s') as clew_create_time,
        cb.register_name as register_name,
        cb.register_mode as register_mode,
        DATE_FORMAT(ct.update_time,'%Y-%m-%d %H:%i:%s') as task_modify_time,
        ct.user_name as clew_create_user_name,
        ct.task_type as task_type,
        ct.user_id as user_id,
        ct.id as task_id,
        ct.task_status as task_status,
        cb.cf_title as cf_title,
        cb.cf_content as cf_content,
        cb.cf_target_amount as cf_target_amount,
        cb.phone_is_repetitive as phone_is_repetitive
        from cf_clew_task ct
        join cf_clew_base_info cb ON cb.id = ct.clew_id
        <include refid="whereCondition4WaitAssign"/>
        ORDER BY cb.create_time asc
        <if test="offset != null">
            limit #{offset},#{pageSize}
        </if>
    </select>
    <select id="getWaitAssignClewInfoModelCounttBySearchParam"
            parameterType="com.shuidihuzhu.cf.dao.clewtrack.param.TaskSearchParam"
            resultType="java.lang.Integer">
        select
        count(1)
        from cf_clew_task ct
        join cf_clew_base_info cb on cb.id=ct.clew_id
        <include refid="whereCondition4WaitAssign"/>
    </select>
    <select id="getUnHandleTaskListByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select
        <include refid="baseResult"/>
        from cf_clew_task
        where is_delete=0 and clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </select>
    <update id="shutDownTaskByTaskId">
        update
        <include refid="tableName"/>
        set task_status=6,is_closed=1,closed_type=#{closeType}
        where task_type in
        <foreach collection="taskTypeList" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
        and id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>
    <select id="getCurrentDayClewStatistics" resultType="com.shuidihuzhu.cf.clewtrack.model.CFClewManageModel">
        SELECT
        IFNULL(SUM(case when cct.packet_id!=0 then 1 else 0 end),0)-IFNULL(SUM(case when cct.task_status=6 and cct.delay_handle=1 then 1 else 0 end),0) as newClew,
        IFNULL(SUM(case when cct.task_status not in (2,6) and cct.packet_id!=0 then 1 else 0 end),0) as surplusClew
        from cf_clew_base_info ccbi
        join cf_clew_task cct on ccbi.id = cct.clew_id
        where cct.task_type=1 and  cct.is_delete=0 and
            ((ccbi.create_time BETWEEN #{registeStartTime} and #{registeEndTime}) or (ccbi.create_time BETWEEN #{manualStartTime} and #{manualEndTime} and register_mode=2))
            and ccbi.clew_type=#{clewType};
    </select>
    <select id="getCurrentDayhandleClewStatistics" resultType="java.lang.Integer">
        SELECT
        count(1) as handleClew
        from cf_clew_task cct
        join cf_clew_base_info ccbi on ccbi.id=cct.clew_id
        where cct.update_time>#{lastHandleTime} and cct.task_type=1
        and ccbi.clew_type=#{clewType} and cct.task_status=6 and cct.delay_handle=0 and cct.user_id!='' and cct.is_delete=0 ;
    </select>

    <select id="getWhCurrentDayWxTranferClew" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT clew_id) as wxTranferClew
        from cf_clew_attach_info where update_time>#{startTime} and request_status in (1,2) and wechat_id!='' and task_type=1;
    </select>
    <select id="getCurrentDayWxTranferClew" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT clew_id) wxTransferClew
        from cf_clew_task where create_time >= #{startTime} and task_type=3 and is_delete=0;
    </select>

    <select id="get7DayClew" resultType="com.shuidihuzhu.cf.clewtrack.model.CFClewManageModel">
        SELECT
            SUM(case when cct.task_status!=6 and cct.delay_handle=1 then 1 else 0 end) as waitAssignClew,
            SUM(case when cct.task_status=2 and cct.delay_handle=0 then 1 else 0 end) as waitCallClew
        from cf_clew_task  cct
        force index(idx_clew_id_user_id_packet_id)
        where cct.task_type=1 and cct.is_delete=0 and cct.clew_id in (SELECT id from cf_clew_base_info where create_time >#{startTime} and clew_type=#{clewType});
    </select>
    <select id="getUserCurrentDayAssignClewBase" resultType="java.lang.Long">
        -- 查询每个人当前线索的统计
        SELECT ccbi.id
        from cf_clew_base_info ccbi
        join  cf_clew_task cct on ccbi.id = cct.clew_id
        where cct.assgin_time >= #{startTime} and ccbi.clew_type=#{clewType} and cct.task_type=1 and cct.is_delete=0 and cct.delay_handle=0
        <if test="userIds!=null and userIds.size()>0">
            and cct.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="getUserCurrentClewStatistics" resultType="com.shuidihuzhu.cf.clewtrack.model.CFUserClewmanageModel">
        -- 查询每个人当前线索的统计
        SELECT
        cct.user_name as userName,
        cct.user_id as clewUserId,
        cct.packet_id as packetId,
        sum(case when cct.task_status=1 and cct.delay_handle=0 then 1 else 0 end) as callClew,
        SUM(case when cct.task_status=2 and cct.delay_handle=0 then 1 else 0 end) as newWaitCallClew,
        sum(case when cct.task_status=6 and cct.delay_handle=0 then 1 else 0 end) as handledClew,
        sum(case when cct.task_status!=0 and cct.delay_handle=0 then 1 else 0 end) as assignClew,
        sum(case when cct.task_status!=0 and cct.delay_handle=0 and cct.last_assign_type=3 then 1 else 0 end) as fuboAssignClew,
        sum(case when cct.task_status!=0 and cct.delay_handle=0 and cct.last_assign_type=1 then 1 else 0 end) as awardAssignClew,
        sum(case when cct.task_status!=0 and cct.delay_handle=0 and cct.last_assign_type=0 then 1 else 0 end) as newAssignClew,
        sum(case when cct.task_status!=0 and cct.delay_handle=0 then 1 else 0 end) as assignClew,
        sum(case when ccbi.phone_status=10 then 1 else 0 end) as noNeedCallClew
        from cf_clew_base_info ccbi
        join  cf_clew_task cct on ccbi.id = cct.clew_id
        where cct.assgin_time >= #{startTime} and ccbi.clew_type=#{clewType} and cct.task_type=1 and cct.is_delete=0
        <if test="userIds!=null and userIds.size()>0">
            and cct.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        GROUP BY cct.user_id,cct.packet_id
    </select>
    <select id="getUserCurrentWaitCallClew" resultType="com.shuidihuzhu.cf.clewtrack.model.CFUserClewmanageModel">
        SELECT
            cct.user_id as clewUserId,
            count(1) as waitCallClew
        from cf_clew_base_info ccbi
        join  cf_clew_task cct on ccbi.id = cct.clew_id
        where cct.assgin_time >= #{timeBefore7} and ccbi.clew_type=#{clewType}  and cct.is_delete=0 and cct.task_type=1 and cct.user_id!='' and cct.task_status=2 and cct.delay_handle=0
        <if test="userIds!=null and userIds.size()>0">
            and cct.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        GROUP BY cct.user_id ORDER BY cct.user_id;
    </select>
    <select id="getUserCurrentWxTransferClew" resultType="com.shuidihuzhu.cf.clewtrack.model.CFUserClewmanageModel">
        -- 查询 每个人微信服务流转量
        SELECT
        user_id as clewUserId,
        SUM(1) as wxTranferClew
        from cf_clew_attach_info where update_time>#{startTime} and request_status in (1,2) and wechat_id!='' and task_type=1
        <if test="userIds!=null and userIds.size()>0">
            and user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        GROUP BY user_id
    </select>
    <select id="getBeforeCurrentClewTimeCount" resultType="java.lang.Integer">
        SELECT count(1)
        from cf_clew_base_info ccbi
        join cf_clew_task cct on ccbi.id = cct.clew_id and cct.task_status=0 and cct.task_type=3 and cct.user_id=''
        where ccbi.create_time <![CDATA[<]]> #{lastCreateTime} and cct.is_delete=0;
    </select>
    <select id="getFuwuTaskByCaseId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        SELECT <include refid="baseResult"/> from <include refid="tableName"/>
        where task_type=3 and clew_id in (
        SELECT id from cf_clew_base_info where info_id=#{caseId}) and is_delete=0
        limit 1;
    </select>
    <select id="getFuWuWaitAssignAndAssignedCount" resultType="com.shuidihuzhu.cf.clewtrack.model.CFFuWuManageModel">
        SELECT
          IFNULL(sum(case when task_status=0 then 1 else 0 end ),0) as waitAssign,
          IFNULL(sum(case when task_status!=0 then 1 else 0 end ),0) as assigned
        from cf_clew_task
        where task_type=3 and create_time BETWEEN #{startTime} and #{endTime} and is_delete=0;
    </select>
    <select id="getFuWuCurrentDayCaseCount" resultType="com.shuidihuzhu.cf.clewtrack.model.CFFuWuManageModel">
        SELECT
          IFNULL(COUNT(DISTINCT ccbi.info_uuid),0) as currentDayLaunchCase,
          IFNULL(sum(case when ccca.amount>0 then 1 else 0 end),0) as currentDayRMBCase
        from cf_clew_task ct
        INNER JOIN cf_clew_base_info ccbi on ct.clew_id=ccbi.id
        left JOIN cf_clew_case_amount ccca on ccca.info_uuid=ccbi.info_uuid
        where ct.task_type=3 and ct.assgin_time >= #{currentDate} and ccbi.`status`=2 and ct.is_recycle=0 and ccbi.system_handle_time > #{currentDate} and ct.is_delete=0;
    </select>
    <select id="getFuwuWaitAssignCount" resultType="java.lang.Integer">
        SELECT count(1) from cf_clew_task  where task_type=3 and task_status=0 and delay_handle=0 and is_delete=0
        <if test="fuwuCreateTime != null">
            and create_time >= #{fuwuCreateTime}
        </if>
        <if test="workContentType != null">
            and work_content_type=#{workContentType}
        </if>
    </select>
    <select id="getFuWuUserCurrentDayStatistics" resultType="com.shuidihuzhu.cf.clewtrack.model.CFFuWuUserCurrentDayStatisticsModel" >
        SELECT
          ct.user_id as clewUserId,
          ct.user_name as userName,
          ct.packet_id as packetId,
          IFNULL(COUNT(DISTINCT ct.clew_id),0) as currentDayAssign,
          IFNULL(sum(case when ccbi.`status`=2 and ccbi.system_handle_time > #{currentDate} then 1 else 0 end),0) as currentDayLaunchCase,
          IFNULL(sum(case when ccbi.system_handle_time > #{currentDate} and ccca.amount>0 then 1 else 0 end),0) as currentDayRMBCase
        from cf_clew_task ct
        left JOIN cf_clew_base_info ccbi on ct.clew_id=ccbi.id
        left JOIN cf_clew_case_amount ccca on ccca.info_uuid=ccbi.info_uuid
        where ct.assgin_time>#{currentDate} and ct.task_type=3 and ct.user_id!='' and ct.is_delete=0 and ct.is_recycle=0
        <if test="userIds!=null and userIds.size()>0">
            and ct.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        GROUP BY ct.user_id,ct.packet_id;
    </select>

    <select id="getIsClosedTaskByClewIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </select>

    <select id="getFuWuServicingCountGroupByUserId" resultType="java.util.Map">
        SELECT
          user_id as clewUserId,
          COUNT(1) as servicingCount
        from cf_clew_task
        where task_type=3 and task_status not in (0,6) and is_delete=0
        <if test="userIds!=null and userIds.size()>0">
            and user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        GROUP BY user_id;
    </select>
    <select id="getTaskByClewIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        SELECT <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0 and clew_id in
            <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
                #{clewId}
            </foreach>
        order by create_time desc
    </select>
    <update id="deleteTaskByTaskId">
        update <include refid="tableName"/> set is_delete=1
        where id = #{taskId}
    </update>
    <update id="batchDeleteTaskByTaskId">
        update <include refid="tableName"/> set is_delete=1
        where id in
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId}
        </foreach>
    </update>
    <update id="updateWorkOrderIdById">
        update <include refid="tableName"/>
        set work_order_id = #{workOrderId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
    </update>
    <select id="getCurrentDayAssignedByUserId" resultType="java.lang.Integer">
        select count(1) from cf_clew_task where task_type=#{taskType} and assgin_time>#{currentDate} and user_id=#{userId} and delay_handle=0 and is_delete=0
    </select>
    <select id="getFuWuRecycleTaskTotal"  resultType="java.lang.Integer">
        select count(1) from cf_clew_task where task_status!=6 and task_type=3 and delay_handle=0 and packet_id=#{packetId}  and user_id =#{userId}  and assgin_time between #{assignStartTime} and #{assignEndTime}
    </select>
    <select id="getFuWuRecycleTask" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/> where task_status!=6 and task_type=3 and delay_handle=0 and packet_id=#{packetId}  and user_id =#{userId}  and assgin_time between #{assignStartTime} and #{assignEndTime}
    </select>
    <select id="getNeedRecycleTask" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where task_type=1 and delay_handle=0 and task_status=1;
    </select>
    <select id="getNeedAutoAbandon" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where assgin_time <![CDATA[ < ]]> #{before7DayAssginTime} and task_status=2 and delay_handle=0 and task_type=1;
    </select>
    <select id="getWithin7DayLaunchedByTaskType" resultType="com.shuidihuzhu.cf.clewtrack.model.CfClewBaseResultModel">
        SELECT a.clew_id as 'clewId',a.id as 'taskId',
        b.system_handle_time as 'LaunchTime',a.user_id as 'clewUserId',
        a.user_name as 'clewUserName'
        from cf_clew_task a
        LEFT JOIN cf_clew_base_info b on a.clew_id=b.id
        where a.task_type=#{taskType}
            <if test="taskType==1">
                and b.clew_type=0
            </if>
            and b.info_id=#{infoId}
            and b.system_handle_time BETWEEN a.assgin_time and date_add(a.assgin_time, interval 7 day)
            and a.user_id!=''
        order by assgin_time desc limit 1;
    </select>
    <update id="updateSourceTypeById">
        update <include refid="tableName"/>
        set source_type = #{sourceType}
        <if test="workContentType!=null">
            ,work_content_type=#{workContentType}
        </if>
        where id = #{id}
    </update>
    <select id="getNeedAutoAssignTask" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewTaskAssignModel">
        SELECT cct.id as taskId,
        ccbi.primary_channel as primaryChannel,
        cct.task_type as taskType,
        cct.source_type as sourceType,
        ccbi.clew_type as clewType
        from cf_clew_task cct join cf_clew_base_info ccbi on cct.clew_id=ccbi.id
        where cct.task_status=0 and cct.delay_handle in (0,1) and cct.user_id='' and cct.create_time BETWEEN #{beginTime} and #{endTime}
    </select>
    <select id="getPredictiveCallPhone" resultType="com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel">
        select  cct.id as taskId,ccbi.encrypt_phone as encrypt_phone,cct.clew_id as clew_id
        from cf_clew_task cct
        join cf_clew_base_info ccbi
        on cct.clew_id=ccbi.id
        where cct.task_status = #{taskStatus}
         and cct.delay_handle = #{delayHandle}
         and cct.task_type = #{taskType}
         and cct.packet_id = #{packetId}
         and cct.assgin_time > #{startTime}
         and cct.is_delete=0
         and cct.is_closed=0
        order by cct.assgin_time desc
        limit #{importPhoneNums} ;
    </select>
    <select id="getTaskByClewIdListWithTime" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        SELECT <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0 and clew_id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
        and create_time between #{startTime} and #{endTime}
        order by create_time desc
    </select>
    <select id="getHighPriorityTaskIds" resultType="java.lang.Long">
        select id
        from cf_clew_task
        where task_status=0 and delay_handle=4 and is_delete=0 and task_type=#{taskType} and create_time>#{within7Day}
        order by priority desc,expect_last_assgin_time desc
        limit #{limit}
    </select>
    <select id="getForecastTaskByUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        SELECT <include refid="baseResult"/> from <include refid="tableName"/>
        where task_status=1 and delay_handle=0 and source_type=4 and task_type = 1
         and user_id=#{userId}
    </select>
    <select id="getCallLossClew" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        SELECT cct.id as id ,
            cct.clew_id as clew_id,
            cct.packet_id as packet_id,
            cct.create_time as create_time,
            cct.update_time as update_time,
            cct.task_status as task_status,
            cct.delay_handle as delay_handle,
            cct.task_type as task_type,
            cct.expect_last_handle_time as expect_last_handle_time,
            cct.expect_last_assgin_time as expect_last_assgin_time,
            cct.assgin_time as assgin_time,
            cct.last_handle_time as last_handle_time,
            cct.user_id as user_id,
            cct.user_name as user_name,
            cct.is_delete as is_delete,
            cct.work_order_id as work_order_id,
            cct.is_recycle as is_recycle,
            cct.source_type as source_type
        from cf_clew_task  cct
        join cf_clew_base_info ccbi
        on ccbi.id = cct.clew_id
        where cct.task_type=1 and cct.source_type=5 and cct.is_delete=0
        and ccbi.create_time >#{startTime} and ccbi.clew_type=#{clewType};
    </select>
    <select id="getFuwuTaskWithAscertainParam" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where
        clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and task_type in
        <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
        and task_status in
        <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
            #{taskStatus}
        </foreach>
        and packet_id in
        <foreach collection="packetIdList" item="packetId" open="(" separator="," close=")">
            #{packetId}
        </foreach>
        and is_delete=0
        and delay_handle=0
        <if test="userid!=null and userid!=''">
            and user_id = #{userid}
        </if>
    </select>
    <select id="getMaxTaskId" resultType="java.lang.Long">
        select max(id)
        from <include refid="tableName"/>
    </select>
    <select id="getTaskListByUpdateTime" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where update_time between #{startTime} and #{endTime} and id>#{maxId}
        order by id
        limit #{pageSize}
    </select>

    <update id="batchUpdateTaskInfo" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="packet_id =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.packetId !=null">
                        when id=#{item.id} then #{item.packetId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_status =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.taskStatus !=null">
                        when id=#{item.id} then #{item.taskStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="delay_handle =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.delayHandle !=null">
                        when id=#{item.id} then #{item.delayHandle}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_delete =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.isDelete !=null">
                        when id=#{item.id} then #{item.isDelete}
                    </if>
                </foreach>
            </trim>
            <trim prefix="source_type =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.sourceType !=null">
                        when id=#{item.id} then #{item.sourceType}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="items" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
        order by id
    </update>
    <update id="updateTaskLastHandleTime">
        update <include refid="tableName"/>
        set last_handle_time= #{lastHandleTime}
        where id=#{taskId}
    </update>
    <select id="getTaskByClewIdListAndConditions" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        SELECT <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
        and clew_id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
        and task_type = #{taskType}
        and delay_handle = #{taskDelayHandle}
        <if test="taskStatus != null and taskStatusForWaitAssign == null">
            and task_status = #{taskStatus}
        </if>
        <if test="taskStatus == null and taskStatusForWaitAssign != null">
            and task_status in
            <foreach collection="taskStatusForWaitAssign" item="filterStatus" open="(" separator="," close=")">
                #{filterStatus}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <select id="getForecastTaskIdsByCreatTime" resultType="java.lang.Long">
        select id from <include refid="tableName"/>
        where create_time BETWEEN #{startTime} AND #{endTime}
        <if test="taskStatus != null">
            and task_status = #{taskStatus}
        </if>
        <if test="taskType != null ">
            and task_type = #{taskType}
        </if>
        <if test="delayHandle != null ">
            and delay_handle = #{delayHandle}
        </if>
        <if test="sourceType != null ">
            and source_type = #{sourceType}
        </if>
         and is_delete = 0 and is_closed = 0
    </select>

    <select id="getForecastTaskUnHandleCount" resultType="java.lang.Integer">
        select count(*) as unhandleCount from <include refid="tableName"/>
        where is_delete=0 and is_closed=0
            and task_status = 6
            and task_type = 1
            and create_time > #{startTime}
            and delay_handle = #{delayHandle}
    </select>

    <select id="getNeedAutoAbandonUsePacketId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where assgin_time BETWEEN #{startTime} AND #{endTime}
        <if test="taskStatus != null">
            and task_status = #{taskStatus}
        </if>
        <if test="taskType != null ">
            and task_type = #{taskType}
        </if>
        <if test="delayHandle != null ">
            and delay_handle = #{delayHandle}
        </if>
        <if test="pakcetId != null ">
            and packet_id = #{pakcetId}
        </if>
        and is_delete = 0 and is_closed = 0
    </select>

    <select id="getClewTaskDOByClewIdAndTaskType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete = 0
        <if test="clewId != null ">
            and clew_id = #{clewId}
        </if>
        <if test="taskType != null ">
            and task_type = #{taskType}
        </if>
    </select>

    <select id="getTask4FuwuStatistic"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where task_type = 3 and user_id != '' and is_delete=0 and is_recycle =0
        and assgin_time BETWEEN #{startDate} AND #{endDate}
    </select>

	<update id="updateBusinessTagsById">
		update <include refid="tableName"/>
		set `business_tags` = #{businessTags}
		where id = #{id}
	</update>

    <select id="getTask4FuwuStatisticByUserIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where task_type = 3
        <if test="userIds!=null and userIds.size()>0">
            and user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        and packet_id = #{pakcetId}
        and assgin_time BETWEEN #{startDate} AND #{endDate}
        and is_delete=0 and is_recycle =0
    </select>


    <update id="shutDownGrabOrderTaskByTaskId">
        update
        <include refid="tableName"/>
        set task_status=6,is_closed=#{isClosed},closed_type=#{closeType}
        <if test="endReasonRemark != null and endReasonRemark != ''">
            ,end_reason_remark=#{endReasonRemark}
        </if>
        where id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <select id="listUnAssignTaskByDelayHandle" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        <where>
            <if test="taskStatus != null">
                task_status = #{taskStatus}
            </if>
            <if test="taskType != null ">
                and task_type = #{taskType}
            </if>
            <if test="delayHandle != null ">
                and delay_handle = #{delayHandle}
            </if>
            and is_delete = 0 and is_closed = 0
            <if test="startTime != null">
                and create_time &gt; #{startTime}
            </if>
        </where>
    </select>
    <select id="getUnHandleTaskListForUser" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        <where>
            user_id=#{userid}
            and create_time between #{startTime} and #{endTime}
            and packet_id=#{packetId}
            and is_delete = 0 and is_closed = 0
            and delay_handle = 0
            and task_status in
            <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")" >
                #{taskStatus}
            </foreach>
        </where>
    </select>

    <select id="getCanAssignTaskList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
            and packet_id = #{pakcetId}
            and task_status in <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">#{taskStatus}</foreach>
            and task_type = #{taskType}
            and delay_handle = #{delayHandle}
            and create_time > #{startDate}
            and work_content_type = #{workContentType}
            and is_closed = 0
            and source_type not in
        <foreach collection="recallSourceTypes" item="recallSourceType" open="(" close=")" separator=",">#{recallSourceType}</foreach>
        order by create_time desc
        limit #{limit}
    </select>

    <select id="getFuwuCanAssignTaskListNotInEagleHandles" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
            and packet_id = #{pakcetId}
            and task_status in <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">#{taskStatus}</foreach>
            and task_type = #{taskType}
            and delay_handle = #{delayHandle}
            and create_time > #{startDate}
            and create_time <![CDATA[ <= ]]> #{endDate}
            and work_content_type = #{workContentType}
            and is_closed = 0
            and eagle_handle not in <foreach collection="eagleHandles" item="eagleHandle" open="(" close=")" separator=",">#{eagleHandle}</foreach>
        order by create_time desc
        limit #{limit}
    </select>
    <select id="getFuwuCanAssignTaskListInEagleHandles" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
            and packet_id = #{pakcetId}
            and task_status in <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">#{taskStatus}</foreach>
            and task_type = #{taskType}
            and delay_handle = #{delayHandle}
            and create_time > #{startDate}
            and work_content_type = #{workContentType}
            and is_closed = 0
            and eagle_handle in <foreach collection="eagleHandles" item="eagleHandle" open="(" close=")" separator=",">#{eagleHandle}</foreach>
        order by create_time desc
        limit #{limit}
    </select>
    <select id="getNoWorkbenchType" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0 and workbench_type=-1 and id>#{id}
        limit 100
    </select>
    <update id="batchUpdateWorkbenchType">
        update <include refid="tableName"/>
        set workbench_type = case id
        <foreach collection="cfClewTasks" item="cfClewTask">
            when #{cfClewTask.id} then #{cfClewTask.workbenchType}
        </foreach>
        end
        where id in
        <foreach collection="cfClewTasks" item="cfClewTask" open="(" close=")" separator=",">
            #{cfClewTask.id}
        </foreach>
    </update>
    <select id="getFuwuCanAssignClewLayerTaskListNotInEagleHandles"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
        and packet_id = #{pakcetId}
        and task_status in <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">#{taskStatus}</foreach>
        and task_type = #{taskType}
        and delay_handle = #{delayHandle}
        and create_time > #{startDate}
        and clew_layer_name = #{clewLayerName}
        and is_award = #{isAward}
        and is_closed = 0
        and clew_layer_config_id = #{layerConfigId}
        and eagle_handle not in <foreach collection="eagleHandles" item="eagleHandle" open="(" close=")" separator=",">#{eagleHandle}</foreach>
        order by create_time desc
        limit #{limit}
    </select>

    <select id="getCanAssignClewLayerTaskList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
        and packet_id = #{pakcetId}
        and task_status in <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">#{taskStatus}</foreach>
        and task_type = #{taskType}
        and delay_handle = #{delayHandle}
        and create_time > #{startDate}
        and clew_layer_name = #{clewLayerName}
        and is_award = #{isAward}
        and is_closed = 0
        and clew_layer_config_id = #{layerConfigId}
        and source_type not in
        <foreach collection="recallSourceTypes" item="recallSourceType" open="(" close=")" separator=",">#{recallSourceType}</foreach>
        order by create_time desc
        limit #{limit}
    </select>

    <update id="updateScoreById">
        update <include refid="tableName"/>
        set score = #{score}
        where id = #{id}
    </update>

    <select id="getShutDownTaskIsByClewIdAndTaskType" resultType="java.lang.Long">
        select id from <include refid="tableName"/>
        where delay_handle=0
        and clew_id in
        <foreach collection="clewIdList" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and task_status in
        <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
            #{taskStatus}
        </foreach>
        and task_type in
        <foreach collection="taskTypeList" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
    </select>
    <select id="getClewIdByClewIdWithUserId" resultType="java.lang.Long">
        select clew_id from <include refid="tableName"/>
        where clew_id in <foreach collection="clewIdList" item="clewId" open="(" separator="," close=")"> #{clewId} </foreach>
        and is_delete=0 and user_id=#{clewUserId}
        limit 1
    </select>

    <select id="listFuwuTaskByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIdList" item="clewId" open="(" separator="," close=")">
         #{clewId}
        </foreach>
        and is_delete=0 and task_type = 3
    </select>

    <update id="updateEagelHandleById">
        update <include refid="tableName"/>
        set eagle_handle = #{eagleHandle}
        where id = #{id}
    </update>

    <select id="listCanAssignTask" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
        and packet_id = #{pakcetId}
        and task_status in
        <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
            #{taskStatus}
        </foreach>
        and task_type = #{taskType}
        and delay_handle = #{delayHandle}
        and create_time > #{startDate}
        and work_content_type = #{workContentType}
        and eagle_handle = #{eagleHandle}
        order by priority asc,create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="getTaskByTimeAndUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and user_id in
        <foreach collection="userIds" item="userId" close=")" open="(" separator=",">
            #{userId}
        </foreach>
        and task_type = 3
        and assgin_time between #{startTime} and #{endTime}
    </select>

    <select id="getTaskByTimeRange" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and create_time between #{startTime} and #{endTime}
        and user_id in
        <foreach collection="userIds" open="(" close=")" separator="," item="userId">
            #{userId}
        </foreach>
        and task_type = #{taskType}
    </select>


    <select id="listByAssignTime" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and assgin_time between #{assignStartTime} and #{assignEndTime}
        and task_type = #{taskType}
        and task_status = #{taskStatus}
    </select>

    <select id="getHasClosedTaskByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_closed = #{isClosed}
        and closed_type in
        <foreach collection="closedTypeList" item="closedType" separator="," close=")" open="(">
            #{closedType}
        </foreach>
        and clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </select>

    <select id="getErrorCountUserIdList" resultType="java.lang.String">
        select DISTINCT user_id
        from <include refid="tableName"/>
        where create_time > #{createTime}
        and task_status not in
        <foreach collection="taskStatusList" item="taskStatus" separator="," close=")" open="(">
            #{taskStatus}
        </foreach>
        <if test="userIdList != null and userIdList.size() > 0">
            and user_id in
            <foreach collection="userIdList" item="userId" separator="," close=")" open="(">
                #{userId}
            </foreach>
        </if>
        and packet_id = #{packetId}
        and is_delete = 0
    </select>

</mapper>
