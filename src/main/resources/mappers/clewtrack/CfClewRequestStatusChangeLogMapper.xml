<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewRequestStatusChangeLogDao">
    <sql id="tableName">cf_clew_request_status_change_log</sql>

    <sql id="baseResult">
        id,
        clew_id,
        create_time,
        update_time ,
        request_status_type,
        source_request_status,
        target_request_status,
        is_delete
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRequestStatusChangeLogDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="clewId != null" >
                clew_id,
            </if>
            <if test="requestStatusType != null" >
                request_status_type,
            </if>
            <if test="sourceRequestStatus != null" >
                source_request_status,
            </if>
            <if test="targetRequestStatus != null" >
                target_request_status,
            </if>
            <if test="requestReason != null and requestReason!=''" >
                request_reason,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="clewId != null" >
                #{clewId},
            </if>
            <if test="requestStatusType != null" >
                #{requestStatusType},
            </if>
            <if test="sourceRequestStatus != null" >
                #{sourceRequestStatus},
            </if>
            <if test="targetRequestStatus != null" >
                #{targetRequestStatus},
            </if>
            <if test="requestReason != null and requestReason!=''" >
                #{requestReason},
            </if>
        </trim>
    </insert>

</mapper>