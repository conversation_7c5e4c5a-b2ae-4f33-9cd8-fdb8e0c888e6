<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.myclewreminder.CfClewReminderRecordDao">

    <!-- 保存线索提醒记录 -->
    <insert id="save" parameterType="com.shuidihuzhu.cf.clewtrack.model.myclewreminder.CfClewReminderRecord" keyProperty="id" useGeneratedKeys="true">
        INSERT ignore INTO cf_clew_reminder_record (
        mis, task_id, clew_id, service_stage, day_key, batch_num, msg
        <if test="expectedTimeoutTime != null">
            , expected_timeout_time
        </if>
        ) VALUES (
        #{mis}, #{taskId}, #{clewId}, #{serviceStage}, #{dayKey}, #{batchNum}, #{msg}
        <if test="expectedTimeoutTime != null">
            , #{expectedTimeoutTime}
        </if>
        )
    </insert>
    <update id="updateExpectedTimeoutTimeById">
        update cf_clew_reminder_record
        set expected_timeout_time = #{expectedTimeoutTime}
        where id = #{id}
    </update>

    <select id="countByMisTaskIdServiceStageDayKeyAndExpectedTimeoutTime" resultType="java.lang.Integer">
        select count(1)
        from cf_clew_reminder_record
        where mis = #{mis}
        and task_id = #{taskId}
        and service_stage = #{serviceStage}
        and day_key = #{dayKey}
        <if test="lastFollowUpTime!= null">
           and expected_timeout_time > #{lastFollowUpTime}
        </if>
        and expected_timeout_time &lt;= #{expectedTimeoutTime}
    </select>

    <select id="getSendRecordByTaskIdAndStage" resultType="com.shuidihuzhu.cf.clewtrack.model.myclewreminder.CfClewReminderRecord">
        select *
        from cf_clew_reminder_record
        where task_id = #{taskId}
        and service_stage = #{serviceStage}
    </select>
</mapper>
