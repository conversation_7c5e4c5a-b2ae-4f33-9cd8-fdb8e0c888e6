<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCaseAmountDao">

    <sql id="tableName">cf_clew_case_amount</sql>

    <insert id="insert">
        insert into <include refid="tableName"/>
        (info_uuid,amount,create_time,info_id)
        values (#{infoUuid},#{amount},now(),#{infoId})
    </insert>

    <update id="updateAmount">
        update <include refid="tableName"/>
        set amount = #{amount}
        where info_uuid = #{infoUuid}
    </update>
    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCaseAmountDO">
        select id,info_uuid,amount,info_id
        from <include refid="tableName"/>
        where info_uuid = #{infoUuid}
    </select>
</mapper>
