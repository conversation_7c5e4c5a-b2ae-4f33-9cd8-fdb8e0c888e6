<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfFundraiseDemandDao">
    <sql id="tableName">cf_fundraise_demand</sql>

    <sql id="fileds">
        `id`,
        `person_id`,
        `uuid`,
        `clew_id`,
        `source_type`,
        `valid_time`,
        `demand_level`,
        `primary_channel`,
        `owner_type`,
        `demand_type`,
        `demand_close_type`,
        `work_bench_type`,
        `order_task_id`,
        `order_task_status`,
        `handle_team`,
        `operator_org`,
        `operator_mis`,
        `order_create_time`,
        `order_assign_time`,
        `encrypt_phone`,
        `encrypt_second_phone`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfFundraiseDemandDO">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="personId!=null and personId!=''">
                person_id,
            </if>
            <if test="uuid != null and uuid != ''">
                uuid,
            </if>
            <if test="clewId != null">
                clew_id,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="validTime !=null">
                valid_time,
            </if>
            <if test="demandLevel !=null ">
                demand_level,
            </if>
            <if test="primaryChannel != null and primaryChannel != ''">
                primary_channel,
            </if>
            <if test="ownerType != null">
                owner_type,
            </if>
            <if test="demandType != null">
                demand_type,
            </if>
            <if test="demandCloseType != null">
                demand_close_type,
            </if>
            <if test="workBenchType != null">
                work_bench_type,
            </if>
            <if test="orderTaskId != null">
                order_task_id,
            </if>
            <if test="orderTaskStatus != null">
                order_task_status,
            </if>
            <if test="handleTeam != null and handleTeam != ''">
                handle_team,
            </if>
            <if test="operatorOrg != null and operatorOrg != ''">
                operator_org,
            </if>
            <if test="operatorMis != null and operatorMis != ''">
                operator_mis,
            </if>
            <if test="orderCreateTime != null">
                order_create_time,
            </if>
            <if test="orderAssignTime != null">
                order_assign_time,
            </if>
            <if test="encryptPhone != null">
                encrypt_phone,
            </if>
            <if test="encryptSecondPhone != null">
                encrypt_second_phone,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="personId!=null and personId!=''">
                #{personId},
            </if>
            <if test="uuid != null and uuid != ''">
                #{uuid},
            </if>
            <if test="clewId != null">
                #{clewId} ,
            </if>
            <if test="sourceType != null">
                #{sourceType} ,
            </if>
            <if test="validTime!=null">
                #{validTime} ,
            </if>
            <if test="demandLevel!=null">
                #{demandLevel} ,
            </if>
            <if test="primaryChannel != null and primaryChannel != ''">
                #{primaryChannel} ,
            </if>
            <if test="ownerType != null">
                #{ownerType} ,
            </if>
            <if test="demandType != null">
                #{demandType} ,
            </if>
            <if test="demandCloseType != null">
                #{demandCloseType} ,
            </if>
            <if test="workBenchType != null">
                #{workBenchType} ,
            </if>
            <if test="orderTaskId != null">
                #{orderTaskId} ,
            </if>
            <if test="orderTaskStatus != null">
                #{orderTaskStatus} ,
            </if>
            <if test="handleTeam != null and handleTeam != ''">
                #{handleTeam} ,
            </if>
            <if test="operatorOrg != null and operatorOrg != ''">
                #{operatorOrg} ,
            </if>
            <if test="operatorMis != null and operatorMis != ''">
                #{operatorMis} ,
            </if>
            <if test="orderCreateTime != null">
                #{orderCreateTime} ,
            </if>
            <if test="orderAssignTime != null">
                #{orderAssignTime} ,
            </if>
            <if test="encryptPhone != null">
                #{encryptPhone} ,
            </if>
            <if test="encryptSecondPhone != null">
                #{encryptSecondPhone} ,
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfFundraiseDemandDO">
        update <include refid="tableName"/>
        <set>
            <if test="personId!=null and personId!=''">
                person_id = #{personId} ,
            </if>
            <if test="uuid != null and uuid != ''">
                uuid = #{uuid} ,
            </if>
            <if test="clewId != null">
                clew_id = #{clewId} ,
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType} ,
            </if>
            <if test="validTime!=null">
                valid_time=#{validTime} ,
            </if>
            <if test="demandLevel!=null">
                demand_level = #{demandLevel} ,
            </if>
            <if test="primaryChannel != null and primaryChannel != ''">
                primary_channel = #{primaryChannel} ,
            </if>
            <if test="ownerType != null">
                owner_type = #{ownerType} ,
            </if>
            <if test="demandType != null">
                demand_type = #{demandType} ,
            </if>
            <if test="demandCloseType != null">
                demand_close_type = #{demandCloseType} ,
            </if>
            <if test="workBenchType != null">
                work_bench_type = #{workBenchType} ,
            </if>
            <if test="orderTaskId != null">
                order_task_id = #{orderTaskId} ,
            </if>
            <if test="orderTaskStatus != null">
                order_task_status = #{orderTaskStatus} ,
            </if>
            <if test="handleTeam != null">
                handle_team = #{handleTeam} ,
            </if>
            <if test="operatorOrg != null">
                operator_org = #{operatorOrg} ,
            </if>
            <if test="operatorMis != null">
                operator_mis = #{operatorMis} ,
            </if>
            <if test="orderCreateTime != null">
                order_create_time = #{orderCreateTime} ,
            </if>
            <if test="orderAssignTime != null">
                order_assign_time = #{orderAssignTime} ,
            </if>
        </set>
        where id=#{id}
    </update>

    <select id="getFundraiseDemandByuuid"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfFundraiseDemandDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where is_delete=0 and demand_close_type = 0
        and uuid = #{uuid}
        order by id desc limit 1
    </select>
    <select id="getFundraiseDemandByPersonId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfFundraiseDemandDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where is_delete=0 and demand_close_type = 0
        and person_id = #{personId}
        order by id desc limit 1
    </select>

    <update id="updateWaihuByTaskIds">
        update <include refid="tableName"/>
        set order_task_status = #{orderTaskStatus} ,
            owner_type = #{ownerType}
        where order_task_id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <update id="updateQianzhiByTaskIds">
        update <include refid="tableName"/>
        set order_task_status = #{orderTaskStatus} ,
        owner_type = #{ownerType},
        demand_close_type = #{closeType}
        where order_task_id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>
    <update id="updateByClewIds">
        update <include refid="tableName"/>
        set order_task_status = #{orderTaskStatus} ,
        owner_type = #{ownerType},
        demand_close_type = #{closeType}
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </update>

    <update id="updateUuIdByPersonId">
        update <include refid="tableName"/>
        set uuid = #{uuid}
        where person_id = #{personId}
    </update>

    <select id="getFundraiseDemandCountByValidTime" resultType="java.lang.Integer">
        SELECT count(*)
        from <include refid="tableName"/>
        where valid_time = #{currentDate}
        and demand_close_type = #{closeType}
        and demand_type = 0
        and is_delete = 0
    </select>

    <select id="getFundraiseDemandByValidTime"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfFundraiseDemandDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where valid_time = #{currentDate}
        and demand_close_type = #{closeType}
        and demand_type = 0
        and is_delete = 0
        order by id desc
        limit #{offset},#{pageSize}
    </select>
    <update id="updateDemandInvalid">
        update <include refid="tableName"/>
        set demand_close_type = #{closeType}
        where id in
        <foreach collection="items" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="updateQianzhiByClewIds">
        update <include refid="tableName"/>
        set order_task_status = #{orderTaskStatus} ,
        owner_type = #{ownerType},
        demand_close_type = #{closeType}
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </update>
    <update id="updateWaihuByClewIds">
        update <include refid="tableName"/>
        set order_task_status = #{orderTaskStatus} ,
        owner_type = #{ownerType}
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </update>

    <update id="updateEncryptPhoneByClewIds">
        update <include refid="tableName"/>
        set encrypt_second_phone = #{encryptSecondPhone}
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </update>

    <select id="listFundraiseDemandByTime"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfFundraiseDemandDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where create_time between #{queryDate} and #{endDate}
    </select>

    <update id="updatePhone" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfFundraiseDemandDO">
        update <include refid="tableName"/>
        <set>
            <if test="encryptPhone != null">
                encrypt_phone = #{encryptPhone},
            </if>
            <if test="encryptSecondPhone != null">
                encrypt_second_phone = #{encryptSecondPhone},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateDeleteFundraiseDemand">
        update <include refid="tableName"/>
        set is_delete = 1
        where is_delete = 0
        <if test="personIdList != null and personIdList.size() > 0">
            or person_id in
            <foreach collection="personIdList" item="personId" open="(" separator="," close=")">
                #{personId}
            </foreach>
        </if>
        <if test="uuidList != null and uuidList.size() > 0">
            or uuid in
            <foreach collection="uuidList" item="uuid" open="(" separator="," close=")">
                #{uuid}
            </foreach>
        </if>
    </update>
</mapper>
