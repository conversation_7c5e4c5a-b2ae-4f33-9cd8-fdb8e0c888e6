<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewNoticeRecordsDao">
  <sql id="tableFiled">
    id, task_id, user_id, notice_content, is_delete, create_time, update_time,status,`type`
  </sql>

  <sql id="tableName">
    cf_clew_notice_records
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewNoticeRecordsDO">
    select
    <include refid="tableFiled" />
    from <include refid="tableName"/>
    where id = #{id}
  </select>
  <select id="selectNoticeByPage" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewNoticeRecordsDO">
    select <include refid="tableFiled"/>
    from <include refid="tableName"/>
    where
    user_id = #{clewUserId} and status=0
    <if test="startTime!=null">
      and create_time>= #{startTime}
    </if>
    order by create_time desc
    limit #{offSet} ,#{pageSize}
  </select>
  <select id="selectCountNoticeByUserId" resultType="java.lang.Integer">
    select count(*)
    from <include refid="tableName"/>
    where
    user_id = #{clewUserId} and status=0
    <if test="startTime!=null">
      and create_time>= #{startTime}
    </if>
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewNoticeRecordsDO" useGeneratedKeys="true">
    insert into <include refid="tableName"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        task_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="noticeContent != null">
        notice_content,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskId != null">
        #{taskId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="noticeContent != null">
        #{noticeContent},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="type != null">
        #{type},
      </if>
      <if test="status != null">
        #{status},
      </if>
    </trim>
  </insert>

  <update id="updateStatus">
    update <include refid="tableName"/> set status=1 where task_id = #{taskId} and `type` = #{type} and is_delete = 0
  </update>

  <insert id="batchInsertSelective">
      insert into <include refid="tableName"/>
        (task_id, user_id, notice_content, `type`)
      values
      <foreach collection="records" item="record" separator=",">
        (#{record.taskId},#{record.userId},#{record.noticeContent},#{record.type})
      </foreach>
  </insert>
</mapper>
