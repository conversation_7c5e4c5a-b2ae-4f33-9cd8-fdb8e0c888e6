<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewSmartWordDao">

    <sql id="baseResult">
        `id`,
        `class_id`,
        `descs`,
	    `clew_user_id`,
	    `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <sql id="tableName">cf_clew_smartword</sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordVO"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`class_id`,`descs`,`clew_user_id`,`create_time`,`update_time`)
        values
        (#{classId}, #{descs}, #{clewUserId}, sysdate(), sysdate())
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordVO">
        update <include refid="tableName"/>
        <set>
            <if test="descs != null and descs !=''">
                descs = #{descs} ,
            </if>
            <if test="clewUserId != null and clewUserId != ''">
                clew_user_id = #{clewUserId},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteByClassIds">
        update <include refid="tableName"/>
        set is_delete = 1
        where class_id in
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
    </update>

    <update id="delete">
        update <include refid="tableName"/>
        set is_delete = 1
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="sortSmartWordVo">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="descs =case" suffix="end,">
                <foreach collection="sortedVOList" item="item" index="index">
                    <if test="item.descs !=null">
                        when id=#{item.id} then #{item.descs}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="sortedVOList" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="getSmartWordListByClassIds" resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordVO">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and class_id in
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        order by id desc
        <if test="limit != null">
        limit #{limit}
        </if>
    </select>

    <select id="getSmartWordByIds" resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordVO">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by id desc
    </select>

    <select id="getCountByQueryParam" resultType="java.lang.Integer"
            parameterType="com.shuidihuzhu.cf.clewtrack.param.CfSmartVordQueryParam">
        SELECT count(*)
        from <include refid="tableName"/>
        where is_delete=0
        <if test="classId != null ">
            and class_id = #{classId}
        </if>
    </select>

    <select id="getSmartWordListByQueryParam" resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordVO"
            parameterType="com.shuidihuzhu.cf.clewtrack.param.CfSmartVordQueryParam">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        <if test="classId != null ">
            and class_id = #{classId}
        </if>
        order by id desc
        limit #{offset},#{pageSize}
    </select>

    <select id="fuzzyQuerySmartword" resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordVO">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        <if test="fuzzyWord != null and fuzzyWord != ''">
            and descs like concat('%',#{fuzzyWord},'%')
        </if>
        order by id desc
        limit #{pageSize}
    </select>

</mapper>
