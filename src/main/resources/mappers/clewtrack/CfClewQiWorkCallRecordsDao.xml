<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewQiWorkCallRecordsDao">

    <sql id="tableName">
        cf_clew_qi_work_call_records
    </sql>
    
    <sql id="selectFields">
        id,
        user_id,
        qi_cno,
        clew_id,
        task_id,
        record_file,
        phone_ring_time,
        phone_start_time,
        phone_end_time,
        phone_status,
        phone_out_number,
        phone_in_number,
        user_nick,
        satisfaction,
        is_delete,
        create_time,
        update_time,
        business_line,
        business_no,
        app_code,
        call_result,
        client_name,
        org_name,
        cos_file,
        phone_call_type,
        record_ext
    </sql>

    <sql id="insertFiles">
        user_id,
        record_file,
        phone_ring_time,
        phone_start_time,
        phone_end_time,
        phone_status,
        phone_out_number,
        phone_in_number,
        user_nick,
        phone_call_type,
        cos_file
    </sql>
    

    <!--查询单个-->
    <select id="queryById" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewQiWorkCallRecordDO">
        select
          <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and id = #{id}
    </select>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="qiCno != null and qiCno != ''">
                qi_cno,
            </if>
            <if test="clewId != null">
                clew_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="recordFile != null and recordFile != ''">
                record_file,
            </if>
            <if test="phoneRingTime != null and phoneRingTime != ''">
                phone_ring_time,
            </if>
            <if test="phoneStartTime != null and phoneStartTime != ''">
                phone_start_time,
            </if>
            <if test="phoneEndTime != null and phoneEndTime != ''">
                phone_end_time,
            </if>
            <if test="phoneStatus != null">
                phone_status,
            </if>
            <if test="phoneOutNumber != null and phoneOutNumber != ''">
                phone_out_number,
            </if>
            <if test="phoneInNumber != null and phoneInNumber != ''">
                phone_in_number,
            </if>
            <if test="userNick != null and userNick != ''">
                user_nick,
            </if>
            <if test="satisfaction != null">
                satisfaction,
            </if>
            <if test="businessLine != null">
                business_line,
            </if>
            <if test="businessNo != null">
                business_no,
            </if>
            <if test="appCode != null and appCode != ''">
                app_code,
            </if>
            <if test="callResult != null and callResult != ''">
                call_result,
            </if>
            <if test="clientName != null and clientName != ''">
                client_name,
            </if>
            <if test="orgName != null and orgName != ''">
                org_name,
            </if>
            <if test="phoneCallType != null">
                phone_call_type,
            </if>
        </trim>
        values 
         <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
              #{userId},
            </if>
            <if test="qiCno != null and qiCno != ''">
              #{qiCno},
            </if>
            <if test="clewId != null">
              #{clewId},
            </if>
            <if test="taskId != null">
              #{taskId},
            </if>
            <if test="recordFile != null and recordFile != ''">
              #{recordFile},
            </if>
            <if test="phoneRingTime != null and phoneRingTime != ''">
              #{phoneRingTime},
            </if>
            <if test="phoneStartTime != null and phoneStartTime != ''">
              #{phoneStartTime},
            </if>
            <if test="phoneEndTime != null and phoneEndTime != ''">
              #{phoneEndTime},
            </if>
            <if test="phoneStatus != null">
              #{phoneStatus},
            </if>
            <if test="phoneOutNumber != null and phoneOutNumber != ''">
              #{phoneOutNumber},
            </if>
            <if test="phoneInNumber != null and phoneInNumber != ''">
              #{phoneInNumber},
            </if>
            <if test="userNick != null and userNick != ''">
              #{userNick},
            </if>
            <if test="satisfaction != null">
              #{satisfaction},
            </if>
            <if test="businessLine != null">
              #{businessLine},
            </if>
            <if test="businessNo != null">
              #{businessNo},
            </if>
            <if test="appCode != null and appCode != ''">
              #{appCode},
            </if>
            <if test="callResult != null and callResult != ''">
              #{callResult},
            </if>
            <if test="clientName != null and clientName != ''">
             #{clientName},
            </if>
            <if test="orgName != null and orgName != ''">
             #{orgName},
            </if>
            <if test="phoneCallType != null">
              #{phoneCallType},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        (<include refid="insertFiles"/>)
        values
        <foreach collection="cfClewQiWorkCallRecordDOList" item="record" separator=",">
            (#{record.userId}, #{record.recordFile}, #{record.phoneRingTime}, #{record.phoneStartTime},
            #{record.phoneEndTime}, #{record.phoneStatus}, #{record.phoneOutNumber}, #{record.phoneInNumber},
            #{record.userNick}, #{record.phoneCallType}, #{record.cosFile})
        </foreach>
    </insert>


    <!--通过主键修改数据-->
    <update id="updateRecordInfo">
        update <include refid = "tableName"/>
        <set>
            <if test="recordFile != null and recordFile != ''">
                record_file = #{recordFile},
            </if>
            <if test="phoneRingTime != null and phoneRingTime != ''">
                phone_ring_time = #{phoneRingTime},
            </if>
            <if test="phoneStartTime != null and phoneStartTime != ''">
                phone_start_time = #{phoneStartTime},
            </if>
            <if test="phoneEndTime != null and phoneEndTime != ''">
                phone_end_time = #{phoneEndTime},
            </if>
            <if test="phoneStatus != null">
                phone_status = #{phoneStatus},
            </if>
            <if test="phoneInNumber != null and phoneInNumber != ''">
                phone_in_number = #{phoneInNumber},
            </if>
            <if test="userNick != null and userNick != ''">
                user_nick = #{userNick},
            </if>
            <if test="satisfaction != null">
                satisfaction = #{satisfaction},
            </if>
            <if test="callResult != null and callResult != ''">
                call_result = #{callResult},
            </if>
            <if test="cosFile != null and cosFile != ''">
                cos_file = #{cosFile},
            </if>
            <if test="recordExt != null">
                record_ext = #{recordExt},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="listNoCallFile" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewQiWorkCallRecordDO">
        select
        <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and create_time between #{startTime} and #{endTime} and record_file = ""
    </select>

    <select id="listCallRecords" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewQiWorkCallRecordDO">
        select
        <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete=0
        <if test="queryParam.clewId != null"> and clew_id=#{queryParam.clewId}</if>
        <if test="queryParam.taskId != null"> and task_id=#{queryParam.taskId}</if>
        <if test="encryptPhone != '' and encryptPhone != null">
            and phone_out_number = #{encryptPhone}
        </if>
        <if test="queryParam.tab !=0">
            and phone_status = 1
        </if>
        <if test="queryParam.excludePhones != null and queryParam.excludePhones.size() > 0">
            and phone_out_number not in
            <foreach collection="queryParam.excludePhones" item="excludePhone" open="(" separator="," close=")">
                #{excludePhone}
            </foreach>
        </if>
        and create_time BETWEEN #{queryParam.dateQueryParam.startTime} AND #{queryParam.dateQueryParam.endTime}
    </select>

    <select id="listCallRecordsByMisAndPhone" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewQiWorkCallRecordDO">
        select
        <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete=0 and user_id=#{userId}
        <if test="encryptSecondPhone != null and encryptSecondPhone != ''">
            and phone_out_number in (#{encryptPhone},#{encryptSecondPhone})
        </if>
        <if test="encryptSecondPhone == null or encryptSecondPhone == ''">
            and phone_out_number = #{encryptPhone}
        </if>
        and create_time <![CDATA[ >= ]]> #{taskAssignDate}
    </select>

    <select id="listCallRecordsByTaskId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewQiWorkCallRecordDO">
        select
        <include refid = "selectFields"/>
        from <include refid = "tableName"/>
        where is_delete = 0 and task_id = #{taskId}
    </select>
</mapper>

