<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewSmartWordCategoryDao">

    <sql id="baseResult">
        `id`,
        `parent_id`,
        `descs`,
        `sort_id`,
	    `clew_user_id`,
	    `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <sql id="tableName">cf_clew_smartword_category</sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordCategoryVO"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`parent_id`,`descs`,`clew_user_id`,`create_time`,`update_time`
        )
        values (#{parentId} ,#{descs} ,#{clewUserId} ,sysdate(),sysdate())
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordCategoryVO">
        update <include refid="tableName"/>
        <set>
            <if test="descs != null and descs != ''">
                descs = #{descs} ,
            </if>
            <if test="clewUserId != null and clewUserId != ''">
                clew_user_id = #{clewUserId},
            </if>
            <if test="sortId != null">
                sort_id = #{sortId} ,
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteByIds">
        update <include refid="tableName"/>
        set is_delete = 1
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="sortWordCategory">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="sort_id =case" suffix="end,">
                <foreach collection="sortedCategorys" item="item" index="index">
                    <if test="item.sortId !=null">
                        when id=#{item.id} then #{item.sortId}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="sortedCategorys" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateParentId">
        update <include refid="tableName"/>
        set parent_id = #{parentId}
        where id in
        <foreach collection="items" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <select id="getSmartWordCategoryByParentId"
            resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordCategoryVO">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0 and parent_id = #{parentId}
        order by id desc
    </select>

    <select id="getSmartWordCategoryByParentIds"
            resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordCategoryVO">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and parent_id in
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
        order by id desc
    </select>

    <select id="getSmartWordCategoryByIds"
            resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfSmartWordCategoryVO">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by id desc
    </select>

</mapper>
