<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfTaskCaseDetailDao">

    <sql id="baseResult">
        `id`,
        `user_id`,
        `task_case_statistics_id`,
        `task_id`,
        `clew_id`,
        `case_id`,
        `info_uuid`,
        `type`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <sql id="tableName">cf_task_case_detail</sql>
    <sql id="cfCaseServiceInfo">
        user_id,
        clew_id,
        case_id
    </sql>

    <select id="getCfCaseServiceInfoList" resultType="com.shuidihuzhu.cf.clewtrack.domain.casestat.CfCaseServiceInfo">
        select <include refid="cfCaseServiceInfo"/> from <include refid="tableName"/>
        where task_case_statistics_id in <foreach collection="searchModel.ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
        and `type`=#{searchModel.taskCaseStatisticsType} and is_delete=0
        order by create_time
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`task_case_statistics_id`,`user_id`,`user_name`,`task_id`,`clew_id`,`case_id`,`info_uuid`,`type`)
        values
        (#{taskCaseStatisticsId},#{userId},#{userName},#{taskId},#{clewId},#{caseId},#{infoUuid},#{type})
    </insert>

    <select id="getByCaseIdAndUserIdAndTypes" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfTaskCaseDetailDO">
        select <include refid="cfCaseServiceInfo"/> from <include refid="tableName"/>
        where case_id = #{caseId} and is_delete=0 and user_id=#{userId} and `type` in <foreach collection="types" item="type" open="(" close=")" separator=",">#{type}</foreach>
        limit 1
    </select>

    <select id="getByUserIdAndTaskIdAndType" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfTaskCaseDetailDO">
        select <include refid="cfCaseServiceInfo"/> from <include refid="tableName"/>
        where user_id = #{userId} and is_delete=0 and task_id = #{taskId} and `type`=#{type}
        limit 1
    </select>
</mapper>
