<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.OuterOrgBindInnerDao">
    <sql id="tableName">outer_org_bind_inner</sql>

    <sql id="fields">
        outer_org_id,
        outer_org_name,
        inner_org_id,
        inner_org_name,
        create_time,
        update_time,
        is_delete
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.OuterOrgBindInnerDO">
        insert into <include refid="tableName"/>
        (outer_org_id, outer_org_name, inner_org_id, inner_org_name)
        value
        (#{outerOrgId},#{outerOrgName},#{innerOrgId},#{innerOrgName})
    </insert>

    <select id="getByOuterOrgId" resultType="com.shuidihuzhu.cf.clewtrack.domain.OuterOrgBindInnerDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and outer_org_id = #{outerOrgId}
        limit 1
    </select>

    <select id="listByInnerOrgIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.OuterOrgBindInnerDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and inner_org_id in
        <foreach collection="innerOrgIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="listAllBindInfo" resultType="com.shuidihuzhu.cf.clewtrack.domain.OuterOrgBindInnerDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
    </select>
</mapper>
