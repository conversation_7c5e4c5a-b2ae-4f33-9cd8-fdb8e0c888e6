<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewFollowUpTaskDao">

    <sql id="baseResult">
        `id`,
        `task_id`,
        `user_id`,
        `event_type`,
        `handle_status`,
        `service_status`,
        `service_phase`,
        `wechat_pass`,
        `first_tag`,
        `second_tag`,
        `service_mode`,
        `comment`,
        `remind_time`,
        `operator`,
        `handle_time`,
        `info_id`,
        `case_status`,
        `first_approve_status`,
        `amount`,
        `share_count`,
        `is_delete`,
        `create_time`,
        `update_time`,
        `case_initial_time`,
        `day_key`,
        `encrypt_phone`,
        `encrypt_second_phone`,
        `work_content_type`,
        `call_num`,
        `phone_status`,
        `workbench_type`,
        `inspect_result`,
        `donate_count`,
        `submit_donate_count`,
        `submit_amount`,
        `submit_share_count`,
        `proxy_user_id`,
        `expect_handle_time`,
        `notify_charge`,
        `agree_charge`,
        `city`,
        `wx_friend_num`,
        `raiser_profession`,
        `patient_profession`,
        `other_profession`,
        `other_relation`,
        `is_pull_group`,
        `is_minority`,
        `call_num_status`,
        `guide_result`,
        `next_follow_time`,
        `focus_case`,
        `major_clew`,
        `major_disease`,
        `follow_task_type`
    </sql>

    <sql id="tableName">cf_clew_follow_up_task</sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into <include refid="tableName"/>
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="taskId!=null">
            task_id,
        </if>
        <if test="userId!=null and userId!=''">
            user_id,
        </if>
        <if test="eventType != null">
            event_type,
        </if>
        <if test="handleStatus != null">
            handle_status,
        </if>
        <if test="serviceStatus != null" >
            service_status,
        </if>
        <if test="servicePhase != null ">
            service_phase,
        </if>
        <if test="wechatPass != null ">
            wechat_pass,
        </if>
        <if test="firstTag != null ">
            first_tag,
        </if>
        <if test="secondTag !=null">
            second_tag,
        </if>
        <if test="serviceMode !=null">
            service_mode,
        </if>
        <if test="comment !=null">
            comment,
        </if>
        <if test="remindTime !=null">
            remind_time,
        </if>
        <if test="operator != null and operator != ''">
            operator,
        </if>
        <if test="handleTime !=null">
            handle_time,
        </if>
        <if test="infoId!=null and infoId!=''">
            info_id,
        </if>
        <if test="caseStatus !=null">
            case_status,
        </if>
        <if test="firstApproveStatus!=null">
            first_approve_status,
        </if>
        <if test="amount!=null">
            amount,
        </if>
        <if test="shareCount!=null">
            share_count,
        </if>
        <if test="isDelete!=null">
            is_delete,
        </if>
        <if test="createTime != null">
            create_time,
        </if>
        <if test="updateTime != null">
            update_time,
        </if>
        <if test="caseInitialTime != null">
            case_initial_time,
        </if>
        <if test="dayKey != null and dayKey != ''">
            day_key,
        </if>
        <if test="encryptPhone != null and encryptPhone != ''">
            encrypt_phone,
        </if>
        <if test="encryptSecondPhone != null and encryptSecondPhone != ''">
            encrypt_second_phone,
        </if>
        <if test="workContentType != null ">
            work_content_type,
        </if>
        <if test="workbenchType != null ">
            workbench_type,
        </if>
        <if test="phoneStatus != null ">
            phone_status,
        </if>
        <if test="inspectResult != null ">
            inspect_result,
        </if>
        <if test="donateCount != null ">
            donate_count,
        </if>
        <if test="submitDonateCount != null ">
            submit_donate_count,
        </if>
        <if test="submitAmount != null ">
            submit_amount,
        </if>
        <if test="submitShareCount != null ">
            submit_share_count,
        </if>
        <if test="proxyUserId != null ">
            proxy_user_id,
        </if>
        <if test="expectHandleTime != null">
            expect_handle_time,
        </if>
        <if test="notifyCharge != null">
            notify_charge,
        </if>
        <if test="agreeCharge != null">
            agree_charge,
        </if>
        <if test="city != null and city != ''">
            city,
        </if>
        <if test="wxFriendNum != null">
            wx_friend_num,
        </if>
        <if test="raiserProfession != null and raiserProfession != ''">
            raiser_profession,
        </if>
        <if test="patientProfession != null and patientProfession != ''">
            patient_profession,
        </if>
        <if test="otherProfession != null and otherProfession != ''">
            other_profession,
        </if>
        <if test="otherRelation != null and otherRelation != ''">
            other_relation,
        </if>
        <if test="isPullGroup != null">
            is_pull_group,
        </if>
        <if test="isMinority != null">
            is_minority,
        </if>
        <if test="callNum != null ">
            call_num,
        </if>
        <if test="guideResult != null ">
            guide_result,
        </if>
        <if test="nextFollowTime != null">
            next_follow_time,
        </if>
        <if test="focusCase != null">
            focus_case,
        </if>
        <if test="majorClew != null">
            major_clew,
        </if>
        <if test="majorDisease!= null and majorDisease != ''">
            major_disease,
        </if>
        <if test="followTaskType != null">
            follow_task_type,
        </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="taskId!=null">
            #{taskId} ,
        </if>
        <if test="userId!=null and userId!=''">
            #{userId} ,
        </if>
        <if test="eventType != null">
            #{eventType} ,
        </if>
        <if test="handleStatus != null">
            #{handleStatus} ,
        </if>
        <if test="serviceStatus != null" >
            #{serviceStatus} ,
        </if>
        <if test="servicePhase != null ">
            #{servicePhase} ,
        </if>
        <if test="wechatPass != null ">
            #{wechatPass} ,
        </if>
        <if test="firstTag != null ">
            #{firstTag} ,
        </if>
        <if test="secondTag !=null">
            #{secondTag} ,
        </if>
        <if test="serviceMode !=null">
            #{serviceMode} ,
        </if>
        <if test="comment !=null">
            #{comment} ,
        </if>
        <if test="remindTime !=null">
            #{remindTime} ,
        </if>
        <if test="operator != null and operator != ''">
            #{operator} ,
        </if>
        <if test="handleTime !=null">
            #{handleTime} ,
        </if>
        <if test="infoId!=null and infoId!=''">
            #{infoId} ,
        </if>
        <if test="caseStatus !=null">
            #{caseStatus} ,
        </if>
        <if test="firstApproveStatus!=null">
            #{firstApproveStatus} ,
        </if>
        <if test="amount!=null">
            #{amount} ,
        </if>
        <if test="shareCount!=null">
            #{shareCount} ,
        </if>
        <if test="isDelete!=null">
            #{isDelete},
        </if>
        <if test="createTime != null">
            #{createTime} ,
        </if>
        <if test="updateTime != null">
            #{updateTime} ,
        </if>
        <if test="caseInitialTime != null">
            #{caseInitialTime} ,
        </if>
        <if test="dayKey != null and dayKey != ''">
            #{dayKey} ,
        </if>
        <if test="encryptPhone != null and encryptPhone != ''">
            #{encryptPhone} ,
        </if>
        <if test="encryptSecondPhone != null and encryptSecondPhone != ''">
            #{encryptSecondPhone} ,
        </if>
        <if test="workContentType != null ">
            #{workContentType} ,
        </if>
        <if test="workbenchType != null ">
            #{workbenchType} ,
        </if>
        <if test="phoneStatus != null ">
            #{phoneStatus} ,
        </if>
        <if test="inspectResult != null ">
            #{inspectResult} ,
        </if>
        <if test="donateCount != null ">
            #{donateCount} ,
        </if>
        <if test="submitDonateCount != null ">
            #{submitDonateCount} ,
        </if>
        <if test="submitAmount != null ">
            #{submitAmount} ,
        </if>
        <if test="submitShareCount != null ">
            #{submitShareCount} ,
        </if>
        <if test="proxyUserId != null ">
            #{proxyUserId} ,
        </if>
        <if test="expectHandleTime != null">
            #{expectHandleTime} ,
        </if>
        <if test="notifyCharge != null">
            #{notifyCharge},
        </if>
        <if test="agreeCharge != null">
            #{agreeCharge},
        </if>
        <if test="city != null and city != ''">
            #{city},
        </if>
        <if test="wxFriendNum != null">
            #{wxFriendNum},
        </if>
        <if test="raiserProfession != null and raiserProfession != ''">
            #{raiserProfession},
        </if>
        <if test="patientProfession != null and patientProfession != ''">
            #{patientProfession},
        </if>
        <if test="otherProfession != null and otherProfession != ''">
            #{otherProfession},
        </if>
        <if test="otherRelation != null and otherRelation != ''">
            #{otherRelation},
        </if>
        <if test="isPullGroup != null">
            #{isPullGroup},
        </if>
        <if test="isMinority != null">
            #{isMinority},
        </if>
        <if test="callNum != null ">
            #{callNum},
        </if>
        <if test="guideResult != null ">
            #{guideResult},
        </if>
        <if test="nextFollowTime != null">
            #{nextFollowTime},
        </if>
        <if test="focusCase!= null">
            #{focusCase},
        </if>
        <if test="majorClew!= null">
            #{majorClew},
        </if>
        <if test="majorDisease!= null and majorDisease != ''">
            #{majorDisease},
        </if>
        <if test="followTaskType != null">
            #{followTaskType},
        </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
    update <include refid="tableName"/>
    <set>
        <if test="taskId!=null">
            task_id=#{taskId} ,
        </if>
        <if test="userId!=null and userId!=''">
            user_id=#{userId} ,
        </if>
        <if test="eventType != null">
            event_type=#{eventType} ,
        </if>
        <if test="handleStatus != null">
            handle_status=#{handleStatus} ,
        </if>
        <if test="serviceStatus != null" >
            service_status=#{serviceStatus} ,
        </if>
        <if test="servicePhase != null ">
            service_phase=#{servicePhase} ,
        </if>
        <if test="wechatPass != null ">
            wechat_pass=#{wechatPass} ,
        </if>
        <if test="firstTag != null ">
            first_tag=#{firstTag} ,
        </if>
        <if test="secondTag !=null">
            second_tag=#{secondTag} ,
        </if>
        <if test="serviceMode !=null">
            service_mode=#{serviceMode} ,
        </if>
        <if test="comment !=null">
            comment=#{comment} ,
        </if>
        <if test="remindTime !=null">
            remind_time=#{remindTime} ,
        </if>
        <if test="operator != null and operator != ''">
            operator=#{operator} ,
        </if>
        <if test="handleTime !=null">
            handle_time=#{handleTime} ,
        </if>
        <if test="infoId!=null and infoId!=''">
            info_id=#{infoId} ,
        </if>
        <if test="caseStatus !=null">
            case_status=#{caseStatus} ,
        </if>
        <if test="firstApproveStatus!=null">
            first_approve_status=#{firstApproveStatus} ,
        </if>
        <if test="amount!=null">
            amount=#{amount} ,
        </if>
        <if test="shareCount!=null">
            share_count=#{shareCount} ,
        </if>
        <if test="isDelete!=null">
            is_delete=#{isDelete} ,
        </if>
        <if test="caseInitialTime != null">
            case_initial_time=#{caseInitialTime} ,
        </if>
        <if test="dayKey != null and dayKey != ''">
            day_key = #{dayKey} ,
        </if>
        <if test="encryptPhone != null and encryptPhone != ''">
            encrypt_phone = #{encryptPhone} ,
        </if>
        <if test="encryptSecondPhone != null and encryptSecondPhone != ''">
            encrypt_second_phone = #{encryptSecondPhone} ,
        </if>
        <if test="workContentType != null ">
            work_content_type = #{workContentType} ,
        </if>
        <if test="callNum != null ">
            call_num = #{callNum},
        </if>
        <if test="workbenchType != null ">
            workbench_type = #{workbenchType} ,
        </if>
        <if test="phoneStatus != null ">
            phone_status = #{phoneStatus},
        </if>
        <if test="inspectResult != null ">
            inspect_result = #{inspectResult} ,
        </if>
        <if test="donateCount != null ">
            donate_count = #{donateCount} ,
        </if>
        <if test="submitDonateCount != null ">
            submit_donate_count = #{submitDonateCount} ,
        </if>
        <if test="submitAmount != null ">
            submit_amount = #{submitAmount} ,
        </if>
        <if test="submitShareCount != null ">
            submit_share_count = #{submitShareCount} ,
        </if>
        <if test="proxyUserId != null ">
            `proxy_user_id` = #{proxyUserId} ,
        </if>
        <if test="notifyCharge != null ">
            `notify_charge` = #{notifyCharge} ,
        </if>
        <if test="agreeCharge != null ">
            `agree_charge` = #{agreeCharge} ,
        </if>
        <if test="city != null and city != ''">
            `city` = #{city} ,
        </if>
        <if test="wxFriendNum != null ">
            `wx_friend_num` = #{wxFriendNum} ,
        </if>
        <if test="raiserProfession != null">
            `raiser_profession` = #{raiserProfession} ,
        </if>
        <if test="patientProfession != null">
            `patient_profession` = #{patientProfession} ,
        </if>
        <if test="otherProfession != null">
            `other_profession` = #{otherProfession} ,
        </if>
        <if test="otherRelation != null">
            `other_relation` = #{otherRelation} ,
        </if>
        <if test="isPullGroup != null">
            `is_pull_group` = #{isPullGroup} ,
        </if>
        <if test="isMinority!= null">
            `is_minority` = #{isMinority},
        </if>
        <if test="guideResult!= null">
            `guide_result` = #{guideResult},
        </if>
        <if test="callNumStatus != null">
            call_num_status=#{callNumStatus},
        </if>
        <if test="nextFollowTime != null">
            next_follow_time=#{nextFollowTime},
        </if>
        <if test="focusCase!= null">
            focus_case=#{focusCase},
        </if>
        <if test="majorClew != null">
            major_clew = #{majorClew},
        </if>
        <if test="majorDisease != null and majorDisease!= ''">
            major_disease = #{majorDisease},
        </if>
    </set>
        where id=#{id}
    </update>

    <update id="update4TaskHandleStatus">
        update <include refid="tableName"/>
        <set>
            <if test="taskHandleStatus !=null ">
                handle_status=#{taskHandleStatus} ,
            </if>
        </set>
        where id in
        <foreach collection="followTaskList" item="followTask" open="(" close=")" separator=",">
            #{followTask.id}
        </foreach>
    </update>

    <update id="saveComment">
        update <include refid="tableName"/>
        <set>
            comment=#{comment}
        </set>
        where id =  #{id}
    </update>

    <update id="updateTaskHandleStatus4DonateService">
        update <include refid="tableName"/>
        <set>
            <if test="taskHandleStatus !=null ">
                handle_status=#{taskHandleStatus} ,
            </if>
            <if test="submitDonateCount !=null ">
                submit_donate_count=#{submitDonateCount} ,
            </if>
            <if test="submitAmount !=null ">
                submit_amount=#{submitAmount} ,
            </if>
            <if test="submitShareCount !=null ">
                submit_share_count=#{submitShareCount} ,
            </if>
        </set>
        where id in
        <foreach collection="followTaskList" item="followTask" open="(" close=")" separator=",">
            #{followTask.id}
        </foreach>
    </update>

    <update id="batchUpdateProxyUserId">
        update <include refid="tableName"/>
        set
        <trim prefix="proxy_user_id =case" suffix="end">
            <foreach collection="models" item="model" index="index">
                when id=#{model.id} then #{model.proxyUserId}
            </foreach>
        </trim>
        where id in
        <foreach collection="models" item="model" open="(" close=")" separator=",">
            #{model.id}
        </foreach>
    </update>

    <select id="getInfoById" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
    select <include refid="baseResult"/>
    from <include refid="tableName"/>
    where id = #{id} and is_delete=0 <if test="userId != null and userId != ''"> and user_id = #{userId}</if>
    </select>

    <select id="getInfoListByTaskId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_id = #{taskId} and is_delete=0  <if test="userId != null and userId != ''"> and user_id = #{userId}</if>
        order by id desc
        limit #{offset},#{pageSize}
    </select>

    <select id="getTaskInfoByTaskIdFromMaster"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_id = #{taskId} and is_delete=0
    </select>

    <select id="getTaskInfoByIdS" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete= 0
        and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getTaskInfoByTaskIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and task_id in
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId}
        </foreach>
    </select>

    <select id="getTaskInfoCountByTaskId" resultType="java.lang.Integer">
        SELECT count(*)
        from <include refid="tableName"/>
        where task_id = #{taskId} and is_delete=0
    </select>

    <select id="getTaskInfoByTaskIdListFromMaster"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and task_id in
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId}
        </foreach>
    </select>

    <select id="countByFollowUpTaskQueryParam" resultType="java.lang.Long">
        select count(*)
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != #{excludeServicePhase}
        and day_key between #{followUpTaskQueryParam.startDateKey} and #{followUpTaskQueryParam.endDateKey}
        and create_time &gt; curdate()
        <if test="followUpTaskQueryParam.followTaskHandleStatus != null">
            and handle_status= #{followUpTaskQueryParam.followTaskHandleStatus}
        </if>
        <if test="followUpTaskQueryParam.servicePhase != null">
            and service_phase = #{followUpTaskQueryParam.servicePhase}
        </if>
        <if test="followUpTaskQueryParam.followTaskHandleStatus == null ">
            and handle_status in (0, 1)
        </if>
        <if test="followUpTaskQueryParam.workContent != null">
            and work_content_type = #{followUpTaskQueryParam.workContent}
        </if>
        <if test="followUpTaskQueryParam.workbenchTypeEnum != null">
            and workbench_type = #{followUpTaskQueryParam.workbenchTypeEnum.type}
        </if>
        <if test="followUpTaskQueryParam.encryptPhone != null and followUpTaskQueryParam.encryptPhone != ''">
            and (encrypt_phone = #{followUpTaskQueryParam.encryptPhone} or encrypt_second_phone = #{followUpTaskQueryParam.encryptPhone})
        </if>
        <if test="followUpTaskQueryParam.misList != null and followUpTaskQueryParam.misList.size() != 0 and followUpTaskQueryParam.followUpClassifyType !=9">
            and user_id in
            <foreach collection="followUpTaskQueryParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="followUpTaskQueryParam.misList != null and followUpTaskQueryParam.misList.size() != 0 and followUpTaskQueryParam.followUpClassifyType ==9">
            and proxy_user_id in
            <foreach collection="followUpTaskQueryParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="followUpTaskQueryParam.followUpClassifyType ==9">
            and proxy_user_id != ""
        </if>
    </select>

    <select id="listByFollowUpTaskQueryParam" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != #{excludeServicePhase}
        and day_key between #{followUpTaskQueryParam.startDateKey} and #{followUpTaskQueryParam.endDateKey}
        and create_time &gt; curdate()
        <if test="followUpTaskQueryParam.followTaskHandleStatus != null">
            and handle_status= #{followUpTaskQueryParam.followTaskHandleStatus}
        </if>
        <if test="followUpTaskQueryParam.servicePhase != null">
            and service_phase = #{followUpTaskQueryParam.servicePhase}
        </if>
        <if test="followUpTaskQueryParam.followTaskHandleStatus == null ">
            and handle_status in (0, 1)
        </if>
        <if test="followUpTaskQueryParam.workContent != null">
            and work_content_type = #{followUpTaskQueryParam.workContent}
        </if>
        <if test="followUpTaskQueryParam.workbenchTypeEnum != null">
            and workbench_type = #{followUpTaskQueryParam.workbenchTypeEnum.type}
        </if>
        <if test="followUpTaskQueryParam.encryptPhone != null and followUpTaskQueryParam.encryptPhone != ''">
            and (encrypt_phone = #{followUpTaskQueryParam.encryptPhone} or encrypt_second_phone = #{followUpTaskQueryParam.encryptPhone})
        </if>
        <if test="followUpTaskQueryParam.misList != null and followUpTaskQueryParam.misList.size() != 0 and followUpTaskQueryParam.followUpClassifyType !=9">
            and user_id in
            <foreach collection="followUpTaskQueryParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="followUpTaskQueryParam.misList != null and followUpTaskQueryParam.misList.size() != 0 and followUpTaskQueryParam.followUpClassifyType ==9">
            and proxy_user_id in
            <foreach collection="followUpTaskQueryParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="followUpTaskQueryParam.followUpClassifyType ==9">
            and proxy_user_id != ""
        </if>
        <if test="followUpTaskQueryParam.workbenchType == 2">
            order by expect_handle_time,create_time desc
        </if>
        <if test="followUpTaskQueryParam.workbenchType == 4">
            order by create_time desc
        </if>
        limit #{followUpTaskQueryParam.offset}, #{followUpTaskQueryParam.pageSize}
    </select>

    <select id="listTaskClassifyModelGroupByDays" resultType="com.shuidihuzhu.cf.clewtrack.model.FollowUpTaskClassifyModel">
        select day_key, count(id) as count
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != #{excludeServicePhase}
        and day_key between #{followUpTaskQueryParam.startDateKey} and #{followUpTaskQueryParam.endDateKey}
        and create_time &gt; curdate()
        <if test="followUpTaskQueryParam.followTaskHandleStatus != null">
            and handle_status= #{followUpTaskQueryParam.followTaskHandleStatus}
        </if>
        <if test="followUpTaskQueryParam.servicePhase != null">
            and service_phase = #{followUpTaskQueryParam.servicePhase}
        </if>
        <if test="followUpTaskQueryParam.followTaskHandleStatus == null ">
            and handle_status in (0, 1)
        </if>
        <if test="followUpTaskQueryParam.workContent != null">
            and work_content_type = #{followUpTaskQueryParam.workContent}
        </if>
        <if test="followUpTaskQueryParam.workbenchTypeEnum != null">
            and workbench_type = #{followUpTaskQueryParam.workbenchTypeEnum.type}
        </if>
        <if test="followUpTaskQueryParam.encryptPhone != null and followUpTaskQueryParam.encryptPhone != ''">
            and (encrypt_phone = #{followUpTaskQueryParam.encryptPhone} or encrypt_second_phone = #{followUpTaskQueryParam.encryptPhone})
        </if>
        <if test="followUpTaskQueryParam.misList != null and followUpTaskQueryParam.misList.size() != 0">
            and user_id in
            <foreach collection="followUpTaskQueryParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by day_key
    </select>

    <select id="countFollowUpTaskMonitor" resultType="java.lang.Long">
        select count(*) from (
        select user_id
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != 0
        and handle_status in (0, 1, 2)
        and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.d0EndDayKey}
        and create_time between #{monitorParam.dayKey} and #{monitorParam.endDateKey}
        <if test="monitorParam.workContent != null">
            and work_content_type= #{monitorParam.workContent}
        </if>
        <if test="monitorParam.workbenchTypeEnum != null">
            and workbench_type = #{monitorParam.workbenchTypeEnum.type}
        </if>
        <if test="monitorParam.misList != null and monitorParam.misList.size() != 0">
            and user_id in
            <foreach collection="monitorParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by work_content_type, user_id
        ) as temp
    </select>

    <select id="followUpTaskMonitor" resultType="com.shuidihuzhu.cf.clewtrack.model.FollowUpTaskMonitorDBModel">
        select
        day_key,
        work_content_type,
        user_id,
        sum(case when handle_status in (0, 2) and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as d0UnHandle,
        sum(case when handle_status in (0, 2) and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as d1UnHandle,
        sum(case when handle_status in (0, 2) and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as d2UnHandle,
        sum(case when handle_status in (0, 2) and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as d3UnHandle,
        sum(case when handle_status in (0, 2) and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.historyEndDayKey} then 1 else 0 end) as historyUnHandle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as d0Handle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as d1Handle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as d2Handle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as d3Handle
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != 0
        and handle_status in (0, 1, 2)
        and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.d0EndDayKey}
        and create_time between #{monitorParam.dayKey} and #{monitorParam.endDateKey}
        <if test="monitorParam.workContent != null">
            and work_content_type= #{monitorParam.workContent}
        </if>
        <if test="monitorParam.workbenchTypeEnum != null">
            and workbench_type = #{monitorParam.workbenchTypeEnum.type}
        </if>
        <if test="monitorParam.misList != null and monitorParam.misList.size() != 0">
            and user_id in
            <foreach collection="monitorParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by work_content_type, user_id
        limit #{monitorParam.offset}, #{monitorParam.pageSize}
    </select>

    <select id="followUpTaskMonitorForToday" resultType="com.shuidihuzhu.cf.clewtrack.model.FollowUpTaskMonitorDBModel">
        select
        day_key,
        work_content_type,
        user_id,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as d0UnHandle,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as d1UnHandle,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as d2UnHandle,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as d3UnHandle,
        sum(case when handle_status = 0 and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.historyEndDayKey} then 1 else 0 end) as historyUnHandle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as d0Handle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as d1Handle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as d2Handle,
        sum(case when handle_status = 1 and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as d3Handle
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != 0
        and handle_status in (0, 1)
        and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.d0EndDayKey}
        and create_time between #{monitorParam.dayKey} and #{monitorParam.endDateKey}
        <if test="monitorParam.workContent != null">
            and work_content_type= #{monitorParam.workContent}
        </if>
        <if test="monitorParam.workbenchTypeEnum != null">
            and workbench_type = #{monitorParam.workbenchTypeEnum.type}
        </if>
        <if test="monitorParam.misList != null and monitorParam.misList.size() != 0">
            and user_id in
            <foreach collection="monitorParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by work_content_type, user_id
        limit #{monitorParam.offset}, #{monitorParam.pageSize}
    </select>

    <select id="downLoadMonitorData" resultType="com.shuidihuzhu.cf.clewtrack.model.FollowUpTaskMonitorDBModel">
        select
        day_key,
        work_content_type,
        user_id,
        encrypt_phone as phone
        from <include refid="tableName"/>
        where is_delete = 0
        and service_phase != 0
        and handle_status in (0, 2)
        and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.historyEndDayKey}
        and create_time between #{monitorParam.dayKey} and #{monitorParam.endDateKey}
        <if test="monitorParam.workContent != null">
            and work_content_type= #{monitorParam.workContent}
        </if>
        <if test="monitorParam.workbenchTypeEnum != null">
            and workbench_type = #{monitorParam.workbenchTypeEnum.type}
        </if>
        <if test="monitorParam.misList != null and monitorParam.misList.size() != 0">
            and user_id in
            <foreach collection="monitorParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="downLoadMonitorDataForToday" resultType="com.shuidihuzhu.cf.clewtrack.model.FollowUpTaskMonitorDBModel">
        select
        day_key,
        work_content_type,
        user_id,
        encrypt_phone as phone
        from <include refid="tableName"/>
        where is_delete = 0
        and service_phase != 0
        and handle_status = 0
        and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.historyEndDayKey}
        and create_time between #{monitorParam.dayKey} and #{monitorParam.endDateKey}
        <if test="monitorParam.workContent != null">
            and work_content_type= #{monitorParam.workContent}
        </if>
        <if test="monitorParam.workbenchTypeEnum != null">
            and workbench_type = #{monitorParam.workbenchTypeEnum.type}
        </if>
        <if test="monitorParam.misList != null and monitorParam.misList.size() != 0">
            and user_id in
            <foreach collection="monitorParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listFollowUpTaskByDayKey"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and day_key = #{dayKey} and workbench_type = #{workbenchType}
        <if test="userId!=null and userId!=''">
         and user_id=#{userId}
        </if>
    </select>

    <select id="followUpTaskStatistic"
            resultType="com.shuidihuzhu.cf.clewtrack.model.vo.FollowTaskStatisticVO">
        select
        user_id,
        sum(case when handle_status = 1 and user_id = operator and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as handleCountD0,
        sum(case when handle_status = 1 and user_id = operator and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as handleCountD1,
        sum(case when handle_status = 1 and user_id = operator and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as handleCountD2,
        sum(case when handle_status = 1 and user_id = operator and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as handleCountD3,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as unHandle0CountD0,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as unHandle0CountD1,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as unHandle0CountD2,
        sum(case when handle_status = 0 and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as unHandle0CountD3,
        sum(case when handle_status = 0 and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.historyEndDayKey} then 1 else 0 end) as unHandleCountHist,
        sum(case when handle_status = 2 and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as unHandle2CountD0,
        sum(case when handle_status = 2 and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as unHandle2CountD1,
        sum(case when handle_status = 2 and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as unHandle2CountD2,
        sum(case when handle_status = 2 and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as unHandle2CountD3,
        sum(case when handle_status = 1 and user_id != operator and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as followGroupCountD0,
        sum(case when handle_status = 1 and user_id != operator and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as followGroupCountD1,
        sum(case when handle_status = 1 and user_id != operator and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as followGroupCountD2,
        sum(case when handle_status = 1 and user_id != operator and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as followGroupCountD3,
        sum(case when handle_status = 1 and call_num > 0 and day_key between #{monitorParam.d0StartDayKey} and #{monitorParam.d0EndDayKey} then 1 else 0 end) as followPhoneCountD0,
        sum(case when handle_status = 1 and call_num > 0 and day_key between #{monitorParam.d1StartDayKey} and #{monitorParam.d1EndDayKey} then 1 else 0 end) as followPhoneCountD1,
        sum(case when handle_status = 1 and call_num > 0 and day_key between #{monitorParam.d2StartDayKey} and #{monitorParam.d2EndDayKey} then 1 else 0 end) as followPhoneCountD2,
        sum(case when handle_status = 1 and call_num > 0 and day_key between #{monitorParam.d3StartDayKey} and #{monitorParam.d3EndDayKey} then 1 else 0 end) as followPhoneCountD3
        from <include refid="tableName"/>
        where is_delete=0
        and handle_status in (0, 1, 2)
        and day_key between #{monitorParam.historyStartDayKey} and #{monitorParam.d0EndDayKey}
        and create_time between #{monitorParam.dayKey} and #{monitorParam.endDateKey}
        <if test="searchParam.workbenchTypeEnum != null">
            and workbench_type = #{searchParam.workbenchTypeEnum.type}
        </if>
        <if test="searchParam.queryUserIds != null and searchParam.queryUserIds.size() != 0">
            and user_id in
            <foreach collection="searchParam.queryUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by user_id
    </select>

    <select id="listFollowUpTask" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and handle_status in (0, 1, 2, 3)
        and create_time between #{startDate}  and #{endDate}
        <if test="searchParam.workContentType != null">
            and work_content_type= #{searchParam.workContentType}
        </if>
        <if test="searchParam.workbenchTypeEnum != null">
            and workbench_type = #{searchParam.workbenchTypeEnum.type}
        </if>
        <if test="searchParam.queryUserIds != null and searchParam.queryUserIds.size() != 0">
            and user_id in
            <foreach collection="searchParam.queryUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countVacationDonateTask" resultType="java.lang.Integer">
        select count(*) from <include refid="tableName"/>
        where is_delete=0
        and service_phase != 100
        and handle_status = 0
        and workbench_type = #{queryParam.workbenchType}
        and day_key <![CDATA[ >= ]]> #{queryParam.newStartDayKey} and day_key <![CDATA[ <= ]]> #{queryParam.newEndDayKey}
        and create_time between #{queryParam.startTime} and #{queryParam.endTime}
        and `proxy_user_id` = ""
        <if test="queryParam.donateServiceMis != null and queryParam.donateServiceMis.size() != 0">
            and user_id in
            <foreach collection="queryParam.donateServiceMis" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listVacationDonateTask" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0
        and service_phase != 100
        and handle_status = 0
        and workbench_type = #{queryParam.workbenchType}
        and day_key between #{queryParam.startDayKey}  and #{queryParam.endDayKey}
        and create_time between #{queryParam.startTime} and #{queryParam.endTime}
        and proxy_user_id = ""
        <if test="queryParam.donateServiceMis != null and queryParam.donateServiceMis.size() != 0">
            and user_id in
            <foreach collection="queryParam.donateServiceMis" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countProxyDonateTask" resultType="int">
        select count(id)
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != #{excludeServicePhase}
        and day_key between #{proxyParam.startDateKey} and #{proxyParam.endDateKey}
        and create_time &gt; curdate()
        and proxy_user_id != ""
        <if test="proxyParam.followTaskHandleStatus != null">
            and handle_status= #{proxyParam.followTaskHandleStatus}
        </if>
        <if test="proxyParam.servicePhase != null">
            and service_phase = #{proxyParam.servicePhase}
        </if>
        <if test="proxyParam.followTaskHandleStatus == null ">
            and handle_status in (0, 1)
        </if>
        <if test="proxyParam.workContent != null">
            and work_content_type = #{proxyParam.workContent}
        </if>
        <if test="proxyParam.workbenchTypeEnum != null">
            and workbench_type = #{proxyParam.workbenchTypeEnum.type}
        </if>
        <if test="proxyParam.encryptPhone != null and proxyParam.encryptPhone != ''">
            and (encrypt_phone = #{proxyParam.encryptPhone} or encrypt_second_phone = #{proxyParam.encryptPhone})
        </if>
        <if test="proxyParam.misList != null and proxyParam.misList.size() != 0">
            and proxy_user_id in
            <foreach collection="proxyParam.misList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listFollowUpTaskByTaskIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase = #{servicePhase}
        and workbench_type = #{workbenchType}
        <if test="taskIds != null and taskIds.size() != 0">
            and task_id in
            <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
        </if>
    </select>

    <select id="listFollowUpTaskByMisIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase = #{servicePhase}
        and workbench_type = #{workbenchType}
        and create_time between #{startTime} and #{endTime}
        <if test="misIds != null and misIds.size() != 0">
            and user_id in
            <foreach collection="misIds" item="misId" open="(" close=")" separator=",">
                #{misId}
            </foreach>
        </if>
    </select>

    <update id="update4ExpectHandleTime">
        update <include refid="tableName"/>
        set expect_handle_time = #{expectHandleTime}
        where id = #{model.id}
    </update>

    <update id="updateWhenTransform">
        update <include refid="tableName"/>
        set handle_status = #{handleStatus}, `comment` = #{comment}
        where id = #{id}
    </update>

    <update id="updateNotifyChargeAndCity">
        update <include refid="tableName"/>
        <set>
            <if test="notifyCharge != null ">
                `notify_charge` = #{notifyCharge} ,
            </if>
            <if test="agreeCharge != null ">
                `agree_charge` = #{agreeCharge} ,
            </if>
            <if test="city != null and city != ''">
                `city` = #{city},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="countProxyDonateTaskGroupByUser" resultType="java.util.Map">
        select 
            user_id as userId,
            count(id) as taskCount
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != 100
        and handle_status = 0
        and workbench_type = #{queryParam.workbenchType}
        and proxy_user_id != ""
        and create_time between #{queryParam.startTime} and #{queryParam.endTime}
        <if test="queryParam.proxyDonateServiceMis != null and queryParam.proxyDonateServiceMis.size() != 0">
            and user_id in
            <foreach collection="queryParam.proxyDonateServiceMis" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by user_id
    </select>

    <select id="listProxyDonateTask" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and service_phase != 100
        and handle_status = 0
        and workbench_type = #{param.workbenchType}
        and day_key &gt;= #{param.newStartDayKey}
        and day_key &lt; #{param.newEndDayKey}
        and proxy_user_id = ''
        and create_time between #{param.startTime} and #{param.endTime}
        <if test="param.donateServiceMis != null and param.donateServiceMis.size() != 0">
            and user_id in
            <foreach collection="param.donateServiceMis" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        limit #{limit}
    </select>

</mapper>
