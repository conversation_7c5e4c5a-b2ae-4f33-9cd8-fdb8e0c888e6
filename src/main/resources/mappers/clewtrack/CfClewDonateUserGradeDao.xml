<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewDonateUserGradeDao">

    <sql id="table_name">
        `cf_clew_donate_user_grade`
    </sql>

    <sql id="result">
        `id`,
        `produce_date`,
        `user_id`,
        `grade`,
        `rank`,
        `score`,
        `rank_detail`,
        `rank_last_updated`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <update id="updateGrade">
        update <include refid="table_name"/>
        <set>
            <if test="grade != null and grade != ''">
                `grade` = #{grade},
            </if>
            <if test="rankLastUpdated != null">
                `rank_last_updated` = #{rankLastUpdated},
            </if>
        </set>
        where `id` = #{id}
    </update>

    <select id="getByRankId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewDonateUserGradeDO">
        select <include refid="result"/>
        from <include refid="table_name"/>
        where is_delete = 0
        and id = #{id}
    </select>

    <select id="queryDonateRankings" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewDonateUserGradeDO">
        select <include refid="result"/>
        from <include refid="table_name"/>
        where is_delete = 0
        <if test="searchModel.dateTime != null and searchModel.dateTime!= ''">
            and `produce_date` = #{searchModel.dateTime}
        </if>
        <if test="searchModel.searchUserIds != null and searchModel.searchUserIds.size() > 0">
            and `user_id` in
            <foreach collection="searchModel.searchUserIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getLatestProduceTime" resultType="java.lang.String">
        select max(`produce_date`)
        from <include refid="table_name"/>
        where is_delete = 0
    </select>

    <select id="getLatestProduceUserRankByUserIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewDonateUserGradeDO">
        select <include refid="result"/>
        from <include refid="table_name"/>
        where is_delete = 0
        and `user_id` in
        <foreach collection="userIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and produce_date = #{latestProduceTime}
    </select>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="table_name"/>
        (`produce_date`, `user_id`, `grade`, `rank`, `score`, `rank_detail`, `rank_last_updated`)
        values
        <foreach collection="insertList" item="item" separator=",">
            (#{item.produceDate}, #{item.userId}, #{item.grade},
            <if test="item.rank !=null">
                #{item.rank},
            </if>
            <if test="item.rank == null">
                0,
            </if>
            #{item.score}, #{item.rankDetail}, #{item.rankLastUpdated})
        </foreach>
    </insert>

    <update id="batchUpdate">
        update <include refid="table_name"/>
        set grade = case id
        <foreach collection="updateList" item="item">
            when #{item.id} then #{item.grade}
        </foreach>
        end, rank = case id
        <foreach collection="updateList" item="item">
            when #{item.id} then #{item.rank}
        </foreach>
        end, score = case id
        <foreach collection="updateList" item="item">
            when #{item.id} then #{item.score}
        </foreach>
        end, rank_detail = case id
        <foreach collection="updateList" item="item">
            when #{item.id} then #{item.rankDetail}
        </foreach>
        end, rank_last_updated = case id
        <foreach collection="updateList" item="item">
            when #{item.id} then #{item.rankLastUpdated}
        </foreach>
        end
        where `id` in
        <foreach collection="updateList" item="item" index="index" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
    </update>

    <select id="getUserGradeByUserGrade" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewDonateUserGradeDO">
        select <include refid="result"/>
        from <include refid="table_name"/>
        where is_delete = 0
        and produce_date = #{latestDate}
        and grade in
        <foreach collection="gradeList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getMonthProduceTime" resultType="java.lang.String">
        select max(`produce_date`)
        from <include refid="table_name"/>
        where is_delete = 0
        and produce_date between #{monthOfStartTime} and #{monthOfEndTime}
    </select>

    <select id="getDonateGradeUserByLatestProduceTime" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewDonateUserGradeDO">
        select <include refid="result"/>
        from <include refid="table_name"/>
        where is_delete = 0
        and produce_date = #{latestProduceTime}
    </select>

</mapper>