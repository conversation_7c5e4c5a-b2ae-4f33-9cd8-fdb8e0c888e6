<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewAnnouncementRecordsDao">

    <sql id="tableFiled">
    id, announcement_creator, announcement_creator_mis, workbench_type, content_type,
    announcement_creator_org_id, announcement_creator_org_name, remark, is_delete, create_time,
    update_time
  </sql>
    <sql id="tableName">
    cf_clew_announcement_records
  </sql>
    <delete id="deleteByKey">
        update
        <include refid="tableName"/>
        set is_delete= 1
        where
        id = #{id}
    </delete>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsDO">
        select
        <include refid="tableFiled"/>
        from
        <include refid="tableName"/>
        where id = #{id}
    </select>
    <select id="selectPageByParam"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsDO">
        select
        <include refid="tableFiled"/>
        from
        <include refid="tableName"/>
        where
        is_delete=0
        <if test="param.workbenchType != null and param.workbenchType != ''">
            AND workbench_type = #{param.workbenchType}
        </if>
        <if test="param.contentType!= null and param.contentType!= ''">
            AND content_type = #{param.contentType}
        </if>
        <if test="param.announcementCreatorOrgId != null and param.announcementCreatorOrgId !=''">
            AND announcement_creator_org_id = #{param.announcementCreatorOrgId}
        </if>
        <if test="param.announcementCreatorMis != null and param.announcementCreatorMis !=''">
            AND announcement_creator_mis = #{param.announcementCreatorMis}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        order by
        create_time DESC
        LIMIT #{offSet},#{param.pageSize}
    </select>
    <select id="selectByParam"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsDO">
        SELECT
        record.*,
        GROUP_CONCAT( sub.announcement_recipient ) AS announcementRecipientList,
        GROUP_CONCAT( sub.announcement_recipient_mis ) AS announcementRecipientMisList
        FROM
        cf_clew_announcement_records record
        LEFT JOIN cf_clew_announcement_records_sub sub ON record.id = sub.announcement_records_id
        AND record.is_delete = sub.is_delete
        WHERE
        record.is_delete = 0
        <if test="param.announcementRecipientOrgIdList != null and param.announcementRecipientOrgIdList.size()>0">
            AND sub.announcement_recipient_org_id in
            <foreach collection="param.announcementRecipientOrgIdList" item="item" index="index" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>
        <if test="param.announcementRecipientMis != null and param.announcementRecipientMis!=''">
            and EXISTS (SELECT s.id FROM cf_clew_announcement_records_sub s WHERE s.announcement_records_id = record.id
            and s.announcement_recipient_mis = #{param.announcementRecipientMis})
        </if>
        <if test="param.workbenchType != null and param.workbenchType != ''">
            AND record.workbench_type = #{param.workbenchType}
        </if>
        <if test="param.contentType!= null and param.contentType!= ''">
            AND record.content_type = #{param.contentType}
        </if>
        <if test="param.announcementCreatorOrgIdList != null and param.announcementCreatorOrgIdList.size()>0">
            AND record.announcement_creator_org_id in
            <foreach collection="param.announcementCreatorOrgIdList" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.announcementCreatorMis != null and param.announcementCreatorMis !=''">
            AND record.announcement_creator_mis = #{param.announcementCreatorMis}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and record.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and record.create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.id != null and param.id != ''">
            and record.id = #{param.id}
        </if>
        <if test="param.memberClewUserId != null and param.memberClewUserId !=''">
            and EXISTS (SELECT s.id FROM cf_clew_announcement_records_sub s WHERE s.announcement_records_id = record.id
            and s.announcement_recipient_mis = #{param.memberClewUserId})
        </if>
        <if test="param.outerBindInnerOrgIdList != null and param.outerBindInnerOrgIdList.size()>0">
            and EXISTS (SELECT s.id FROM cf_clew_announcement_records_sub s WHERE s.announcement_records_id = record.id
            and s.announcement_recipient_org_id in
            <foreach collection="param.outerBindInnerOrgIdList" open="(" close=")" separator="," index="index"
                     item="item">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY
        record.id
        ORDER BY
        record.create_time DESC
    </select>
    <select id="selectSubPageByParam"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsDO">
        SELECT
        record.*,
        GROUP_CONCAT( sub.announcement_recipient ) AS announcementRecipientList,
        GROUP_CONCAT( sub.announcement_recipient_mis ) AS announcementRecipientMisList
        FROM
        cf_clew_announcement_records record
        LEFT JOIN cf_clew_announcement_records_sub sub ON record.id = sub.announcement_records_id
        AND record.is_delete = sub.is_delete
        WHERE
        record.is_delete = 0
        <if test="param.announcementRecipientOrgIdList != null and param.announcementRecipientOrgIdList.size()>0">
            AND sub.announcement_recipient_org_id in
            <foreach collection="param.announcementRecipientOrgIdList" item="item" index="index" separator="," close=")" open="(">
                #{item}
            </foreach>
        </if>

        <if test="param.announcementRecipientMis != null and param.announcementRecipientMis!=''">
            and EXISTS (SELECT s.id FROM cf_clew_announcement_records_sub s WHERE s.announcement_records_id = record.id
            and s.announcement_recipient_mis = #{param.announcementRecipientMis})
<!--             AND sub.announcement_recipient_mis= #{param.announcementRecipientMis}!-->
        </if>
        <if test="param.workbenchType != null and param.workbenchType != ''">
            AND record.workbench_type = #{param.workbenchType}
        </if>
        <if test="param.contentType!= null and param.contentType!= ''">
            AND record.content_type = #{param.contentType}
        </if>
        <if test="param.announcementCreatorOrgIdList != null and param.announcementCreatorOrgIdList.size()>0">
            AND record.announcement_creator_org_id in
            <foreach collection="param.announcementCreatorOrgIdList" open="(" close=")" separator="," index="index" item="item">
                #{item}
            </foreach>
        </if>
        <if test="param.announcementCreatorMis != null and param.announcementCreatorMis !=''">
            AND record.announcement_creator_mis = #{param.announcementCreatorMis}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and record.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and record.create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.memberClewUserId != null and param.memberClewUserId !=''">
            and EXISTS (SELECT s.id FROM cf_clew_announcement_records_sub s WHERE s.announcement_records_id = record.id
            and s.announcement_recipient_mis = #{param.memberClewUserId})
            <!--   AND sub.announcement_recipient_mis = #{param.memberClewUserId}!-->
        </if>
        <if test="param.outerBindInnerOrgIdList != null and param.outerBindInnerOrgIdList.size()>0">
            and EXISTS (SELECT s.id FROM cf_clew_announcement_records_sub s WHERE s.announcement_records_id = record.id
            and s.announcement_recipient_org_id in
            <foreach collection="param.outerBindInnerOrgIdList" open="(" close=")" separator="," index="index"
                     item="item">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY
        record.id
        ORDER BY
        record.create_time DESC
        LIMIT #{offSet},#{param.pageSize}
    </select>

    <select id="selectSubCountByParam"
            resultType="java.lang.Integer">
        select count(*) from (
        SELECT
        count(*)
        FROM
        cf_clew_announcement_records record
        LEFT JOIN cf_clew_announcement_records_sub sub ON record.id = sub.announcement_records_id
        AND record.is_delete = sub.is_delete
        WHERE
        record.is_delete = 0
        <if test="param.announcementRecipientOrgId != null and param.announcementRecipientOrgId !=''">
            AND sub.announcement_recipient_org_id =#{param.announcementRecipientOrgId}
        </if>
        <if test="param.announcementRecipientMis != null and param.announcementRecipientMis!=''">
            AND sub.announcement_recipient_mis= #{param.announcementRecipientMis}
        </if>
        <if test="param.workbenchType != null and param.workbenchType != ''">
            AND record.workbench_type = #{param.workbenchType}
        </if>
        <if test="param.contentType!= null and param.contentType!= ''">
            AND record.content_type = #{param.contentType}
        </if>
        <if test="param.announcementCreatorOrgId != null and param.announcementCreatorOrgId !=''">
            AND record.announcement_creator_org_id = #{param.announcementCreatorOrgId}
        </if>
        <if test="param.announcementCreatorMis != null and param.announcementCreatorMis !=''">
            AND record.announcement_creator_mis = #{param.announcementCreatorMis}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and record.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and record.create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.memberClewUserId != null and param.memberClewUserId !=''">
            AND sub.announcement_recipient_mis = #{param.memberClewUserId}
        </if>
        <if test="param.outerBindInnerOrgIdList != null and param.outerBindInnerOrgIdList.size()>0">
            and EXISTS (SELECT s.id FROM cf_clew_announcement_records_sub s WHERE s.announcement_records_id = record.id
            and s.announcement_recipient_org_id in
            <foreach collection="param.outerBindInnerOrgIdList" open="(" close=")" separator="," index="index"
                     item="item">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY
        record.id
        ) a
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsDO" useGeneratedKeys="true">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="announcementCreator != null">
                announcement_creator,
            </if>
            <if test="announcementCreatorMis != null">
                announcement_creator_mis,
            </if>
            <if test="workbenchType != null">
                workbench_type,
            </if>
            <if test="contentType != null">
                content_type,
            </if>
            <if test="announcementCreatorOrgId != null">
                announcement_creator_org_id,
            </if>
            <if test="announcementCreatorOrgName != null">
                announcement_creator_org_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="announcementCreator != null">
                #{announcementCreator},
            </if>
            <if test="announcementCreatorMis != null">
                #{announcementCreatorMis},
            </if>
            <if test="workbenchType != null">
                #{workbenchType},
            </if>
            <if test="contentType != null">
                #{contentType},
            </if>
            <if test="announcementCreatorOrgId != null">
                #{announcementCreatorOrgId},
            </if>
            <if test="announcementCreatorOrgName != null">
                #{announcementCreatorOrgName,},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnouncementRecordsDO">
        update
        <include refid="tableName"/>
        <set>
            <if test="announcementCreator != null">
                announcement_creator = #{announcementCreator},
            </if>
            <if test="announcementCreatorMis != null">
                announcement_creator_mis = #{announcementCreatorMis},
            </if>
            <if test="workbenchType != null">
                workbench_type = #{workbenchType},
            </if>
            <if test="contentType != null">
                content_type = #{contentType},
            </if>
            <if test="announcementCreatorOrgId != null">
                announcement_creator_org_id = #{announcementCreatorOrgId},
            </if>
            <if test="announcementCreatorOrgName != null">
                announcement_creator_org_name = #{announcementCreatorOrgName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>