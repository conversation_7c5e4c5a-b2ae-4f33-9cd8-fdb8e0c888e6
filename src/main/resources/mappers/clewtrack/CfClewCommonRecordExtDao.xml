<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCommonRecordExtDao">

    <sql id="tableName">cf_clew_common_record_ext</sql>

    <sql id="selectFiled">
        `id`,
        `record_id` as recordId,
        `ext_name` as extName,
        `ext_value` as extValue
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCommonRecordExtDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        (`record_id`,`ext_name`,`ext_value`)
        values
        (#{recordId}, #{extName}, #{extValue})
    </insert>


</mapper>
