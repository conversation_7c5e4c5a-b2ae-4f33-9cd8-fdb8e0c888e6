<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseLayerAssignRuleDao">
    <sql id="tableName">cf_clew_base_layer_assign_rule</sql>
    <sql id="field">
        `id`,
        `clew_layer_id`,
        `clew_layer_name`,
        `crowd_packet_id`,
        `crowd_packet_name`,
        `effective_start_date`,
        `effective_end_date`,
        `single_assign_upper_limit_str`,
        `rule_type`,
        `workbench_type`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <select id="getClewlayerassignruleList"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerAssignRuleDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and rule_type = #{ruleType}
    </select>
    <select id="getClewlayerassignruleCountByClewlayerId" resultType="java.lang.Integer">
        select count(*) from <include refid="tableName"/>
        where is_delete=0 and clew_layer_id = #{clewLayerId} and rule_type = #{ruleType}
    </select>
    <select id="getClewlayerassignrule"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerAssignRuleDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and id = #{id}
    </select>
    <insert id="saveClewlayerassignrule" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`clew_layer_id`,`clew_layer_name`,`crowd_packet_id`,`crowd_packet_name`,`effective_start_date`,`effective_end_date`,`single_assign_upper_limit_str`,`rule_type`,`workbench_type`)
        value
        (#{clewLayerId},#{clewLayerName},#{crowdPacketId},#{crowdPacketName},#{effectiveStartDate},#{effectiveEndDate},#{singleAssignUpperLimitStr},#{ruleType},#{workbenchType})
    </insert>
    <update id="updateClewlayerassignrule">
        update <include refid="tableName"/>
        set clew_layer_id=#{clewLayerId},
            clew_layer_name=#{clewLayerName},
            crowd_packet_id=#{crowdPacketId},
            crowd_packet_name=#{crowdPacketName},
            effective_start_date=#{effectiveStartDate},
            effective_end_date=#{effectiveEndDate},
            single_assign_upper_limit_str=#{singleAssignUpperLimitStr},
            rule_type=#{ruleType},
            workbench_type=#{workbenchType}
        where id = #{id}
    </update>
    <update id="delClewlayerassignrule">
        update <include refid="tableName"/>
        set is_delete = 1
        where id = #{id}
    </update>
    <select id="getclewlayerassignruleByCrowdPacketId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerAssignRuleDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and crowd_packet_id = #{crowdPacketId}
    </select>
    <update id="updateSingleAssignUpperLimitStr">
        update <include refid="tableName"/>
        set single_assign_upper_limit_str=#{singleAssignUpperLimitStr}
        where clew_layer_id=#{clewLayerId} and is_delete = 0
    </update>

    <select id="getClewlayerassignruleByClewlayerId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerAssignRuleDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and clew_layer_id = #{clewLayerId}
    </select>
    <select id="getEffectiveAssignRuleList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerAssignRuleDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and `effective_start_date`<![CDATA[<=]]> #{currentTime} and `effective_end_date` >= #{currentTime}
    </select>
</mapper>
