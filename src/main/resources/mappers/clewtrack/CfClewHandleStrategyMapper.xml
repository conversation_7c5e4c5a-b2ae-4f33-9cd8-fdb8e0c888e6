<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewHandleStrategyDao">
    <sql id="TABLE_NAME">cf_clew_handle_strategy</sql>
    <sql id="fields">id,team_id,packet_id,time_interval_start_time,time_interval_end_time,valid_time,primary_channel,control_code,create_time,update_time,is_delete</sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
      insert into <include refid="TABLE_NAME"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId!=null">
              team_id,
            </if>
            <if test="packetId!=null">
              packet_id,
            </if>
            <if test="timeIntervalStartTime!=null and timeIntervalStartTime!=''">
              time_interval_start_time,
            </if>
            <if test="timeIntervalEndTime!=null and timeIntervalEndTime!=''">
              time_interval_end_time,
            </if>
            <if test="validTime!=null">
              valid_time,
            </if>
            <if test="primaryChannel!=null">
              primary_channel,
            </if>
            <if test="controlCode!=null">
              control_code,
            </if>
            <if test="isDelete!=null">
              is_delete,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId!=null">
                #{teamId},
            </if>
            <if test="packetId!=null">
                #{packetId},
            </if>
            <if test="timeIntervalStartTime!=null and timeIntervalStartTime!=''">
                #{timeIntervalStartTime},
            </if>
            <if test="timeIntervalEndTime!=null and timeIntervalEndTime!=''">
                #{timeIntervalEndTime},
            </if>
            <if test="validTime!=null">
                #{validTime},
            </if>
            <if test="primaryChannel!=null">
                #{primaryChannel},
            </if>
            <if test="controlCode!=null">
                #{controlCode},
            </if>
            <if test="isDelete!=null">
                #{isDelete},
            </if>
        </trim>
    </insert>
    <select id="getCfClewHandleStrategyDO"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewHandleStrategyDO">
        select <include refid="fields"/> from <include refid="TABLE_NAME"/>
        where team_id=#{teamId} and packet_id = #{packetId}
    </select>
    <update id="updateCfClewHandleStrategyDO" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewHandleStrategyDO">
        update <include refid="TABLE_NAME"/>
        <set>
            <if test="primaryChannel!=null">
                primary_channel = #{primaryChannel},
            </if>
            <if test="controlCode!=null">
                control_code = #{controlCode},
            </if>
            <if test="timeIntervalStartTime!=null and timeIntervalStartTime!=''">
                time_interval_start_time = #{timeIntervalStartTime},
            </if>
            <if test="timeIntervalEndTime!=null and timeIntervalEndTime!=''">
                time_interval_end_time = #{timeIntervalEndTime},
            </if>
            <if test="validTime!=null">
                valid_time = #{validTime},
            </if>
            <if test="isDelete!=null">
                is_delete = #{isDelete},
            </if>
        </set>
        where team_id = #{teamId} and packet_id = #{packetId}
    </update>
    <delete id="delCfClewHandleStrategyByTeamIdAndPacketId">
        delete from <include refid="TABLE_NAME"/> where team_id = #{teamId} and packet_id = #{packetId}
    </delete>

</mapper>
