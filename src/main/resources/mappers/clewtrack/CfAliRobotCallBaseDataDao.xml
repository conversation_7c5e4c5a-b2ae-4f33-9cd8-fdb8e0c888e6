<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfAliRobotCallBaseDataDao">
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.clewtrack.domain.CfAliRobotCallBaseDataDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="job_id" property="jobId" jdbcType="VARCHAR"/>
        <result column="job_group_id" property="jobGroupId" jdbcType="VARCHAR"/>
        <result column="encrypt_phone" property="encryptPhone" jdbcType="VARCHAR"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="clew_id" property="clewId" jdbcType="BIGINT"/>
        <result column="redial_num" property="redialNum" jdbcType="INTEGER"/>
        <result column="call_type" property="callType" jdbcType="INTEGER"/>
        <result column="call_result" property="callResult" jdbcType="VARCHAR"/>
        <result column="call_ab_tag" property="callAbTag" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="tableName">
        `cf_ali_robot_call_base_data`
    </sql>

    <sql id="Base_Column_List">
          id,
          job_id,
          job_group_id,
          encrypt_phone,
          task_id,
          clew_id,
          redial_num,
          call_type,
          call_result,
          call_ab_tag,
          create_time,
          update_time,
          is_delete
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfAliRobotCallBaseDataDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobId != null">
                job_id,
            </if>
            <if test="jobGroupId != null">
                job_group_id,
            </if>
            <if test="encryptPhone != null">
                encrypt_phone,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="clewId != null">
                clew_id,
            </if>
            <if test="redialNum != null">
                redial_num,
            </if>
            <if test="callAbTag != null">
                call_ab_tag,
            </if>
            <if test="callResult != null">
                call_result,
            </if>
            <if test="callType != null">
                call_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">
                #{jobId,jdbcType=VARCHAR},
            </if>
            <if test="jobGroupId != null">
                #{jobGroupId,jdbcType=VARCHAR},
            </if>
            <if test="encryptPhone != null">
                #{encryptPhone,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="clewId != null">
                #{clewId,jdbcType=BIGINT},
            </if>
            <if test="redialNum != null">
                #{redialNum,jdbcType=INTEGER},
            </if>
            <if test="callAbTag != null">
                #{callAbTag,jdbcType=VARCHAR},
            </if>
            <if test="callResult != null">
                #{callResult,jdbcType=VARCHAR},
            </if>
            <if test="callType != null">
                #{callType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="getByJobId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE job_id = #{jobId,jdbcType=VARCHAR}
        AND is_delete = 0
        LIMIT 1
    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfAliRobotCallBaseDataDO">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="callResult != null">
                call_result = #{callResult,jdbcType=VARCHAR},
            </if>
            <if test="redialNum != null">
                redial_num = #{redialNum,jdbcType=INTEGER},
            </if>
            <if test="jobId != null and jobId != ''">
                job_id = #{jobId,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
        AND is_delete = 0
    </update>

    <select id="getByPhone" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE encrypt_phone = #{encryptPhone,jdbcType=VARCHAR}
        AND is_delete = 0
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="getByPhoneAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE encrypt_phone = #{encryptPhone,jdbcType=VARCHAR}
        AND call_type = #{callType,jdbcType=INTEGER}
        AND is_delete = 0
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="getByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE task_id = #{taskId,jdbcType=BIGINT}
        AND is_delete = 0
        ORDER BY id DESC
    </select>

    <select id="getByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE create_time >= #{startTime,jdbcType=TIMESTAMP}
        AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        AND is_delete = 0
        AND call_type = #{callType,jdbcType=INTEGER}
        ORDER BY id DESC
    </select>
</mapper> 