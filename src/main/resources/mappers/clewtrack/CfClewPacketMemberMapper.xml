<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewPacketMemberDao">
    <sql id="baseResult">
        id,
        create_time,
        packet_id,
        cf_clew_tracker_id,
        user_id,
        user_name,
        promise_service_time_range,
        max_handle_perday,
        last_assgin_clew_time,
        today_assgin_clew_count,
        today_handle_clew_count,
        member_role,
        today_wait_call_clew_count,
        today_max_assign_clew_count,
        is_delete,
        work_content_type,
        online_time,
        latest_online,
        latest_suspend,
        today_can_assign_max_num,
        today_can_assign_min_num,
        current_can_assign_max_num,
        current_can_assign_min_num,
        clew_assign_gap
    </sql>

    <sql id="tableName">cf_clew_packet_member</sql>

    <sql id="table_join_user_info">cf_clew_track_user_info</sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into <include refid="tableName"/>
    (packet_id,create_time,cf_clew_tracker_id,user_id,user_name,promise_service_time_range,max_handle_perday,member_role)
    values
    (#{packetId},#{createTime},#{cfClewTrackerId},#{userId},#{userName},#{promiseServiceTimeRange},#{maxHandlePerday},
        #{memberRole})
    </insert>
    <update id="handleUserPacketMemberWaitCallClew">
        update <include refid="tableName"/>
        set today_wait_call_clew_count=if(to_days(last_assgin_clew_time)=to_days(#{date}),
        today_wait_call_clew_count-1,0)
        where user_id=#{targetUserid} and packet_id=#{packetId} and today_wait_call_clew_count>0
    </update>
    <update id="updateUserPacketMemberWaitCallClewLastAssginTime">
        update <include refid="tableName"/>
        set today_wait_call_clew_count=if(to_days(last_assgin_clew_time)=to_days(#{date}),
        today_wait_call_clew_count+1,1)
        where user_id=#{targetUserid} and packet_id=#{packetId}
    </update>
    <update id="addUserPacketMemberAssginClewCount">
        update <include refid="tableName"/>
        set today_assgin_clew_count=if(to_days(last_assgin_clew_time)=to_days(#{date}),
        today_assgin_clew_count+1,1),last_assgin_clew_time=#{date}
        where user_id=#{targetUserid} and packet_id=#{packetId}
    </update>
    <update id="subUserPacketMemberAssginClewCount">
        update <include refid="tableName"/>
        set today_assgin_clew_count=if(to_days(last_assgin_clew_time)=to_days(#{date}),
        today_assgin_clew_count-1,0)
        where user_id=#{targetUserid} and packet_id=#{packetId} and today_assgin_clew_count>0
    </update>
    <update id="addTodayHandleClewCount">
        update <include refid="tableName"/>
        set today_handle_clew_count = if(to_days(last_assgin_clew_time)=to_days(#{date}),
        today_handle_clew_count+1,1)
        where user_id=#{targetUserid} and packet_id=#{packetId}
    </update>
    <update id="updateUserPacketMembelTodayCount">
        update <include refid="tableName"/>
        set today_assgin_clew_count=if(to_days(last_assgin_clew_time)=to_days(now()),
        today_assgin_clew_count,0),today_handle_clew_count=if(to_days(last_assgin_clew_time)=to_days(now()),
        today_handle_clew_count,0)
        where
        user_id in
        <if test="userIds != null and userIds.size>0">
          <foreach collection="userIds" item="userId" separator="," open="(" close=")">
              #{userId}
          </foreach>
        </if>
        and packet_id in
        <if test="packetIds != null and packetIds.size>0">
            <foreach collection="packetIds" item="packetId" separator="," open="(" close=")">
                #{packetId}
            </foreach>
        </if>
    </update>
    <select id="getByUserIdAndPacketIdList"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
    select <include refid="baseResult"/>
    from <include refid="tableName"/>
    where user_id=#{targetUserid} and packet_id in
        <foreach collection="packetIdList" item="packetId" open="(" separator="," close=")">
            #{packetId}
        </foreach>
    </select>


    <select id="getWhUserListByAssginRule" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
    select
      pm.id,
      pm.packet_id,
      pm.cf_clew_tracker_id,
      pm.user_id,
      pm.user_name,
      pm.promise_service_time_range,
      pm.max_handle_perday,
      pm.last_assgin_clew_time,
      pm.today_assgin_clew_count,
      pm.member_role,
      pm.today_handle_clew_count,
      if(to_days(ctu.last_handle_online_time)=to_days(now()), pm.today_assgin_clew_count+pm.today_wait_call_clew_count,0) as today_unhandle_clew_count,
      pm.today_max_assign_clew_count as today_max_assign_clew_count,
<!--      ctu.qy_wechat_user_id,-->
      ctu.qy_wechat_user_id_encrypt,
      pm.work_content_type,
      ctu.is_top
    from <include refid="tableName" /> pm
    join <include refid="table_join_user_info" /> ctu on pm.user_id=ctu.user_id
    where ctu.tracker_type=1 and <![CDATA[ ((ctu.online_status>>(#{workbenchType}*3))&7)=1 ]]>
    and pm.member_role in (1,2)
    and pm.max_handle_perday > pm.today_assgin_clew_count
    and pm.today_max_assign_clew_count-pm.today_assgin_clew_count-pm.today_handle_clew_count>0
    and to_days(ctu.last_handle_online_time)=to_days(now())
    and ctu.is_delete = 0
    and pm.is_delete = 0
    and pm.work_content_type = #{workContentType}
    and pm.packet_id = #{packetId}
    order by today_assgin_clew_count, last_assgin_clew_time asc
    </select>
    <select id="getWhUserListByAssginRuleWithUserIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
    select
      pm.id,
      pm.packet_id,
      pm.cf_clew_tracker_id,
      pm.user_id,
      pm.user_name,
      pm.promise_service_time_range,
      pm.max_handle_perday,
      pm.last_assgin_clew_time,
      pm.today_assgin_clew_count,
      pm.member_role,
      pm.today_handle_clew_count,
      if(to_days(ctu.last_handle_online_time)=to_days(now()), pm.today_assgin_clew_count+pm.today_wait_call_clew_count,0) as today_unhandle_clew_count,
      pm.today_max_assign_clew_count as today_max_assign_clew_count,
<!--      ctu.qy_wechat_user_id,-->
      ctu.qy_wechat_user_id_encrypt,
      pm.work_content_type,
      ctu.is_top
    from <include refid="tableName" /> pm
    join <include refid="table_join_user_info" /> ctu on pm.user_id=ctu.user_id
    where ctu.tracker_type=1 and <![CDATA[ ((ctu.online_status>>(#{workbenchType}*3))&7)=1 ]]>
    and pm.member_role in (1,2)
    and pm.max_handle_perday > pm.today_assgin_clew_count
    and to_days(ctu.last_handle_online_time)=to_days(now())
    and ctu.is_delete = 0
    and pm.is_delete = 0
    and pm.packet_id = #{packetId}
    and pm.user_id in <foreach collection="clewUserIds" item="clewUserId" separator="," open="(" close=")">#{clewUserId}</foreach>
    <if test="occupyStatus!=0">
        and pm.today_max_assign_clew_count-pm.today_assgin_clew_count-pm.today_handle_clew_count>0
    </if>
    </select>

    <select id="getFuWuUserListByAssginRule" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select
        pm.id,
        pm.packet_id,
        pm.cf_clew_tracker_id,
        pm.user_id,
        pm.user_name,
        pm.promise_service_time_range,
        pm.max_handle_perday,
        pm.last_assgin_clew_time,
        pm.today_assgin_clew_count,
        pm.member_role,
        pm.today_handle_clew_count,
        if(to_days(ctu.last_handle_online_time)=to_days(now()), pm.today_assgin_clew_count+pm.today_wait_call_clew_count,0) as today_unhandle_clew_count,
        pm.today_max_assign_clew_count as today_max_assign_clew_count,
<!--        ctu.qy_wechat_user_id,-->
        ctu.qy_wechat_user_id_encrypt,
        pm.work_content_type,
        ctu.is_top,
        pm.current_can_assign_max_num,
        pm.clew_assign_gap
        from <include refid="tableName" /> pm
        join <include refid="table_join_user_info" /> ctu on pm.user_id=ctu.user_id
        where ctu.tracker_type=2
        and <![CDATA[ ((ctu.online_status>>(#{workbenchType}*3))&7)=1 ]]>
        and pm.member_role in (1,2)
        and pm.max_handle_perday > pm.today_assgin_clew_count
        and to_days(ctu.last_handle_online_time)=to_days(now())
        and pm.today_max_assign_clew_count-pm.today_assgin_clew_count-pm.today_handle_clew_count>0
        and ctu.is_delete = 0
        and pm.is_delete = 0
        and pm.work_content_type=#{workContentType}
        and packet_id = #{packetId}
        order by today_assgin_clew_count,last_assgin_clew_time asc
    </select>

    <select id="getPacketMemberByPacketIdAndUserId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select <include refid="baseResult"/>
        from <include refid="tableName" />
        where packet_id=#{packetId} and user_id=#{userId}
        limit 1
    </select>
    <select id="getPacketMemberByPacketIdAndTrackerId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where packet_id=#{packetId} and cf_clew_tracker_id=#{trackerId}
        limit 1
    </select>

    <update id="updateUserPacketMembelRole">
        update <include refid="tableName"/>
        set member_role = #{memberRole} , is_delete = 0
        where packet_id=#{packetId} and cf_clew_tracker_id=#{trackerId}
    </update>
    <update id="updateMaxHandlePerday">
        update <include refid="tableName"/>
        set max_handle_perday = #{maxHandlePerday}
        where userId in
        <foreach collection="userIds" separator="," open="(" close=")">
            #{userId}
        </foreach>
        <if test="packetId!=null">
            packet_id = #{packetId}
        </if>
    </update>
    <update id="resetPackeMemberCount">
        update <include refid="tableName"/>
        set today_assgin_clew_count=0 ,  today_wait_call_clew_count=0
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="getWHPacketMemberByMemberRoleAndUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select
            ccpm.id as id,
            ccpm.create_time as create_time,
            ccpm.packet_id as packet_id,
            ccpm.cf_clew_tracker_id as cf_clew_tracker_id,
            ccpm.user_id as user_id,
            ccpm.user_name as user_name,
            ccpm.promise_service_time_range as promise_service_time_range,
            ccpm.max_handle_perday as max_handle_perday,
            ccpm.last_assgin_clew_time as last_assgin_clew_time,
            ccpm.today_assgin_clew_count as today_assgin_clew_count,
            ccpm.member_role as member_role,
            ccpm.work_content_type,
            cctui.is_top
        from <include refid="tableName" /> ccpm
        join <include refid="table_join_user_info" /> cctui on ccpm.cf_clew_tracker_id=cctui.id
        join cf_clew_packet ccp on ccpm.packet_id = ccp.id
        where 1=1
        <if test="memberRole!=null">
          and ccpm.member_role=#{memberRole}
        </if>
         and ccpm.user_id=#{userId} and cctui.tracker_type=#{userType} and ccp.workbench_type=#{workbenchType}
        limit 1
    </select>
    <update id="clearUserWorkcount">
        update <include refid="tableName" /> set today_assgin_clew_count=0
        where user_id=#{userId} and packet_id in
        <foreach collection="packetIds" item="packetId" separator="," open="(" close=")">
            #{packetId}
        </foreach>
    </update>

    <select id="getPacketMemberByPacketIdAndClewTypeAndOnlineStatus" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        SELECT DISTINCT a.id,
          a.create_time,
          a.packet_id,
          a.cf_clew_tracker_id,
          a.user_id,
          a.user_name,
          a.promise_service_time_range,
          a.max_handle_perday,
          a.last_assgin_clew_time,
          a.today_assgin_clew_count,
          a.today_handle_clew_count,
          a.member_role,
          a.today_wait_call_clew_count,
          a.today_max_assign_clew_count,
          a.work_content_type,
          a.is_delete,
          b.is_top
        FROM
            <include refid="tableName" /> a
            JOIN <include refid="table_join_user_info" /> b ON a.user_id = b.user_id
        WHERE a.packet_id=#{packetId} and <![CDATA[ ((b.online_status>>(#{clewType}*3))&7) in ]]>
        <foreach collection="onlineStatusList" item="onlineStatus" open="(" close=")" separator=",">
            #{onlineStatus}
        </foreach>
        and b.tracker_type=#{trackerType}
        <if test="workContentType != null">
            and a.work_content_type= #{workContentType}
        </if>
        and a.is_delete = 0
        and b.is_delete = 0
    </select>
    <update id="updateTodayMaxAssignClewCountByPacketIdAndUserIds">
        update <include refid="tableName"/>
        <set>
            <if test="todayCanAssignMaxNum != null">
                today_can_assign_max_num = #{todayCanAssignMaxNum},
            </if>
            <if test="todayCanAssignMinNum != null">
                today_can_assign_min_num = #{todayCanAssignMinNum},
            </if>
            <if test="currentCanAssignMaxNum != null">
                current_can_assign_max_num = #{currentCanAssignMaxNum},
            </if>
            <if test="currentCanAssignMinNum != null">
                current_can_assign_min_num = #{currentCanAssignMinNum},
            </if>
            <if test="todayMaxAssignClewCount != null">
                today_max_assign_clew_count = #{todayMaxAssignClewCount},
            </if>
            <if test="clewAssignGap != null">
                clew_assign_gap = #{clewAssignGap}
            </if>
        </set>
        where packet_id=#{packetId} and user_id in
        <foreach collection="userIds" item="userId" close=")" open="(" separator=",">
            #{userId}
        </foreach>
    </update>

    <update id="updateUserPacketMembelWorkContentType">
        update <include refid="tableName"/>
        set work_content_type = #{workContentType}
        where
        user_id in
        <if test="userIds != null and userIds.size>0">
            <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        and packet_id in
        <if test="packetIds != null and packetIds.size>0">
            <foreach collection="packetIds" item="packetId" separator="," open="(" close=")">
                #{packetId}
            </foreach>
        </if>
    </update>

    <select id="getPacketMemberByPacketIdAndUserIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where packet_id=#{packetId}
        <if test="userIds!=null and userIds.size>0">
            and user_id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="getCfClewPacketMembersByUserIdAndPacketIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where user_id=#{userId}
        <if test="packetIds!=null and packetIds.size>0">
            and packet_id in
            <foreach collection="packetIds" item="packetId" open="(" separator="," close=")">
                #{packetId}
            </foreach>
        </if>
    </select>
    <select id="getUserIdsByWorkContentType" resultType="java.lang.String">
        select user_id
        from <include refid="tableName"/>
        where is_delete = 0 and work_content_type = #{workContentType}
    </select>

    <select id="listUserInfoByWorkContentTypes" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0 and work_content_type in
        <foreach collection="workContentTypes" item="workContentType" separator="," open="(" close=")">
              #{workContentType}
         </foreach>
    </select>

    <select id="geClewPacketMemberByTrackerUserInfoSearchParam"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where 1=1
        <if test="userId != null and userId != ''">
            and user_id=#{userId}
        </if>
        <if test="packetId != null ">
            and packet_id=#{packetId}
        </if>
        <if test="memberRole != null">
            and member_role=#{memberRole}
        </if>
        <if test="isDelete != null " >
            and is_delete = #{isDelete}
        </if>
    </select>


    <select id="getAllPacketMemberByPacketId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        SELECT <include refid="baseResult"/>
        FROM <include refid="tableName"/>
        WHERE is_delete = 0
          and packet_id=#{packetId}
    </select>

    <select id="geClewPacketMemberByPacketIdAndWorkContentType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        SELECT <include refid="baseResult"/>
        FROM <include refid="tableName"/>
        WHERE is_delete = 0
        <if test="packetId != null ">
            and packet_id=#{packetId}
        </if>
        <if test="workContentType != null">
            and work_content_type=#{workContentType}
        </if>
    </select>
    <select id="countValidMemberByWorkContentType" resultType="java.lang.Long">
        SELECT count(pm.id)
        FROM <include refid="tableName"/> as pm
        left join <include refid="table_join_user_info" /> ctu on ctu.user_id = pm.user_id
        WHERE ctu.is_delete = 0 and pm.is_delete = 0
          and pm.work_content_type = #{workContentType}
          and pm.packet_id = #{packetId}
    </select>
    <select id="getErrorCountPacketMemberList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        SELECT
        <include refid="baseResult"/>
        FROM
        <include refid="tableName"/>
        where packet_id=#{packetId}
        and work_content_type = #{workContentType}
        AND last_assgin_clew_time> #{currentDay}
        and <![CDATA[ max_handle_perday-today_assgin_clew_count<=0 ]]>
    </select>

    <select id="getErrorCountUserIdList" resultType="java.lang.String">
        select user_id
        FROM <include refid="tableName"/>
        WHERE packet_id = #{packetId}
        and work_content_type = #{workContentType}
        and last_assgin_clew_time > #{currentDay}
        and today_assgin_clew_count >= max_handle_perday
    </select>

    <select id="getFuWuUserListByAssginRuleWithUserIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select
        pm.id,
        pm.packet_id,
        pm.cf_clew_tracker_id,
        pm.user_id,
        pm.user_name,
        pm.promise_service_time_range,
        pm.max_handle_perday,
        pm.last_assgin_clew_time,
        pm.today_assgin_clew_count,
        pm.member_role,
        pm.today_handle_clew_count,
        if(to_days(ctu.last_handle_online_time)=to_days(now()), pm.today_assgin_clew_count+pm.today_wait_call_clew_count,0) as today_unhandle_clew_count,
        pm.today_max_assign_clew_count as today_max_assign_clew_count,
<!--        ctu.qy_wechat_user_id,-->
        ctu.qy_wechat_user_id_encrypt,
        pm.work_content_type,
        ctu.is_top
        from <include refid="tableName" /> pm
        join <include refid="table_join_user_info" /> ctu on pm.user_id=ctu.user_id
        where ctu.tracker_type=2
        and <![CDATA[ ((ctu.online_status>>(#{workbenchType}*3))&7)=1 ]]>
        and pm.member_role in (1,2)
        and pm.max_handle_perday > pm.today_assgin_clew_count
        and to_days(ctu.last_handle_online_time)=to_days(now())
        and ctu.is_delete = 0
        and pm.is_delete = 0
        and packet_id = #{packetId}
        and pm.user_id in <foreach collection="clewUserIds" item="clewUserId" open="(" close=")" separator=",">#{clewUserId}</foreach>
        <if test="occupyStatus!=0">
            and pm.today_max_assign_clew_count-pm.today_assgin_clew_count-pm.today_handle_clew_count>0
        </if>

    </select>
    <select id="getAssignCeilingPacketMember"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select
          pm.id,
          pm.packet_id,
          pm.cf_clew_tracker_id,
          pm.user_id,
          pm.user_name,
          pm.promise_service_time_range,
          pm.max_handle_perday,
          pm.last_assgin_clew_time,
          pm.today_assgin_clew_count,
          pm.member_role,
          pm.today_handle_clew_count,
          if(to_days(ctu.last_handle_online_time)=to_days(now()), pm.today_assgin_clew_count+pm.today_wait_call_clew_count,0) as today_unhandle_clew_count,
          pm.today_max_assign_clew_count as today_max_assign_clew_count,
<!--          ctu.qy_wechat_user_id,-->
          ctu.qy_wechat_user_id_encrypt,
          pm.work_content_type,
          ctu.is_top
        from <include refid="tableName" /> pm
        join <include refid="table_join_user_info" /> ctu on pm.user_id=ctu.user_id
        where ctu.tracker_type=1 and <![CDATA[ ((ctu.online_status>>(#{workbenchType}*3))&7)=1 ]]>
        and pm.member_role in (1,2)
        and pm.max_handle_perday > pm.today_assgin_clew_count
        and <![CDATA[ pm.today_max_assign_clew_count-pm.today_assgin_clew_count-pm.today_handle_clew_count <= 0 ]]>
        and to_days(ctu.last_handle_online_time)=to_days(now())
        and ctu.is_delete = 0
        and pm.is_delete = 0
        and pm.work_content_type = #{workContentType}
        and pm.packet_id = #{packetId}
        order by today_unhandle_clew_count asc
    </select>

    <update id="updateTodayMaxAssignClewCountForGrade">
        update <include refid="tableName" />
        <set>
            <if test="todayCanAssignMaxNum != null">
                today_can_assign_max_num = #{todayCanAssignMaxNum},
            </if>
            <if test="todayCanAssignMinNum != null">
                today_can_assign_min_num = #{todayCanAssignMinNum},
            </if>
            <if test="currentCanAssignMaxNum != null">
                current_can_assign_max_num = #{currentCanAssignMaxNum},
            </if>
            <if test="currentCanAssignMinNum != null">
                current_can_assign_min_num = #{currentCanAssignMinNum},
            </if>
            <if test="todayMaxAssignClewCount != null">
                today_max_assign_clew_count = #{todayMaxAssignClewCount},
            </if>
            <if test="clewAssignGap != null">
                clew_assign_gap = #{clewAssignGap}
            </if>
        </set>
        where work_content_type =31 and is_delete=0
        and user_id in <foreach collection="userIds" item="userId" open="(" close=")" separator=",">#{userId}</foreach>
    </update>

    <update id="updateDeletePakcetMember">
        update <include refid="tableName" />
        set is_delete = 1
        where id = #{id}
    </update>

    <select id="getCanAssignUserListByAssginRule"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select
        pm.id,
        pm.packet_id,
        pm.cf_clew_tracker_id,
        pm.user_id,
        pm.user_name,
        pm.promise_service_time_range,
        pm.max_handle_perday,
        pm.last_assgin_clew_time,
        pm.today_assgin_clew_count,
        pm.member_role,
        pm.today_handle_clew_count,
        if(to_days(ctu.last_handle_online_time)=to_days(now()), pm.today_assgin_clew_count+pm.today_wait_call_clew_count,0) as today_unhandle_clew_count,
        pm.today_max_assign_clew_count as today_max_assign_clew_count,
<!--        ctu.qy_wechat_user_id,-->
        ctu.qy_wechat_user_id_encrypt,
        pm.work_content_type,
        ctu.is_top
        from <include refid="tableName" /> pm
        join <include refid="table_join_user_info" /> ctu on pm.user_id=ctu.user_id
        where ctu.tracker_type= #{trackerType}
        and <![CDATA[ ((ctu.online_status>>(#{workbenchType}*3))&7)=1 ]]>
        and pm.member_role in (1,2)
        and pm.max_handle_perday > pm.today_assgin_clew_count
        and to_days(ctu.last_handle_online_time)=to_days(now())
        and pm.today_max_assign_clew_count-pm.today_assgin_clew_count-pm.today_handle_clew_count>0
        and ctu.is_delete = 0
        and pm.is_delete = 0
        and pm.work_content_type=#{workContentType}
        and packet_id = #{packetId}
        order by today_assgin_clew_count,last_assgin_clew_time asc
    </select>

    <update id="updateWhUnderClewCount">
        update  <include refid="tableName" />
        set max_handle_perday = #{underClewCount}
        where is_delete = 0 and packet_id = #{packetId}
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <select id="getClewPacketMemberByUserIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketMemberDO">
        select <include refid="baseResult"/>
            from <include refid="tableName"/>
            where is_delete = 0
            and user_id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        and packet_id = #{packetId}
    </select>

</mapper>
