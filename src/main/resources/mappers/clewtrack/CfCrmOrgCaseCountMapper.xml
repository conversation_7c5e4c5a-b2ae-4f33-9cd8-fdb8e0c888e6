<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfCrmOrgCountDao">
    <sql id="table_name">cf_crm_org_case_count</sql>
    <sql id="fields">
        id,
        date_time,
        amount,
        org_id,
        org_name,
        quantity,
        create_time,
        update_time,
        is_delete
    </sql>
    <sql id="insert_field">
        date_time,
        amount,
        org_id,
        org_name,
        quantity
    </sql>
    <insert id="batchInsert" >
        insert into <include refid="table_name"/>
        (<include refid="insert_field"/>)
        values
        <foreach collection="cfCrmOrgCaseCountDOs" item="cfCrmOrgCaseCountDO" separator="," >
            (#{cfCrmOrgCaseCountDO.dateTime},#{cfCrmOrgCaseCountDO.amount},#{cfCrmOrgCaseCountDO.orgId},#{cfCrmOrgCaseCountDO.orgName},#{cfCrmOrgCaseCountDO.quantity})
        </foreach>
    </insert>
    <update id="batchUpdateCfCrmOrgCaseCountDO">
        update <include refid="table_name"/>
        set quantity = case id
        <foreach collection="cfCrmOrgCaseCountDOs" item="cfCrmOrgCaseCountDO">
            when #{cfCrmOrgCaseCountDO.id} then #{cfCrmOrgCaseCountDO.quantity}
        </foreach>
        end
        where id in
        <foreach collection="cfCrmOrgCaseCountDOs" item="cfCrmOrgCaseCountDO" open="(" close=")" separator=",">
            #{cfCrmOrgCaseCountDO.id}
        </foreach>
    </update>
    <select id="getCfCrmOrgCaseCountDOByDateTimeWithAmountWithOrgId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCrmOrgCaseCountDO">
        select <include refid="fields"/>
        from <include refid="table_name"/>
        where date_time=#{dateTime} and amount=#{amount} and org_id=#{orgId} and is_delete=0
    </select>
    <select id="getCfCrmOrgCaseCountDOByDateTimesWithAmountWithOrgIds" resultType="com.shuidihuzhu.cf.clewtrack.result.BdOrgCaseCountResult">
        select
          org_id as orgId,
          sum(quantity) as caseCount
        from <include refid="table_name"/>
        where date_time >= #{dateTimeStart} and <![CDATA[ date_time <= #{dateTimeEnd} ]]>
        <if test="amount != null">
          and amount=#{amount}
        </if>
        and is_delete=0
        and org_id in
        <foreach collection="orgIds" item="orgId" open="(" close=")" separator=",">
            #{orgId}
        </foreach>
        group by org_id
    </select>
    <update id="updateIsdelete">
        update <include refid="table_name"/> set is_delete = 1
        where id in <foreach collection="ids" item="id" separator="," open="(" close=")">#{id}</foreach>
    </update>
</mapper>

