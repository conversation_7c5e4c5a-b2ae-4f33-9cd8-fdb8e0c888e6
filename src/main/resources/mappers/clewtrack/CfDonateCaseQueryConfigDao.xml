<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.donate.CfDonateCaseQueryConfigDao">
    <sql id="tableName">cf_donate_case_query_config</sql>
    <sql id="fields">
       `id`,
       `donate_case_query_config`,
       `create_time`,
       `update_time`,
       `is_delete`
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateCaseQueryConfigDO" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="donateCaseQueryConfig != null and donateCaseQueryConfig != ''">
                `donate_case_query_config`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="donateCaseQueryConfig != null and donateCaseQueryConfig != ''">
                #{donateCaseQueryConfig},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateCaseQueryConfigDO">
        update <include refid="tableName"/>
        <set>
            <if test="donateCaseQueryConfig!= null and donateCaseQueryConfig!= ''">
                `donate_case_query_config` = #{donateCaseQueryConfig}
            </if>
        </set>
        where `id`= #{id}
    </update>

    <select id="selectConfig" resultType = "com.shuidihuzhu.cf.clewtrack.domain.CfDonateCaseQueryConfigDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where `is_delete` = 0
        limit 1
    </select>
</mapper>