<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfAliRobotCallRecordDao">
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.clewtrack.domain.CfAliRobotCallRecordDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="call_base_id" property="callBaseId" jdbcType="BIGINT"/>
        <result column="call_task_id" property="callTaskId" jdbcType="VARCHAR"/>
        <result column="call_task_status" property="callTaskStatus" jdbcType="VARCHAR"/>
        <result column="call_start_time" property="callStartTime" jdbcType="TIMESTAMP"/>
        <result column="call_end_time" property="callEndTime" jdbcType="TIMESTAMP"/>
        <result column="call_duration" property="callDuration" jdbcType="VARCHAR"/>
        <result column="record_file" property="recordFile" jdbcType="VARCHAR"/>
        <result column="ringing_duration" property="ringingDuration" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="tableName">
        `cf_ali_robot_call_record`
    </sql>

    <sql id="Base_Column_List">
          id,
          call_base_id,
          call_task_id,
          call_task_status,
          call_start_time,
          call_end_time,
          call_duration,
          record_file,
          ringing_duration,
          create_time,
          update_time,
          is_delete
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfAliRobotCallRecordDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="callBaseId != 0">
                call_base_id,
            </if>
            <if test="callTaskId != null">
                call_task_id,
            </if>
            <if test="callTaskStatus != null">
                call_task_status,
            </if>
            <if test="callStartTime != null">
                call_start_time,
            </if>
            <if test="callEndTime != null">
                call_end_time,
            </if>
            <if test="callDuration != null">
                call_duration,
            </if>
            <if test="recordFile != null">
                record_file,
            </if>
            <if test="ringingDuration != null">
                ringing_duration,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="callBaseId != 0">
                #{callBaseId,jdbcType=BIGINT},
            </if>
            <if test="callTaskId != null">
                #{callTaskId,jdbcType=VARCHAR},
            </if>
            <if test="callTaskStatus != null">
                #{callTaskStatus,jdbcType=VARCHAR},
            </if>
            <if test="callStartTime != null">
                #{callStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="callEndTime != null">
                #{callEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="callDuration != null">
                #{callDuration,jdbcType=VARCHAR},
            </if>
            <if test="recordFile != null">
                #{recordFile,jdbcType=VARCHAR},
            </if>
            <if test="ringingDuration != null">
                #{ringingDuration,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/>
        (
            call_base_id,
            call_task_id,
            call_task_status,
            call_start_time,
            call_end_time,
            call_duration,
            record_file,
            ringing_duration
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.callBaseId,jdbcType=BIGINT},
                #{item.callTaskId,jdbcType=VARCHAR},
                #{item.callTaskStatus,jdbcType=VARCHAR},
                #{item.callStartTime,jdbcType=TIMESTAMP},
                #{item.callEndTime,jdbcType=TIMESTAMP},
                #{item.callDuration,jdbcType=INTEGER},
                #{item.recordFile,jdbcType=VARCHAR},
                #{item.ringingDuration,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <select id="getByCallTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE call_task_id = #{callTaskId,jdbcType=VARCHAR}
        AND is_delete = 0
        LIMIT 1
    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfAliRobotCallRecordDO">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="callTaskStatus != null">
                call_task_status = #{callTaskStatus,jdbcType=VARCHAR},
            </if>
            <if test="callStartTime != null">
                call_start_time = #{callStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="callEndTime != null">
                call_end_time = #{callEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="callDuration != null">
                call_duration = #{callDuration,jdbcType=VARCHAR},
            </if>
            <if test="recordFile != null">
                record_file = #{recordFile,jdbcType=VARCHAR},
            </if>
            <if test="ringingDuration != null">
                ringing_duration = #{ringingDuration,jdbcType=VARCHAR},
            </if>
            update_time = now()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
        AND is_delete = 0
    </update>

    <select id="getByCallBaseId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE call_base_id = #{callBaseId,jdbcType=BIGINT}
        AND is_delete = 0
        LIMIT 1
    </select>

    <select id="getByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE call_start_time >= #{startTime,jdbcType=TIMESTAMP}
        AND call_start_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        AND is_delete = 0
        ORDER BY id DESC
    </select>

    <select id="getByCallBaseIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE call_base_id IN
        <foreach collection="callBaseIds" item="callBaseId" open="(" separator="," close=")">
            #{callBaseId}
        </foreach>
        AND is_delete = 0
        ORDER BY id DESC
    </select>
</mapper> 