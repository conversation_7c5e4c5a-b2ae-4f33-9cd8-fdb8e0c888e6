<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfMedicareMedicineDao">
    <sql id="table_name">
        cf_medicare_medicine
    </sql>
    <sql id="fileds">
        id,
        medicare_area,
        identifier,
        medicine_name,
        dosage_forms,
        medicine_type,
        medicare_type,
        supplement_situation,
        remarks,
        create_time,
        update_time,
        is_delete
    </sql>
    <insert id="batchInsert">
        insert into <include refid="table_name"/>
        (medicare_area, identifier, medicine_name, dosage_forms, medicine_type, medicare_type, supplement_situation, remarks)
        values
        <foreach collection="cfMedicareMedicineList" item="cfMedicareMedicine" separator=",">
          (#{cfMedicareMedicine.medicareArea}, #{cfMedicareMedicine.identifier}, #{cfMedicareMedicine.medicineName}, #{cfMedicareMedicine.dosageForms}, #{cfMedicareMedicine.medicineType}, #{cfMedicareMedicine.medicareType}, #{cfMedicareMedicine.supplementSituation}, #{cfMedicareMedicine.remarks})
        </foreach>
    </insert>

    <select id="getListByMedicineNameAndMedicareArea" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfMedicareMedicineDO">
        select <include refid="fileds"/>
        from <include refid="table_name"/>
        where id > #{anchorId}
          <if test="medicareArea != null and medicareArea != ''">
            and medicare_area = #{medicareArea}
          </if>
          <if test="medicineName != null and medicineName != ''">
            and medicine_name like CONCAT('%', #{medicineName}, '%')
          </if>
        limit #{limit}
    </select>

    <select id="getMedicareMedicineIsExist" resultType="java.lang.Integer">
        select count(1)
        from <include refid="table_name"/>
        where medicare_area = #{medicareArea}
        and medicine_name like CONCAT('%', #{medicineName}, '%')
    </select>
    <select id="getMedicineNameByLike" resultType="com.shuidihuzhu.cf.clewtrack.model.MedicineModel">
        select DISTINCT medicine_name,dosage_forms
        from <include refid="table_name"/>
        where medicine_name like CONCAT('%', #{medicineName}, '%')
        and medicare_area = #{medicareArea}
        limit #{offset},#{pageSize}
    </select>
</mapper>
