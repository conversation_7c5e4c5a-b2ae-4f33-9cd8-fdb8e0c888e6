<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfForecastCallTaskChangeInfoDao">

    <sql id="tableName">cf_forecast_call_task_change_info</sql>

    <sql id="baseResult">
      `id`,
      `task_id`,
      `answer_rate`,
      `predict_adjust`,
      `create_time`,
      `update_time`,
	  `is_delete`
    </sql>

    <insert id="insertForecastTaskChangeInfo">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="taskId != null">
                task_id,
            </if>
            <if test="answerRate != null">
                answer_rate,
            </if>
            <if test="predictAdjust != null">
                predict_adjust,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="taskId != null">
                #{taskId} ,
            </if>
            <if test="answerRate != null">
                #{answerRate} ,
            </if>
            <if test="predictAdjust != null">
                #{predictAdjust} ,
            </if>
        </trim>
    </insert>

</mapper>
