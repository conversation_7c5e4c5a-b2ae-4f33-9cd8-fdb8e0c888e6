<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.QyWechatMsgInfoRecordDao">
    <sql id="tableName">qy_wechat_msg_info_record</sql>

    <sql id="fields">
        id,
        encrypt_phone,
        send_time,
        trigger_type,
        reply_time,
        create_time,
        update_time,
        is_delete
    </sql>


    <insert id="insertQyWechatMsgInfoRecord" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.QyWechatMsgInfoDO">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="encryptPhone != null and encryptPhone != ''">
                encrypt_phone,
            </if>
            <if test="sendTime != null">
                send_time,
            </if>
            <if test="triggerType != null">
                trigger_type,
            </if>
            <if test="replyTime != null">
                reply_time
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="encryptPhone != null and encryptPhone != ''">
                #{encryptPhone},
            </if>
            <if test="sendTime != null">
                #{sendTime},
            </if>
            <if test="triggerType != null">
                #{triggerType},
            </if>
            <if test="replyTime != null">
                #{replyTime}
            </if>
        </trim>
    </insert>

    <update id="updateQyWechatMsgInfoRecord">
        update
        <include refid="tableName"/>
        <set>
            <if test="record.encryptPhone != null and record.encryptPhone != ''">
                encrypt_phone = #{record.encryptPhone},
            </if>
            <if test="record.sendTime != null">
                send_time = #{record.sendTime},
            </if>
            <if test="record.triggerType != null">
                trigger_type = #{record.triggerType},
            </if>
            <if test="record.replyTime != null">
                reply_time = #{record.replyTime}
            </if>
        </set>
        where id = #{record.id}
    </update>

    <select id="getQyWechatMsgInfoRecordByPhone" resultType="com.shuidihuzhu.cf.clewtrack.domain.QyWechatMsgInfoDO">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where encrypt_phone = #{encryptPhone}
        and is_delete = 0
        order by id desc
    </select>
</mapper>
