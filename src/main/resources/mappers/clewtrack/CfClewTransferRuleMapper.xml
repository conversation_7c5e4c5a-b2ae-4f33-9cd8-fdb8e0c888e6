<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTransferRuleDao">

    <sql id="tableName">cf_clew_transfer_rule</sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into
    <include refid="tableName"/>
    (source_packet_id,workbench_type)
    values
    <foreach collection="sourcePacketIds" item="sourcePacketId" separator=",">
        (#{sourcePacketId},#{targetTeamId},#{workbenchType})
    </foreach>
    </insert>

    <select id="getTransferRuleBySourcePacketId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTransferRuleDO">
        select id,create_time,update_time,source_packet_id,target_team_id,is_delete,workbench_type
        from <include refid="tableName"/>
        where source_packet_id = #{sourcePacketId}
    </select>

    <update id="updateTransferRuleById" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTransferRuleDO">
        update <include refid="tableName"/>
        <set>
            <if test="sourcePacketId != null">
                source_packet_id = #{sourcePacketId}
            </if>
            <if test="targetTeamId != null">
                target_team_id = #{targetTeamId}
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete}
            </if>
            <if test="workbenchType != null">
                workbench_type = #{workbenchType}
            </if>
        </set>
        where id = #{id}
    </update>
    <select id="getSourcePacketIdListBySourcePacketIds" parameterType="java.util.List" resultType="java.lang.Integer">
        select source_packet_id from <include refid="tableName"/>
        <if test="sourcePacketIds !=null and sourcePacketIds.size>0">
            where source_packet_id in
            <foreach collection="sourcePacketIds" item="sourcePacketId" open="(" separator="," close=")">
                #{sourcePacketId}
            </foreach>
        </if>
    </select>
</mapper>
