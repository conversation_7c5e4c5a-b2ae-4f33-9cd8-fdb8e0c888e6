<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseInfoDao">

    <sql id="baseResult">
        `id`,
        `ext_clew_id`,
        `create_time`,
        `update_time`,
        `channel`,
        `register_time`,
        `register_name`,
        `status`,
        `province`,
        `city`,
        `address`,
        `disease_name`,
        `hosptial_name`,
        `expect_contact_time`,
        `wh_remark_content`,
        `phone_status`,
        `province_id`,
        `city_id`,
        `is_delete`,
        `display_id`,
        `fundraising_object`,
        `is_follow`,
        `primary_channel`,
        `not_initiate_reason`,
        `ab_group_id`,
        `register_mode`,
        `info_uuid`,
        `system_handle_time`,
        `cf_title`,
        `cf_content`,
        `cf_target_amount`,
        `clew_type`,
        `info_id`,
        `cf_base_status`,
        `phone_is_repetitive`,
        `user_id`,
        `unique_code`,
        `forecast_call_unique_id`,
        `first_approve_status`,
        `draft_id`,
        `origin_clew_id`,
        `encrypt_phone`,
        `encrypt_second_phone`,
        `is_submit_report`,
        `approve_status`,
        `version`,
        `draft_page_desc`,
        `clew_status`,
        `clew_assign_status`,
        `source_type`,
        `origin_assign`,
        `cur_assign`,
        `pre_clew_id`,
        `person_id`,
        `uuid`,
        `sickroom`,
        `sickbed`,
        `cf_version`
    </sql>
    <sql id="bdCrmClewInfoBaseModel">
        id as clewId,
        status as status,
        unique_code as uniqueCode,
        encrypt_phone as encryptPhone,
        cf_base_status as cfBaseStatus,
        disease_name as diseaseName,
        fundraising_object as fundraisingObject,
        hosptial_name as hosptialName,
        address as address,
        register_mode as registerMode,
        create_time as createTime,
        display_id as displayId,
        is_follow as isFollow,
        primary_channel as primaryChannel,
        register_name as registerName,
        update_time as updateTime,
        system_handle_time as systemHandleTime,
        cf_title as cfTitle,
        info_uuid as infoUuid,
        info_id as infoId,
        wh_remark_content as whRemarkContent,
        `province_id` as provinceId,
        `city_id` as cityId,
        `province`,
        `city`,
        clew_status as clewStatus,
        clew_type as clewType,
        `phone_status` as phoneStatus,
        clew_status as requestStatus
    </sql>
    <sql id="bdCrmClewInfoModel">
        id as clewId,
        status as status,
        unique_code as uniqueCode,
        encrypt_phone as encryptPhone,
        cf_base_status as cfBaseStatus,
        disease_name as diseaseName,
        fundraising_object as fundraisingObject,
        hosptial_name as hosptialName,
        address as address,
        register_mode as registerMode,
        create_time as createTime,
        display_id as displayId,
        is_follow as isFollow,
        primary_channel as primaryChannel,
        register_name as registerName,
        update_time as updateTime,
        system_handle_time as systemHandleTime,
        cf_title as cfTitle,
        info_uuid as infoUuid,
        info_id as infoId,
        province as province,
        city as city,
        province_id as provinceId,
        city_id as cityId,
        clew_status as clewStatus,
        phone_status as phoneStatus,
        sickbed as sickbed,
        sickroom as sickroom,
        clew_type as clewType,
        wh_remark_content as whRemarkContent,
        clew_status as requestStatus
    </sql>
    <sql id="tableName">cf_clew_base_info</sql>
    <sql id="whereCondition">
        where ccpm.packet_id in
        <foreach collection="packetIdList" item="packetId" open="(" close=")" separator=",">
            #{packetId}
        </foreach>
        <if test="userName!=null and userName!=''">
            and ccpm.user_name = #{userName}
        </if>
        <if test="primaryChannel!=null and primaryChannel!=''">
            and ccbi.primary_channel = #{primaryChannel}
        </if>
        <if test="webcallStartTime!=null">
            and cccr.answer_time between #{webcallStartTime} and #{webcallEndTime} and cccr.phone_status=200
        </if>
        <if test="startTime != null">
            and ccai.create_time between #{startTime} and #{endTime}
        </if>
        <if test="cfStartTime != null">
            and ccbi.status=2
            and ccbi.system_handle_time between #{cfStartTime} and #{cfEndTime}
        </if>
    </sql>
    <sql id="whereRegisterCondition">
        where 1=1
        <if test="uniqueCode != null">
            and unique_code = #{uniqueCode}
        </if>
        <if test="clewStatus != null">
            and status = #{clewStatus}
        </if>
        <if test="encryptPhone != null and encryptPhone !=''">
            and encrypt_phone = #{encryptPhone} or encrypt_second_phone=#{encryptPhone}
        </if>
        <if test="startTime != null and endTime != null">
            and create_time between #{startTime} and #{endTime}
        </if>
    </sql>

    <sql id = "whereCommonQueryByPhone">
        where 1=1
        <if test="encryptPhone != null and phoneMode == 0">
            and encrypt_phone=#{encryptPhone}
        </if>
        <if test="encryptPhone != null and phoneMode == 1">
            and encrypt_second_phone=#{encryptPhone}
        </if>
        <if test="clewTypeList!=null and clewTypeList.size()>0">
            and clew_type in
            <foreach collection="clewTypeList" item="clewType" open="(" separator="," close=")">
                #{clewType}
            </foreach>
        </if>
        <if test="isDelete != null">
            and is_delete = #{isDelete}
        </if>
        <if test="uniqueCode != null">
            and unique_code = #{uniqueCode}
        </if>
        <if test="primaryChannel != null">
            and primary_channel = #{primaryChannel}
        </if>
        <if test="channel != null">
            and channel=#{channel}
        </if>
        <if test="clewStatus != null">
            and status = #{clewStatus}
        </if>
        <if test="startTime != null and endTime != null">
            and create_time between #{startTime} and #{endTime}
        </if>
        <if test="orderBy != null">
            order by id
        </if>
        <if test="sortAsc !=null and sortAsc == true">
            asc
        </if>
        <if test="sortAsc !=null and sortAsc == false">
            desc
        </if>
        <if test="limitNum != null ">
            limit #{limitNum}
        </if>
        <if test="pageNo != null and pageSize != null ">
            limit #{pageNo},#{pageSize}
        </if>
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into <include refid="tableName"/>
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="extClewId!=null">
            ext_clew_id,
        </if>
        <if test="encryptPhone!=null and encryptPhone!=''">
            encrypt_phone,
        </if>
        <if test="encryptSecondPhone!=null and encryptSecondPhone!=''">
            encrypt_second_phone,
        </if>
        <if test="channel!=null and channel!=''">
          channel,
        </if>
        <if test="registerTime!=null" >
          register_time,
        </if>
        <if test="registerName != null and registerName != ''">
          register_name,
        </if>
        <if test="diseaseName != null and diseaseName != ''">
          disease_name,
        </if>
        <if test="province!=null and province!=''">
          province,
        </if>
        <if test="provinceId!=null">
          province_id,
        </if>
        <if test="city!=null and city!=''">
          city,
        </if>
        <if test="cityId!=null">
          city_id,
        </if>
        <if test="displayId!=null and displayId!=''">
          display_id,
        </if>
        <if test="fundraisingObject!=null and fundraisingObject!=''">
          fundraising_object,
        </if>
        <if test="isFollow!=null and isFollow!=''">
          is_follow,
        </if>
        <if test="primaryChannel!=null and primaryChannel!=''">
          primary_channel,
        </if>
        <if test="whRemarkContent != null and whRemarkContent!=''">
          wh_remark_content,
        </if>
        <if test="notInitiateReason != null">
            not_initiate_reason,
        </if>
        <if test="systemHandleTime!=null">
            system_handle_time,
        </if>
        <if test="infoUuid!=null and infoUuid!=''">
            info_uuid,
        </if>
        <if test="infoId!=null and infoId!=''">
            info_id,
        </if>
        <if test="abGroupId!=null and abGroupId!=''">
            ab_group_id,
        </if>
        <if test="isDelete!=null">
            is_delete,
        </if>
        <if test="registerMode!=null">
            register_mode,
        </if>
        <if test="status != null">
            status,
        </if>
        <if test="phoneStatus != null">
            phone_status,
        </if>
        <if test="cfTitle!=null and cfTitle!=''">
            cf_title,
        </if>
        <if test="cfContent!=null and cfContent!=''">
            cf_content,
        </if>
        <if test="cfTargetAmount!=null">
            cf_target_amount,
        </if>
        <if test="clewType!=null">
            clew_type,
        </if>
        <if test="phoneIsRepetitive!=null">
            phone_is_repetitive,
        </if>
        <if test="userId!=null">
            user_id,
        </if>
        <if test="uniqueCode!=null and uniqueCode!=''">
            unique_code,
        </if>
        <if test="hosptialName != null">
            hosptial_name,
        </if>
        <if test="address != null">
            address,
        </if>
        <if test="firstApproveStatus!=null">
            first_approve_status,
        </if>
        <if test="cfBaseStatus!=null">
            cf_base_status,
        </if>
        <if test="deviceId!=null">
            device_id,
        </if>
        <if test="idfa!=null">
            idfa,
        </if>
        <if test="forecastCallUniqueId!=null">
            forecast_call_unique_id,
        </if>

        <if test="createTime != null">
            create_time,
        </if>
        <if test="updateTime != null">
            update_time,
        </if>
        <if test="draftId != null ">
            draft_id,
        </if>
        <if test="originClewId != null">
            origin_clew_id,
        </if>
        <if test="draftPageDesc != null and draftPageDesc != ''">
            draft_page_desc,
        </if>
        <if test="clewStatus!=null">
            clew_status,
        </if>
        <if test="clewAssignStatus!=null">
            clew_assign_status,
        </if>
        <if test="sourceType != null">
            source_type,
        </if>
        <if test="originAssign !=null">
            origin_assign,
        </if>
        <if test="curAssign !=null">
            cur_assign,
        </if>
        <if test="preClewId != null">
            pre_clew_id,
        </if>
        <if test="personId != null">
            person_id,
        </if>
        <if test="uuid != null">
            uuid,
        </if>
        <if test="sickroom !=null">
            sickroom,
        </if>
        <if test="sickbed !=null">
            sickbed,
        </if>
        <if test="cfVersion != null">
            cf_version,
        </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="extClewId!=null">
            #{extClewId},
        </if>
        <if test="encryptPhone!=null and encryptPhone!=''">
            #{encryptPhone},
        </if>
        <if test="encryptSecondPhone!=null and encryptSecondPhone!=''">
            #{encryptSecondPhone},
        </if>
        <if test="channel!=null and channel!=''">
            #{channel},
        </if>
        <if test="registerTime!=null" >
            #{registerTime},
        </if>
        <if test="registerName != null and registerName != ''">
            #{registerName},
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            #{diseaseName},
        </if>
        <if test="province!=null and province!=''">
            #{province},
        </if>
        <if test="provinceId!=null">
            #{provinceId},
        </if>
        <if test="city!=null and city!=''">
            #{city},
        </if>
        <if test="cityId!=null">
            #{cityId},
        </if>
        <if test="displayId!=null and displayId!=''">
            #{displayId},
        </if>
        <if test="fundraisingObject!=null and fundraisingObject!=''">
            #{fundraisingObject},
        </if>
        <if test="isFollow!=null  and isFollow!=''">
            #{isFollow},
        </if>
        <if test="primaryChannel!=null and primaryChannel!=''">
            #{primaryChannel},
        </if>
        <if test="whRemarkContent != null and whRemarkContent!=''">
            #{whRemarkContent},
        </if>
        <if test="notInitiateReason != null ">
            #{notInitiateReason},
        </if>
        <if test="systemHandleTime!=null">
            #{systemHandleTime},
        </if>
        <if test="infoUuid!=null and infoUuid!=''">
            #{infoUuid},
        </if>
        <if test="infoId!=null and infoId!=''">
            #{infoId},
        </if>
        <if test="abGroupId!=null and abGroupId!=''">
            #{abGroupId},
        </if>
        <if test="isDelete!=null">
            #{isDelete},
        </if>
        <if test="registerMode!=null">
            #{registerMode},
        </if>
        <if test="status != null">
            #{status},
        </if>
        <if test="phoneStatus != null">
            #{phoneStatus},
        </if>
        <if test="cfTitle!=null and cfTitle!=''">
            #{cfTitle},
        </if>
        <if test="cfContent!=null and cfContent!=''">
            #{cfContent},
        </if>
        <if test="cfTargetAmount!=null">
            #{cfTargetAmount},
        </if>
        <if test="clewType!=null">
            #{clewType},
        </if>
        <if test="phoneIsRepetitive!=null">
            #{phoneIsRepetitive},
        </if>
        <if test="userId!=null">
            #{userId},
        </if>
        <if test="uniqueCode!=null and uniqueCode!=''">
            #{uniqueCode},
        </if>
        <if test="hosptialName != null">
            #{hosptialName},
        </if>
        <if test="address != null">
            #{address},
        </if>
        <if test="firstApproveStatus!=null">
            #{firstApproveStatus},
        </if>
        <if test="cfBaseStatus!=null">
            #{cfBaseStatus},
        </if>
        <if test="deviceId!=null">
            #{deviceId} ,
        </if>
        <if test="idfa!=null">
            #{idfa} ,
        </if>
        <if test="forecastCallUniqueId!=null">
            #{forecastCallUniqueId} ,
        </if>
        <if test="createTime != null">
            #{createTime},
        </if>
        <if test="updateTime != null">
            #{updateTime},
        </if>
        <if test="draftId != null ">
            #{draftId} ,
        </if>
        <if test="originClewId != null">
            #{originClewId} ,
        </if>
        <if test="draftPageDesc != null and draftPageDesc != '' ">
            #{draftPageDesc} ,
        </if>
        <if test="clewStatus!=null">
            #{clewStatus},
        </if>
        <if test="clewAssignStatus!=null">
            #{clewAssignStatus},
        </if>
        <if test="sourceType != null">
            #{sourceType} ,
        </if>
        <if test="originAssign !=null">
            #{originAssign} ,
        </if>
        <if test="curAssign !=null">
            #{curAssign} ,
        </if>
        <if test="preClewId != null">
            #{preClewId} ,
        </if>
        <if test="personId!=null">
            #{personId},
        </if>
        <if test="uuid!=null">
            #{uuid},
        </if>
        <if test="sickroom !=null">
            #{sickroom},
        </if>
        <if test="sickbed !=null">
            #{sickbed},
        </if>
        <if test="cfVersion != null">
            #{cfVersion},
        </if>
    </trim>
    </insert>

    <update id="updateClewBaseInfo" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
    update <include refid="tableName"/>
    <set>
        <if test="registerName != null and registerName!=''">
          register_name = #{registerName},
        </if>
        <if test="extClewId != null">
          ext_clew_id = #{extClewId},
        </if>
        <if test="status != null">
          status = #{status},
        </if>
        <if test="province != null">
          province = #{province},
        </if>
        <if test="city != null">
          city = #{city},
        </if>
        <if test="provinceId != null">
          province_id = #{provinceId},
        </if>
        <if test="cityId != null">
          city_id = #{cityId},
        </if>
        <if test="address != null">
          address = #{address},
        </if>
        <if test="diseaseName != null">
          disease_name = #{diseaseName},
        </if>
        <if test="hosptialName != null">
          hosptial_name = #{hosptialName},
        </if>
        <if test="expectContactTime != null">
          expect_contact_time = #{expectContactTime},
        </if>
        <if test="whRemarkContent != null and whRemarkContent!=''">
          wh_remark_content = #{whRemarkContent},
        </if>
        <if test="phoneStatus != null">
          phone_status=#{phoneStatus},
        </if>
        <if test="fundraisingObject != null">
          fundraising_object=#{fundraisingObject},
        </if>
        <if test="notInitiateReason != null">
          not_initiate_reason=#{notInitiateReason},
        </if>
        <if test="systemHandleTime!=null">
            system_handle_time = #{systemHandleTime},
        </if>
        <if test="infoUuid!=null and infoUuid!=''">
            info_uuid = #{infoUuid},
        </if>
        <if test="cfTitle!=null and cfTitle!=''">
            cf_title = #{cfTitle},
        </if>
        <if test="cfContent!=null and cfContent!=''">
            cf_content = #{cfContent},
        </if>
        <if test="cfTargetAmount!=null">
            cf_target_amount = #{cfTargetAmount},
        </if>
        <if test="clewType!=null">
            clew_type = #{clewType},
        </if>
        <if test="infoId!=null">
            info_id = #{infoId},
        </if>
        <if test="phoneIsRepetitive!=null">
            phone_is_repetitive = #{phoneIsRepetitive},
        </if>
        <if test="userId!=null">
            user_id = #{userId},
        </if>
        <if test="uniqueCode!=null and uniqueCode!=''">
            unique_code = #{uniqueCode},
        </if>
        <if test="cfBaseStatus!=null">
            cf_base_status = #{cfBaseStatus},
        </if>
        <if test="firstApproveStatus!=null">
            first_approve_status = #{firstApproveStatus},
        </if>
        <if test="encryptSecondPhone!=null and encryptSecondPhone!=''">
            encrypt_second_phone=#{encryptSecondPhone},
        </if>
        <if test="draftId != null ">
            draft_id=#{draftId} ,
        </if>
        <if test="registerMode!=null">
            register_mode = #{registerMode},
        </if>
        <if test="clewStatus!=null">
            clew_status = #{clewStatus},
        </if>
        <if test="clewAssignStatus!=null">
            clew_assign_status = #{clewAssignStatus},
        </if>
        <if test="sourceType != null">
            source_type = #{sourceType} ,
        </if>
        <if test="originAssign !=null">
            origin_assign = #{originAssign},
        </if>
        <if test="curAssign !=null">
            cur_assign = #{curAssign},
        </if>
        <if test="sickroom !=null">
            sickroom = #{sickroom},
        </if>
        <if test="sickbed !=null">
            sickbed = #{sickbed},
        </if>
    </set>
    where id=#{id}
    </update>
    <update id="updateLeaderClewBaseInfoById" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        update <include refid="tableName"/>
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId},
            </if>
            <if test="cityId != null">
                city_id = #{cityId},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="diseaseName != null">
                disease_name = #{diseaseName},
            </if>
            <if test="hosptialName != null">
                hosptial_name = #{hosptialName},
            </if>
            <if test="expectContactTime != null">
                expect_contact_time = #{expectContactTime},
            </if>
            <if test="phoneStatus != null">
                phone_status=#{phoneStatus},
            </if>
            <if test="fundraisingObject != null">
                fundraising_object=#{fundraisingObject},
            </if>
            <if test="notInitiateReason != null">
                not_initiate_reason=#{notInitiateReason},
            </if>
            <if test="cfTitle!=null and cfTitle!=''">
                cf_title = #{cfTitle},
            </if>
            <if test="cfContent!=null and cfContent!=''">
                cf_content = #{cfContent},
            </if>
            <if test="cfTargetAmount!=null">
                cf_target_amount = #{cfTargetAmount},
            </if>
            <if test="sickroom!=null">
                sickroom = #{sickroom},
            </if>
            <if test="sickbed!=null">
                sickbed = #{sickbed},
            </if>
        </set>
        where id=#{id}
    </update>

    <update id="updateClewBaseInfoByUniqueCodeWithIdList" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        update <include refid="tableName"/>
        <set>
            <if test="model.status != null">
                status = #{model.status},
            </if>
            <if test="model.province != null">
                province = #{model.province},
            </if>
            <if test="model.city != null">
                city = #{model.city},
            </if>
            <if test="model.provinceId != null">
                province_id = #{model.provinceId},
            </if>
            <if test="model.cityId != null">
                city_id = #{model.cityId},
            </if>
            <if test="model.address != null">
                address = #{model.address},
            </if>
            <if test="model.diseaseName != null">
                disease_name = #{model.diseaseName},
            </if>
            <if test="model.hosptialName != null">
                hosptial_name = #{model.hosptialName},
            </if>
            <if test="model.whRemarkContent != null and model.whRemarkContent!=''">
                wh_remark_content = #{model.whRemarkContent},
            </if>
            <if test="model.phoneStatus != null">
                phone_status=#{model.phoneStatus},
            </if>
            <if test="model.fundraisingObject != null">
                fundraising_object=#{model.fundraisingObject},
            </if>
            <if test="model.notInitiateReason != null">
                not_initiate_reason=#{model.notInitiateReason},
            </if>
            <if test="model.cfTitle!=null and model.cfTitle!=''">
                cf_title = #{model.cfTitle},
            </if>
            <if test="model.cfContent!=null and model.cfContent!=''">
                cf_content = #{model.cfContent},
            </if>
            <if test="model.cfTargetAmount!=null">
                cf_target_amount = #{model.cfTargetAmount},
            </if>
            <if test="model.clewType!=null">
                clew_type = #{model.clewType},
            </if>
            <if test="model.sickroom!=null">
                sickroom = #{model.sickroom},
            </if>
            <if test="model.sickbed!=null">
                sickbed = #{model.sickbed},
            </if>
        </set>
        where id in <foreach collection="clewIdList" item="clewId" open="(" close=")" separator=",">#{clewId}</foreach>
        and unique_code = #{model.uniqueCode}
    </update>
    <update id="updateClewCfBaseStatus">
        update <include refid="tableName"/>
        set cf_base_status = #{cfBaseStatus}
        where info_id = #{infoId} and id in
        <foreach collection="clewIdList" item="clewId" open="(" separator="," close=")">
          #{clewId}
        </foreach>
    </update>

    <select id="getClewInfoByIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
    select <include refid="baseResult"/>
    from <include refid="tableName"/>
    where id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and is_delete=0
    order by create_time desc
    </select>

    <select id="getClewInfoByIdsFromMaster" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
    select <include refid="baseResult"/>
    from <include refid="tableName"/>
    where id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and is_delete=0
    order by create_time desc
    </select>

    <select id="getClewInfoByIdFromMaster" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
    select <include refid="baseResult"/>
    from <include refid="tableName"/>
    where id = #{clewId}
    </select>

    <select id="getClewInfoByIdFromSlave" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id = #{clewId}
    </select>

    <select id="getClewBaseInfoByDisplayId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where display_id = #{displayId}
        and is_delete=0
        limit 1;
    </select>

    <select id="getClewBaseInfoByExtClewId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where ext_clew_id = #{extClewId};
    </select>

    <select id="getClewBaseInfoByInfoUuid" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0 and info_uuid = #{infoUuid}
        <if test="clewTypeList!=null and clewTypeList.size()>0">
            and clew_type in
            <foreach collection="clewTypeList" item="clewType" open="(" separator="," close=")">
                #{clewType}
            </foreach>
        </if>
    </select>
    <select id="getClewBaseInfoByInfoId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0 and info_id = #{infoId}
        <if test="clewTypeList!=null and clewTypeList.size()>0">
            and clew_type in
            <foreach collection="clewTypeList" item="clewType" open="(" separator="," close=")">
                #{clewType}
            </foreach>
        </if>
    </select>

    <select id="getRepetitionPhoneWithNoHandleClew" resultType="java.lang.Long">
        select distinct cb.id
        from cf_clew_base_info cb
        join cf_clew_task cct on cb.id = cct.clew_id
        where cct.task_type=1
        and cb.create_time between #{queryParam.startTime} and #{queryParam.endTime}
        and (cb.encrypt_phone = #{queryParam.encryptPhone} or cb.encrypt_second_phone=#{queryParam.encryptPhone})
        and cb.clew_type = 0
    </select>

    <update id="updatePhoneIsRepetitiveById">
        update <include refid="tableName"/>
        set phone_is_repetitive = 1
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="getClewBaseInfoByUniqueId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id in (select clew_id from cf_clew_call_records where unique_id=#{uniqueId})
        limit 1;
    </select>

    <update id="updateCaseInfoByClewIds" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        update <include refid="tableName"/>
        set info_uuid =#{crowdfundingInfo.infoId} ,system_handle_time =#{crowdfundingInfo.createTime} ,
          cf_title=#{crowdfundingInfo.title} , cf_content=#{crowdfundingInfo.content} ,
          cf_target_amount=#{crowdfundingInfo.targetAmount} ,info_id=#{crowdfundingInfo.id} ,
          user_id=#{crowdfundingInfo.userId},cf_base_status=#{cfCaseStatus}
        where id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </update>

    <update id="updateWhRemarkContentById">
        update <include refid="tableName"/>
        set wh_remark_content = #{whRemarkContent}
        <if test="clewAssignStatus!=null">
            ,clew_assign_status=#{clewAssignStatus}
        </if>
        where id = #{id}
    </update>

    <select id="getLatestClewbaseByUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        SELECT <include refid="baseResult"/> from cf_clew_base_info
        where user_id=#{userId} and clew_type = 0
        ORDER BY create_time desc
        limit 1;
    </select>

    <update id="delClewBaseByIdAndUniqueCode">
        update <include refid="tableName"/>
        set is_delete=1
        where id = #{clewId} and unique_code=#{uniqueCode}
    </update>


    <update id="updateFirstApproveStatusByClewIds">
        update <include refid="tableName"/>
        set first_approve_status=#{firstApproveStatus}
        where id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </update>

    <select id="getClewBaseByUserId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where user_id=#{userId}
            and is_delete=0
            and create_time>#{queryTime}
    </select>


    <select id="getClewBaseInfoByUniqueIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where create_time between #{startTime} and #{endTime}
        and forecast_call_unique_id in
        <foreach collection="uniqueIds" item="uniqueId" open="(" close=")" separator=",">
            #{uniqueId}
        </foreach>
    </select>

    <update id="batchUpdateClewBaseInfo" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="phone_status =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.phoneStatus !=null">
                        when id=#{item.id} then #{item.phoneStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix="wh_remark_content =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.whRemarkContent !=null">
                        when id=#{item.id} then #{item.whRemarkContent}
                    </if>
                </foreach>
            </trim>
            <trim prefix="primary_channel =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.primaryChannel !=null">
                        when id=#{item.id} then #{item.primaryChannel}
                    </if>
                </foreach>
            </trim>
            <trim prefix="channel =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.channel !=null">
                        when id=#{item.id} then #{item.channel}
                    </if>
                </foreach>
            </trim>
            <trim prefix="register_time =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.registerTime !=null">
                        when id=#{item.id} then #{item.registerTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_follow =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.isFollow !=null">
                        when id=#{item.id} then #{item.isFollow}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fundraising_object =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.fundraisingObject !=null">
                        when id=#{item.id} then #{item.fundraisingObject}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="items" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
        order by id
    </update>

    <update id="batchUpdateUniqueCode" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="unique_code =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.uniqueCode !=null">
                        when id=#{item.clewId} then #{item.uniqueCode}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="items" item="item" open="(" separator="," close=")">
            #{item.clewId}
        </foreach>
    </update>

    <select id="getClewBaseInfoByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </select>

    <select id="getClewInfoByDisplayId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
          and display_id=#{displayId}
    </select>

    <select id="getCfClewForCharityDayModelsByDisplayIds"
            resultType="com.shuidihuzhu.client.cf.clewtrack.model.CfClewForCharityDayModel">
        select
        `display_id`,
        `province`,
        `disease_name`,
        `fundraising_object`,
        `info_uuid`,
        `info_id`,
        `encrypt_phone` as phone
        from <include refid="tableName"/>
        where display_id in <foreach collection="displayIds" item="displayId" open="(" close=")" separator=",">#{displayId} </foreach>
    </select>

    <select id="getCfClewBaseInfoDOByPhones"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where encrypt_phone in <foreach collection="encryptPhones" item="phone" open="(" close=")" separator=",">#{phone} </foreach>
    </select>

    <update id="updateDraftIdByClewIds">
        update <include refid="tableName"/>
        set draft_id=#{draftId}
        where id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </update>
    <update id="updatePreposeReportData">
        update <include refid="tableName"/>
        set is_submit_report = #{isSubmitReport},approve_status=#{approveStatus}
        <if test="version != null">
            ,version=#{version}
        </if>
        where id = #{clewId}
    </update>

    <select id="getClewBaseInfosByTimeAndPrimaryChannel"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0
        <if test="startDate != null">
            and create_time >=#{startDate}
        </if>
        <if test="primaryChannel != null and primaryChannel != ''">
            and primary_channel=#{primaryChannel}
        </if>
    </select>

    <update id="updateDraftPageDescByClewIds">
        update <include refid="tableName"/>
        <set>
            <if test="content != null and content != ''">
                cf_content = #{content},
            </if>
            <if test="title != null and title != ''">
                cf_title = #{title},
            </if>
            <if test="targetAmount > 0">
                cf_target_amount = #{targetAmount},
            </if>
            <if test="cfVersion > 0">
                cf_version = #{cfVersion},
            </if>
            <if test="encryptSecondPhone != null and encryptSecondPhone != ''">
                encrypt_second_phone = #{encryptSecondPhone},
            </if>
                draft_page_desc = #{draftPageDesc}
        </set>
        where id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </update>

    <select id="getClewBaseInfoByUserIdAndChannelAndDateInterval"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        <if test="userId != null  ">
            and user_id =#{userId}
        </if>
        <if test="channel != null and channel != ''">
            and channel=#{channel}
        </if>
        <if test="startDate != null and endDate != null">
            and create_time between #{startDate} and #{endDate}
        </if>
        ORDER BY id desc;
    </select>

    <select id="getClewBaseInfoCommonQueryByPhone" parameterType="com.shuidihuzhu.cf.clewtrack.model.CommonQueryByPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        <include refid="whereCommonQueryByPhone"/>
    </select>
    <update id="updateClewStatus">
        update <include refid="tableName"/>
        <set>
            <if test="clewStatus!=null">
                clew_status=#{clewStatus},
            </if>
            <if test="phoneStatus!=null">
                phone_status=#{phoneStatus},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateClewStatusByUniqueCodeWithIdList">
        update <include refid="tableName"/>
        <set>
            <if test="clewStatus!=null">
                clew_status=#{clewStatus},
            </if>
            <if test="phoneStatus!=null">
                phone_status=#{phoneStatus},
            </if>
        </set>
        where id in <foreach collection="clewIdList" item="clewId" open="(" close=")" separator=",">#{clewId}</foreach>
    </update>


    <update id="updateClewStatusByClewAttachInfos" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="clew_status =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.requestStatus !=null">
                        when id=#{item.clewId} then #{item.requestStatus}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="items" index="index" item="item" separator="," open="(" close=")">
            #{item.clewId}
        </foreach>
    </update>

    <update id="updatePersonIdAndUuidByClewId">
        update <include refid="tableName"/>
        <set>
            <if test="personId!=null and personId!=''">
                person_id=#{personId},
            </if>
            <if test="uuid!=null and uuid!=''">
                uuid = #{uuid},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="shutdownGrabOrderFailClewByClewIds">
        update <include refid="tableName"/>
        set is_delete=#{isDelete}
        where id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </update>

    <select id="getCfClewBaseInfoDOBySecondPhones"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where encrypt_second_phone in
        <foreach collection="encryptPhones" item="phone" open="(" close=")" separator=",">
            #{phone}
        </foreach>
    </select>

    <select id="getHasInfoUuidClewBySecondPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where encrypt_second_phone = #{encryptPhone} and info_uuid!=''
        order by id desc limit 1
    </select>

    <select id="getHasInfoUuidClewByPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where encrypt_phone = #{encryptPhone} and info_uuid!=''
        order by id desc limit 1
    </select>



    <select id="getWaitAssignClewBaseInfo" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>  from <include refid="tableName"/>
        where is_delete = 0 and id in <foreach collection="clewIdList" item="clewId" open="(" close=")" separator=",">#{clewId}</foreach>
        <if test="provinceList != null and provinceList.size()>0">
            and province in <foreach collection="provinceList" item="province" open="(" close=")" separator=",">#{province}</foreach>
        </if>
        <if test="cityList != null and cityList.size()>0">
            and city in <foreach collection="cityList" item="city" open="(" close=")" separator=",">#{city}</foreach>
        </if>
    </select>

    <select id="getCfClewBaseInfoDOByTimeWithFirstPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete = 0
        and create_time>#{startTime}
        and encrypt_phone in <foreach collection="encryptPhones" item="encryptPhone" open="(" close=")" separator=",">#{encryptPhone}</foreach>
    </select>

    <select id="getCfClewBaseInfoDOByTimeWithSecondPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete = 0
        and create_time>#{startTime}
        and encrypt_second_phone in <foreach collection="encryptPhones" item="encryptPhone" open="(" close=")" separator=",">#{encryptPhone}</foreach>
    </select>


    <select id="getOfflineClewByClewTypeWithEncrptPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where clew_type=4 and id <![CDATA[ < ]]> #{clewId} and encrypt_phone=#{encryptPhone}
        order by id desc
        limit 1
    </select>


    <select id="getClewBaseInfoListByClewIs"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from cf_clew_base_info where is_delete=0 and id in <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">#{clewId}</foreach>
        order by create_time desc
    </select>

    <update id="updateDiseaseName">
        update <include refid="tableName"/> set disease_name=#{diseaseName} where id = #{clewId}
    </update>

    <select id="listClewByDiseaseName" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete= 0 and clew_type = 0 and disease_name like CONCAT('%',#{diseaseName},'%')
        <if test="startTime != null and endTime != null ">
            and create_time between #{startTime} and #{endTime}
        </if>
    </select>

    <select id="listClewIdsByCaseIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select id, info_id
        from <include refid="tableName"/>
        where is_delete= 0
        and info_id in
        <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        and clew_type in
        <foreach collection="clewTypeList" item="clewType" open="(" separator="," close=")">
            #{clewType}
        </foreach>
    </select>

    <select id="getClewBaseInfoByClewIdsFromMaster"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
    </select>

    <select id="listByCaseId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where info_id = #{caseId}
    </select>

    <select id="getClewInfoByInfoIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where info_id in
        <foreach collection="infoIdList" item="infoId" open="(" close=")" separator=",">
            #{infoId}
        </foreach>
        and is_delete = 0
    </select>
</mapper>
