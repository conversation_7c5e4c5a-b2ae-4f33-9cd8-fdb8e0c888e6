<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfBdCaseAttributionDao">
    <sql id="tableName">
        cf_bd_case_attribution
    </sql>
    <sql id="fileds">
        `id`,
        `encrypt_phone`,
        `case_encrypt_phone`,
        `case_id`,
        `info_uuid`,
        `prepose_material_id`,
        `clew_id`,
        `case_create_time`,
        `unique_code`,
        `attribution_type`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <select id="getCfBdCaseAttributionDOByCaseId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.bdcrm.CfBdCaseAttributionDO">
        select <include refid="fileds"/> from <include refid="tableName"/>
        where case_id = #{caseId} and is_delete = 0
        order by id desc
        limit 1
    </select>

</mapper>
