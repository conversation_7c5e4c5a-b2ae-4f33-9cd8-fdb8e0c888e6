<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfHospitalDataInfoDao">
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfHospitalDataInfoDO" useGeneratedKeys="true" keyProperty="id">
        insert into cf_hospital_data_info
        (create_time,hospital_name,province_name,city_name,address,mdd_code,bd_user_id,bd_user_name)
        values(#{createTime},#{hospitalName},#{provinceName},#{cityName},#{address},#{mddCode},#{bdUserId},#{bdUserName})
    </insert>

    <select id="getDataInfoByHospitalName"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfHospitalDataInfoDO">
        select id ,create_time,update_time,hospital_name,province_name,city_name,address,mdd_code,bd_user_id,bd_user_name
        from cf_hospital_data_info
        where hospital_name=#{hospitalName} and bd_user_id=#{bdUserId}
    </select>
</mapper>