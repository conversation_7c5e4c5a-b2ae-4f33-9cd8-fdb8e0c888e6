<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfServiceOptLogDao">

    <sql id="fields">
        `id`,
        `user_id`,
        `user_name`,
        `work_bench_type`,
        `online_status`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <sql id="tableName">cf_service_opt_log</sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfServiceOptLogDo">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId!=null">
                user_id,
            </if>
            <if test="userName!=null">
                user_name,
            </if>
            <if test="workBenchType!=null and workBenchType!=''">
                work_bench_type,
            </if>
            <if test="onlineStatus!=null and onlineStatus!=''">
                online_status,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId!=null">
                #{userId},
            </if>
            <if test="userName!=null">
                #{userName},
            </if>
            <if test="workBenchType!=null and workBenchType!=''">
                #{workBenchType},
            </if>
            <if test="onlineStatus!=null and onlineStatus!=''">
                #{onlineStatus},
            </if>
        </trim>
    </insert>


    <select id="getServiceTimeInfoByUserId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfServiceOptLogDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where user_id = #{userId}
        and create_time >=#{date}
        and is_delete=0
    </select>




</mapper>