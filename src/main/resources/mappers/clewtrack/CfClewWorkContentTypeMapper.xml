<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewWorkContentTypeDao">

    <sql id="baseResult">
        `id`,
        `content_type`,
        `content_desc`,
        `enum_name`,
        `work_bench_type`,
        `can_assign`,
        `show_type`,
        `show_comment`,
        `operator`,
        `is_delete`,
        `create_time`,
        `update_time`,
        `status`,
        `has_show_time`,
        `work_role`,
        `work_start_time`,
        `work_end_time`,
        `wx_notice`
    </sql>

    <sql id="tableName">cf_clew_work_content_type</sql>

    <insert id="insertClewWorkContentType" parameterType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`content_type`,
        `content_desc`,
        `enum_name`,
        `work_bench_type`,
        `can_assign`,
        `show_type`,
        `show_comment`,
        `operator`,
        `create_time`,
        `update_time`,
        `status`,
        `work_role`,
        `work_start_time`,
        `work_end_time`,
        `wx_notice`)
        values
        (#{contentType},
        #{contentDesc},
        #{enumName},
        #{workBenchType},
        #{canAssign},
        #{showType},
        #{showComment},
        #{operator},
        sysdate(),
        sysdate(),
        #{status},
        #{workRole},
        #{workStartTime},
        #{workEndTime},
        #{wxNotice})
    </insert>

    <update id="updateClewWorkContentType" parameterType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel">
        update <include refid="tableName"/>
        <set>
            <if test="contentDesc != null">
                content_desc = #{contentDesc},
            </if>
            <if test="workBenchType != null">
                work_bench_type = #{workBenchType},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="showType != null">
                show_type = #{showType},
            </if>
            <if test="workRole != null">
                work_role = #{workRole},
            </if>
            <if test="workStartTime != null">
                work_start_time = #{workStartTime},
            </if>
            <if test="workEndTime != null">
                work_end_time = #{workEndTime},
            </if>
            <if test="wxNotice != null">
                wx_notice = #{wxNotice},
            </if>
            update_time = sysdate(),
        </set>
        where id = #{id}
    </update>

    <select id="getClewWorkContentById" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0 and id = #{id}
    </select>

    <select id="getClewWorkContentList" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel"
            parameterType="com.shuidihuzhu.cf.clewtrack.param.CfWorkcontentTypeQueryParam">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        <if test="workContentDesc != null">
            and content_desc like concat('%',#{workContentDesc},'%')
        </if>
        <if test="workBenchType != null">
            and work_bench_type = #{workBenchType}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        order by status asc,create_time desc
        limit #{offset},#{pageSize}
    </select>

    <select id="getClewWorkContentListCount" resultType="java.lang.Integer"
            parameterType="com.shuidihuzhu.cf.clewtrack.param.CfWorkcontentTypeQueryParam">
        SELECT count(1)
        from <include refid="tableName"/>
        where is_delete=0
        <if test="workContentDesc != null">
            and content_desc like concat('%',#{workContentDesc},'%')
        </if>
        <if test="workBenchType != null">
            and work_bench_type = #{workBenchType}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="getMaxWorkContentType" resultType="java.lang.Integer"
            parameterType="com.shuidihuzhu.cf.clewtrack.param.CfWorkcontentTypeQueryParam">
        SELECT max(content_type)
        from <include refid="tableName"/>
    </select>

    <select id="getClewWorkContentType" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
    </select>

    <select id="getWorkContentTypeByEnum" resultType="java.lang.Integer">
        SELECT content_type
        from <include refid="tableName"/>
        where enum_name = #{workContentEnum}
        limit 1
    </select>
    <select id="getValidWorkContentByEnum"
            resultType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where enum_name = #{workContentEnum}
        limit 1
    </select>
    <select id="getValidWorkContentByWorkBenchType"
            resultType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where work_bench_type=#{type}
        and is_delete = 0
    </select>

    <select id="getWorkContentByTypes" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and content_type in
        <foreach collection="workContentTypeList" separator="," item="contentType" open="(" close=")">
            #{contentType}
        </foreach>
    </select>

</mapper>
