<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.clewconfig.CfConsultantTimeRangeConfigDao">

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.clewtrack.domain.clewconfig.CfConsultantTimeRangeConfigDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="consultant_list" jdbcType="VARCHAR" property="consultantList"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, start_time, end_time, consultant_list
    </sql>

    <insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.clewtrack.domain.clewconfig.CfConsultantTimeRangeConfigDO">
        INSERT INTO cf_consultant_time_range_config (
            start_time, end_time, consultant_list
        ) VALUES (
            #{startTime}, #{endTime}, #{consultantList}
        ) ON DUPLICATE KEY UPDATE
            start_time = VALUES(start_time),
            end_time = VALUES(end_time),
            consultant_list = VALUES(consultant_list)
    </insert>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.clewconfig.CfConsultantTimeRangeConfigDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cf_consultant_time_range_config (
            start_time, end_time, consultant_list
        ) VALUES (
            #{startTime}, #{endTime}, #{consultantList}
        )
    </insert>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.clewtrack.domain.clewconfig.CfConsultantTimeRangeConfigDO">
        UPDATE cf_consultant_time_range_config 
        SET start_time = #{startTime},
            end_time = #{endTime},
            consultant_list = #{consultantList}
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cf_consultant_time_range_config
        WHERE id = #{id}
        and is_delete = 0
    </select>

    <select id="selectEffectiveConfig" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cf_consultant_time_range_config
        WHERE CURDATE() BETWEEN start_time AND end_time
        and is_delete = 0
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="getLatestConfig" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from cf_consultant_time_range_config
        where is_delete = 0
        order by id desc
        limit 1
    </select>

    <update id="deleteById">
        UPDATE cf_consultant_time_range_config 
        SET is_delete = 1
        WHERE id = #{id}
    </update>

    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>, is_delete, create_time, update_time
        FROM cf_consultant_time_range_config
        WHERE is_delete = 0
        <if test="startTime != null and startTime != ''">
            AND start_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND end_time &lt;= #{endTime}
        </if>
        ORDER BY id DESC
    </select>

</mapper> 