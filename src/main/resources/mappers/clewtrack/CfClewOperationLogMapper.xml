<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewOperationLogDao">

    <sql id="baseResult">
        `id`,
        `content_type`,
        `work_bench_type`,
        `content`,
        `operator`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="tableName">cf_clew_operation_log</sql>

    <insert id="insertClewOperationLog" parameterType="com.shuidihuzhu.cf.clewtrack.model.ClewOperationLogModel"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`content_type`,
        `work_bench_type`,
        `content`,
        `operator`,
        `create_time`,
        `update_time`)
        values
        (#{contentType},
        #{workBenchType},
        #{content},
        #{operator},
        sysdate(),
        sysdate())
    </insert>

    <insert id="batchInsertClewOperationLog" parameterType="com.shuidihuzhu.cf.clewtrack.model.ClewOperationLogModel"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
            (`content_type`,
            `work_bench_type`,
            `content`,
            `operator`,
            `create_time`,
            `update_time`)
        values
        <foreach collection="clewOperationLogModels" item="clewOperationLogModel" separator="," >
            (#{clewOperationLogModel.contentType},
            #{clewOperationLogModel.workBenchType},
            #{clewOperationLogModel.content},
            #{clewOperationLogModel.operator},
            sysdate(),
            sysdate())
        </foreach>
    </insert>

    <select id="getClewOperationLog" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewOperationLogModel"
        parameterType="com.shuidihuzhu.cf.clewtrack.param.CfWorkcontentTypeQueryParam">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
            and content_type = #{workContentType}
            and work_bench_type = #{workBenchType}
        order by create_time desc
        limit #{offset},#{pageSize}
    </select>

    <select id="getClewOperationLogCount" resultType="java.lang.Integer">
        SELECT count(1)
        from <include refid="tableName"/>
        where is_delete=0
            and content_type = #{contentType}
            and work_bench_type = #{workBenchType}
    </select>

</mapper>
