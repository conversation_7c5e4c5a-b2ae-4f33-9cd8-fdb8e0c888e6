<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfCaseAssignStrategyConfigDao">

    <sql id="tableName">
        cf_case_assign_strategy_config
    </sql>
    
    <sql id="selectFields">
        id,
        staff_level,
        staff_count,
        first_stage_time,
        second_stage_time,
        third_stage_time,
        create_time,
        update_time,
        is_deleted
    </sql>

    <!--查询所有配置-->
    <select id="listAll" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCaseAssignStrategyConfigDO">
        select
          <include refid="selectFields"/>
        from <include refid="tableName"/>
        where is_deleted = 0
    </select>

    <!--批量新增配置-->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into <include refid="tableName"/>
        (staff_level, staff_count, first_stage_time, second_stage_time, third_stage_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.staffLevel},
            #{item.staffCount},
            #{item.firstStageTime},
            #{item.secondStageTime},
            #{item.thirdStageTime}
            )
        </foreach>
    </insert>

    <!--批量更新配置-->
    <update id="updateBatch" parameterType="java.util.List">
        update <include refid="tableName"/>
        set staff_count = case id
        <foreach collection="list" item="item">
            when #{item.id} then #{item.staffCount}
        </foreach>
        end,
        first_stage_time = case id
        <foreach collection="list" item="item">
            when #{item.id} then #{item.firstStageTime}
        </foreach>
        end,
        second_stage_time = case id
        <foreach collection="list" item="item">
            when #{item.id} then #{item.secondStageTime}
        </foreach>
        end,
        third_stage_time = case id
        <foreach collection="list" item="item">
            when #{item.id} then #{item.thirdStageTime}
        </foreach>
        end
        where id in
        <foreach collection="list" item="item" separator="," close=")" open="(">
            #{item.id}
        </foreach>
    </update>

    <select id="getByStaffLevel" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfCaseAssignStrategyConfigDO">
        select
          <include refid="selectFields"/>
        from <include refid="tableName"/>
        where is_deleted = 0
        and staff_level = #{staffLevel}
    </select>

</mapper> 