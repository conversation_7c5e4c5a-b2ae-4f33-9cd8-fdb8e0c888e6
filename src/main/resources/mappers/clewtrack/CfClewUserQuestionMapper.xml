<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewUserQuestionDao">

    <sql id="TABLE_NAME">cf_clew_user_question</sql>
    <sql id="fields">
        id,attach_info_id,question_type,question
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewUserQuestionDO"
        keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="attachInfoId!=null">
              attach_info_id,
            </if>
            <if test="questionType!=null">
              question_type,
            </if>
            <if test="question!=null and question!=''">
              question,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="attachInfoId!=null">
                #{attachInfoId},
            </if>
            <if test="questionType!=null">
                #{questionType},
            </if>
            <if test="question!=null and question!=''">
                #{question},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" parameterType="java.util.ArrayList">
        insert into <include refid="TABLE_NAME"/>
        (attach_info_id,question_type,question)
        values
        <foreach collection="cfClewUserQuestionDOs" item="cfClewUserQuestionDO" separator=",">
            (#{cfClewUserQuestionDO.attachInfoId},#{cfClewUserQuestionDO.questionType},#{cfClewUserQuestionDO.question})
        </foreach>
    </insert>
    <update id="updateCfClewUserQuestionDOById">
        update <include refid="TABLE_NAME"/>
        <set>
            <if test="questionType!=null">
                question_type = #{questionType},
            </if>
            <if test="question!=null and question!=''">
                question = #{question},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteCfClewUserQuestionDOByAttachInfoId">
        update <include refid="TABLE_NAME"/>
        set is_delete = 1
        where attach_info_id = #{attachInfoId}
    </update>
    <select id="getQuestionsByAttachInfoIdList"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewUserQuestionDO">
        select <include refid="fields"/>
        from <include refid="TABLE_NAME"/>
        where attach_info_id in
        <foreach collection="attachInfoIdList" item="attachInfoId" open="(" separator="," close=")">
            #{attachInfoId}
        </foreach>
        and is_delete = 0
    </select>

</mapper>
