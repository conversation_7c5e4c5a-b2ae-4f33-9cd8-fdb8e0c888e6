<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewManualAssignDao">

  <sql id="file">
    id, work_content_type, assign_work_status, assign_count, real_assign_count, `status`,
    register_start_time, register_end_time, `operator`, operator_mis, create_time, update_time, 
    is_delete,workbench_type
  </sql>
  <sql id="fileName">
    cf_clew_manual_assign
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignDO">
    select 
    <include refid="file" />
    from <include refid="fileName"/>
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByParam" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignDO">

      select <include refid="file"/>
      from <include refid="fileName"/>
      where is_delete = 0
      <if test="param.operator!= null ">
        and operator like concat ('%',#{param.operator},'%')
      </if>
      <if test="param.outerAccountMark != null">
        and operator_mis like concat('%',#{param.outerAccountMark},'%')
      </if>
      <if test="param.startTime != null">
        and create_time >= #{param.startTime}
      </if>
      <if test="param.endTime != null">
        and create_time <![CDATA[ <= ]]> #{param.endTime}
      </if>
      order by create_time desc
      <if test="param.offSet!=null">
        limit #{param.offSet},#{param.pageSize}
      </if>
    </select>
  <select id="selectCountByParam" resultType="java.lang.Integer">
    select count(*)
    from <include refid="fileName"/>
    where is_delete = 0
    <if test="param.operator!= null ">
      and operator like concat ('%',#{param.operator},'%')
    </if>
    <if test="param.outerAccountMark != null">
      and operator_mis like concat('%',#{param.outerAccountMark},'%')
    </if>
    <if test="param.startTime != null">
      and create_time >= #{param.startTime}
    </if>
    <if test="param.endTime != null">
      and create_time <![CDATA[ <= ]]> #{param.endTime}
    </if>
  </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignDO" useGeneratedKeys="true">
    insert into <include refid="fileName"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="workContentType != null">
        work_content_type,
      </if>
      <if test="workbenchType!= null">
        workbench_type,
      </if>
      <if test="assignWorkStatus != null">
        assign_work_status,
      </if>
      <if test="assignCount != null">
        assign_count,
      </if>
      <if test="realAssignCount != null">
        real_assign_count,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="registerStartTime != null">
        register_start_time,
      </if>
      <if test="registerEndTime != null">
        register_end_time,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="operatorMis != null">
        operator_mis,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="workContentType != null">
        #{workContentType},
      </if>
      <if test="workbenchType!= null">
        #{workbenchType},
      </if>
      <if test="assignWorkStatus != null">
        #{assignWorkStatus},
      </if>
      <if test="assignCount != null">
        #{assignCount},
      </if>
      <if test="realAssignCount != null">
        #{realAssignCount},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="registerStartTime != null">
        #{registerStartTime},
      </if>
      <if test="registerEndTime != null">
        #{registerEndTime},
      </if>
      <if test="operator != null">
        #{operator},
      </if>
      <if test="operatorMis != null">
        #{operatorMis},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignDO">
    update <include refid="fileName"/>
    <set>
      <if test="workContentType != null">
        work_content_type = #{workContentType,},
      </if>
      <if test="workbenchType !=null">
        workbench_type = #{workbenchType}
      </if>
      <if test="assignWorkStatus != null">
        assign_work_status = #{assignWorkStatus},
      </if>
      <if test="assignCount != null">
        assign_count = #{assignCount},
      </if>
      <if test="realAssignCount != null">
        real_assign_count = #{realAssignCount},
      </if>
      <if test="status != null">
        `status` = #{status},
      </if>
      <if test="registerStartTime != null">
        register_start_time = #{registerStartTime},
      </if>
      <if test="registerEndTime != null">
        register_end_time = #{registerEndTime},
      </if>
      <if test="operator != null">
        `operator` = #{operator},
      </if>
      <if test="operatorMis != null">
        operator_mis = #{operatorMis},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete},
      </if>
    </set>
    where id = #{id}
  </update>

    <update id="updateAssignStatusById">
        update <include refid="fileName"/>
        set status = #{status}
        where id = #{id}
    </update>
</mapper>