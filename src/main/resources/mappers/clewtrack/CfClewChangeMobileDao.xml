<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewChangeMobileDao">

    <sql id="tableName">cf_clew_change_mobile_info</sql>

    <sql id="baseResult">
        `id`,
        `old_mobile`,
        `new_mobile`,
        `comment`,
        `source`,
        `operator_id`,
        `operator_type`,
        `create_time`
    </sql>

    <insert id="add">
        insert into
        <include refid="tableName"/>
        (old_mobile, new_mobile, comment, source, operator_id, operator_type)
        values (#{oldMobile}, #{newMobile}, #{comment}, #{source}, #{operatorId}, #{operatorType})
    </insert>


</mapper>
