<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseInfoActivationRecordDao">
    <sql id="file">
    id, clew_id, activation_reason, close_reason, create_time, update_time, is_delete,activation_status
  </sql>
    <sql id="table">
    cf_clew_base_info_activation_record
  </sql>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoActivationRecordDO"
            useGeneratedKeys="true">
        insert into
        <include refid="table"/>
        (clew_id, activation_reason, close_reason,
        create_time, update_time, is_delete,activation_status
        )
        values (#{clewId,jdbcType=BIGINT}, #{activationReason,jdbcType=VARCHAR}, #{closeReason,jdbcType=VARCHAR},
        #{createTime}, #{updateTime}, #{isDelete},#{activationStatus}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoActivationRecordDO"
            useGeneratedKeys="true">
        insert into
        <include refid="table"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clewId != null">
                clew_id,
            </if>
            <if test="activationReason != null">
                activation_reason,
            </if>
            <if test="closeReason != null">
                close_reason,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="activationStatus != null">
                activation_status
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clewId != null">
                #{clewId},
            </if>
            <if test="activationReason != null">
                #{activationReason},
            </if>
            <if test="closeReason != null">
                #{closeReason},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
            <if test="activationStatus != null">
                #{activationStatus}
            </if>
        </trim>
    </insert>
    <select id="selectRecordByClewIdAndReason"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoActivationRecordDO">
        select <include refid="file"/>
        from <include refid="table"/>
        where
        is_delete =0
        and clew_id = #{clewId}
        <if test="reason!=null">
            and activation_reason = #{reason}
        </if>
    </select>

</mapper>