<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfForecastParameterConfigurationDao">

    <sql id="tableName">cf_forecast_parameter_configuration</sql>

    <sql id="baseResult">
      `id` ,
      `task_type`,
      `task_description`,
      `start_time`,
      `end_time`,
      `answer_rate`,
      `predict_adjust`,
      `create_time`,
      `update_time`,
      `is_delete`
    </sql>

    <update id="updateForecastParameterConfigureById" parameterType="com.shuidihuzhu.cf.clewtrack.param.ForecastParamterParam">
        update
        <include refid="tableName"/>
        <set>
            <if test="answerRate != null">
                answer_rate=#{answerRate}  ,
            </if>
            <if test="predictAdjust != null ">
                predict_adjust=#{predictAdjust} ,
            </if>
        </set>
        where id=#{id}
    </update>

    <select id="getForecastParameterConfigurationByTaskTypeOrQueryTime"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastParameterConfigurationDo">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_type=#{taskType}
        <if test="queryTime != null">
            and start_time <![CDATA[ <= ]]> #{queryTime}
            and end_time > #{queryTime}
        </if>
    </select>

    <select id="getForecastConfigurationByTaskTypeAndQueryTime"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastParameterConfigurationDo">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_type in
        <foreach collection="taskTypes" item="taskType" open="(" separator="," close=")">
            #{taskType}
        </foreach>
        <if test="queryTime != null">
            and start_time <![CDATA[ <= ]]> #{queryTime}
            and end_time >= #{queryTime}
        </if>
    </select>

    <select id="getForecastParameterConfigurationById"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastParameterConfigurationDo">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id=#{id}
    </select>

</mapper>
