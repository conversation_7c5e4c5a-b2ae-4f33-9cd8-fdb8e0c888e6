<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTrackUserInfoDao">
    
    <sql id="table_name">
        cf_clew_track_user_info
    </sql>
    
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into <include refid="table_name" />
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="userId!=null and userId!=''">
            user_id,
        </if>
        <if test="userName!=null and userName!=''">
            user_name,
        </if>
        <if test="phone!=null and phone !=null">
            phone,
        </if>
        <if test="onlineStatus!=null">
            online_status,
        </if>
        <if test="lastHandleOnlineTime!=null">
            last_handle_online_time,
        </if>
        <if test="trackerType!=null">
            tracker_type,
        </if>
        <if test="cno!=null and cno!=''">
            cno,
        </if>
<!--        <if test="qyWechatUserId!=null and qyWechatUserId!=''">-->
<!--            qy_wechat_user_id,-->
<!--        </if>-->
        <if test="qyWechatUserIdEncrypt!=null and qyWechatUserIdEncrypt!=''">
            qy_wechat_user_id_encrypt,
        </if>
        <if test="predictiveCno!=null and predictiveCno!=''">
            predictive_cno,
        </if>
        <if test="isDelete != null">
            is_delete,
        </if>
        <if test="misType != null">
            mis_type,
        </if>
        <if test="userLevel >= 0">
            user_level,
        </if>
        <if test="isTop!=null">
            isTop,
        </if>
        <if test="firstHandleOnlineTime != null">
            `first_handle_online_time`,
        </if>
        <if test="qiCno != null and qiCno !=''">
            `qi_cno`,
        </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="userId!=null and userId!=''">
            #{userId},
        </if>
        <if test="userName!=null and userName!=''">
            #{userName},
        </if>
        <if test="phone!=null and phone !=null">
            #{phone},
        </if>
        <if test="onlineStatus!=null">
            #{onlineStatus},
        </if>
        <if test="lastHandleOnlineTime!=null">
            #{lastHandleOnlineTime},
        </if>
        <if test="trackerType!=null">
            #{trackerType},
        </if>
        <if test="cno!=null and cno!=''">
            #{cno},
        </if>
<!--        <if test="qyWechatUserId!=null and qyWechatUserId!=''">-->
<!--            #{qyWechatUserId},-->
<!--        </if>-->
        <if test="qyWechatUserIdEncrypt!=null and qyWechatUserIdEncrypt!=''">
            #{qyWechatUserIdEncrypt},
        </if>
        <if test="predictiveCno!=null and predictiveCno!=''">
            #{predictiveCno},
        </if>
        <if test="isDelete != null">
            #{isDelete} ,
        </if>
        <if test="misType != null">
            #{misType} ,
        </if>
        <if test="userLevel >= 0">
            #{userLevel},
        </if>
        <if test="isTop!=null">
            #{isTop},
        </if>
        <if test="firstHandleOnlineTime != null">
            #{firstHandleOnlineTime} ,
        </if>
        <if test="qiCno != null and qiCno !=''">
            #{qiCno},
        </if>
    </trim>
    </insert>

    <sql id="baseSelect">
          id,create_time,user_id,user_name,phone,online_status,last_handle_online_time,tracker_type,cno,is_lock,
<!--        qy_wechat_user_id,-->
        qy_wechat_user_id_encrypt,
        predictive_cno,is_delete,update_time,mis_type,
          user_level,is_top,`first_handle_online_time`,`qi_cno`,`on_work_time`
    </sql>

    <update id="update1v1UserOnlineStatus">
        update <include refid="table_name" />
        set online_status = #{onlineStatus}
        where id = #{id}
        and is_delete = 0
    </update>

    <select id="getByUserIdAndType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
      select
        <include refid="baseSelect"/>
      from <include refid="table_name" />
      where user_id in
      <foreach collection="useridList" item="userid" open="(" separator="," close=")">
          #{userid}
      </foreach>  and tracker_type=#{trackerUserType}
    </select>

    <select id="getClewUserInfoDetailById"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select
        <include refid="baseSelect"/>
        from <include refid="table_name" />
        where id=#{id}
    </select>

    <update id="updateClewUserInfoById" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        update <include refid="table_name" />
        <set >
            <if test="userId != null and userId !=''" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName!='' " >
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone !='' " >
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="trackerType != null " >
                tracker_type = #{trackerType,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null" >
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="cno !=null and cno != ''">
                cno = #{cno},
            </if>
            <if test="isLock != null">
                is_lock = #{isLock},
            </if>
<!--            <if test="qyWechatUserId != null and qyWechatUserId!=''">-->
<!--                qy_wechat_user_id = #{qyWechatUserId},-->
<!--            </if>-->
            <if test="qyWechatUserIdEncrypt != null and qyWechatUserIdEncrypt!=''">
                qy_wechat_user_id_encrypt = #{qyWechatUserIdEncrypt},
            </if>
            <if test="predictiveCno!=null and predictiveCno!=''">
                predictive_cno = #{predictiveCno},
            </if>
            <if test="misType != null ">
                mis_type = #{misType} ,
            </if>
            <if test="userLevel >= 0">
              user_level = #{userLevel},
            </if>
            <if test="isTop != null">
              is_top = #{isTop},
            </if>
            <if test="qiCno != null and qiCno !=''">
              qi_cno = #{qiCno},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="getUserInfoListByTrackerType" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where tracker_type = #{trackerType}
        <if test="isDelete != null" >
            and is_delete = #{isDelete}
        </if>
    </select>

    <select id="getClewUserByPacketIdAndUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        SELECT
        DISTINCT b.id,b.create_time,b.user_id,b.user_name,b.phone,b.online_status,b.last_handle_online_time,b.tracker_type,b.cno,b.is_lock
        FROM
            cf_clew_packet_member a
            JOIN <include refid="table_name" /> b ON a.user_id = b.user_id
        WHERE 1=1
        <if test="packetIds!=null and packetIds.size()>0">
            and a.packet_id in
            <foreach collection="packetIds" item="packetId" open="(" close=")" separator=",">
              #{packetId}
            </foreach>
        </if>
        <if test="userId!=null and userId!=''">
          and a.user_id = #{userId}
        </if>
        and a.is_delete=0
    </select>
    <select id="getFuwuClewUserByPacketIdAndUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        SELECT
         b.id,b.create_time,b.user_id,b.user_name,
         b.phone,b.online_status,b.last_handle_online_time,
         b.tracker_type,b.cno,b.is_lock,a.work_content_type,
         a.today_can_assign_max_num, a.today_can_assign_min_num,
         a.current_can_assign_max_num, a.current_can_assign_min_num,
         a.clew_assign_gap, b.is_top
        FROM
        cf_clew_packet_member a
        JOIN <include refid="table_name" /> b ON a.user_id = b.user_id and b.tracker_type=2
        WHERE 1=1
        <if test="packetIds!=null and packetIds.size()>0">
            and a.packet_id in
            <foreach collection="packetIds" item="packetId" open="(" close=")" separator=",">
                #{packetId}
            </foreach>
        </if>
        <if test="userIds!=null and userIds.size>0">
            and a.user_id in
            <foreach collection="userIds" item="userid" open="(" separator="," close=")">
                #{userid}
            </foreach>
        </if>
        <if test="workContentType!=null">
            and a.work_content_type = #{workContentType}
        </if>
        and a.is_delete=0
        and b.is_delete=0
    </select>
    <select id="getClewUserIdsByCno" resultType="java.lang.String">
        SELECT distinct user_id from <include refid="table_name" /> where cno=#{cno}
    </select>

    <select id="getQyWechatUserIdEncryptByUserIdWithTrackerType" resultType="java.lang.String">
        select qy_wechat_user_id_encrypt from <include refid="table_name" /> where user_id = #{userId} and tracker_type=#{trackerType}
    </select>

    <select id="getByUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" /> where user_id = #{userId} and is_delete = 0
    </select>

    <select id="getQyWechatUserIdEncryptByUserIdBatch" resultType="java.lang.String">
        select qy_wechat_user_id_encrypt from <include refid="table_name" /> where tracker_type=#{trackerType}
        <if test="userIds!=null and userIds.size>0">
            and user_id in
            <foreach collection="userIds" item="userid" open="(" separator="," close=")">
                #{userid}
            </foreach>
        </if>
    </select>
    <select id="getPredictiveCnos" resultType="java.lang.String">
        select DISTINCT b.predictive_cno from
        cf_clew_packet_member a
        JOIN <include refid="table_name" /> b ON a.user_id = b.user_id
        WHERE b.predictive_cno!=''
        and to_days(b.last_handle_online_time)=to_days(now())
        and <![CDATA[ (b.online_status&7) =1 ]]>
        and a.work_content_type = #{workContentType}
        and a.is_delete = 0
		and b.is_delete = 0
    </select>
    <select id="getClewUserInfoByPredictiveCno" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where predictive_cno=#{predictiveCno}
        and is_delete = 0
        ORDER BY create_time desc
        limit 1;
    </select>
    <select id="getClewUserByPacketIdAndUserIdAndWorkContentType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        SELECT
         b.id,b.create_time,b.user_id,b.user_name,b.phone,b.online_status,b.last_handle_online_time,b.tracker_type,b.cno,b.is_lock,a.work_content_type,
                 a.online_time,a.latest_online,a.latest_suspend, b.is_top,b.first_handle_online_time
        FROM
        cf_clew_packet_member a
        JOIN <include refid="table_name" /> b ON a.user_id = b.user_id and b.tracker_type = #{trackerType}
        WHERE 1=1
        <if test="packetIds!=null and packetIds.size()>0">
            and a.packet_id in
            <foreach collection="packetIds" item="packetId" open="(" close=")" separator=",">
                #{packetId}
            </foreach>
        </if>
        <if test="userId!=null and userId!=''">
            and a.user_id = #{userId}
        </if>
        <if test="workContentType!=null">
            and a.work_content_type = #{workContentType}
        </if>
        and a.is_delete=0 and b.is_delete = 0
    </select>
    <select id="getByUserIdAndTypeWithWorkContentType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select
        cctui.id as id , cctui.create_time as create_time , cctui.user_id as user_id,
        cctui.user_name as user_name , cctui.phone as phone , cctui.online_status as online_status,
        cctui.last_handle_online_time as last_handle_online_time , cctui.tracker_type as tracker_type,
        cctui.cno as cno , cctui.is_lock as is_lock ,
<!--        cctui.qy_wechat_user_id as qy_wechat_user_id,-->
        cctui.qy_wechat_user_id_encrypt as qy_wechat_user_id_encrypt,
        cctui.predictive_cno as predictive_cno , ccpm.work_content_type as work_content_type ,
        ccpm.today_max_assign_clew_count as today_max_assign_clew_count , ccpm.today_handle_clew_count as today_handle_clew_count,
        ccpm.today_assgin_clew_count as today_assgin_clew_count,
        cctui.is_top as is_top,cctui.user_level as userLevel, ccpm.member_role as memberRole
        from <include refid="table_name" /> cctui
        join cf_clew_packet_member ccpm
        on ccpm.user_id = cctui.user_id
        where cctui.user_id = #{userId}
        and tracker_type = #{trackerUserType}
        and ccpm.packet_id = #{packetId}
    </select>
    <select id="getCfClewTrackUserInfoByUserIdAndTrackerType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where user_id = #{userId}
        and tracker_type=#{trackerType}
        order by id desc
        limit 1
    </select>

    <select id="getCfClewTrackUserInfo"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where user_id = #{userId}
        and tracker_type=#{trackerType}
        and is_delete = 0
        order by id desc
        limit 1
    </select>

    <select id="selectClewTrackUserInfoByName"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where user_name = #{userName}
        and tracker_type=#{trackerType}
        and is_delete = 0
    </select>

    <select id="selectClewTrackUserInfoByJobNum"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where cno = #{jobNum}
        and tracker_type=#{trackerType}
        and is_delete = 0
        order by id desc
        limit 1
    </select>

    <select id="getAllPredictiveCnos" resultType="java.lang.String">
        select DISTINCT predictive_cno from
        <include refid="table_name" />
        WHERE predictive_cno!='' and is_delete = 0
    </select>

    <select id="getClewInfoByTrackerUserInfoSearchParam"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where 1=1
        <if test="userId!=null and userId!=''">
            and user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            and user_name = #{userName}
        </if>
        <if test="roleStatus != null and (roleStatus == 0 or roleStatus == 1) ">
            and is_delete = #{roleStatus}
        </if>
        order by create_time desc
    </select>

    <select id="getUserId" resultType="java.lang.String">
        SELECT user_id
        from <include refid="table_name" />
        where is_delete = 0 and tracker_type = 2
    </select>

    <update id="updateTrackUserInfoByMisId">
        update <include refid="table_name" />
        <set>
        <if test="jobStatus != null">
            is_delete=#{jobStatus} ,
        </if>
        </set>
        where user_id = #{misId}
    </update>
    <update id="updateUserOnlineStatusById">
        update <include refid="table_name" />
        <set>
            <if test="isLock != null">
                is_lock=#{isLock},
            </if>
            <if test="onlineStatus != null">
                online_status=#{onlineStatus},
            </if>
            <if test="lastHandleOnlineTime != null">
                last_handle_online_time=#{lastHandleOnlineTime},
            </if>
            <if test="firstHandleOnlineTime != null">
                first_handle_online_time = #{firstHandleOnlineTime} ,
            </if>
        </set>
        where id=#{id}
    </update>

    <select id="getCfClewTrackUserInfoByUserIdAndIsValid"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where user_id = #{userId}
        <if test="isDelete != null ">
           and is_delete=#{isDelete}
        </if>
    </select>

    <select id="getTrackerUserIdsByWorkContentList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        SELECT
        cctui.id as id , cctui.create_time as create_time , cctui.user_id as user_id,
        cctui.user_name as user_name , cctui.phone as phone , cctui.online_status as online_status,
        cctui.last_handle_online_time as last_handle_online_time , cctui.tracker_type as tracker_type,
        cctui.cno as cno , cctui.is_lock as is_lock ,
<!--        cctui.qy_wechat_user_id as qy_wechat_user_id,-->
        cctui.qy_wechat_user_id_encrypt as qy_wechat_user_id_encrypt,
        cctui.on_work_time as  on_work_time,
        cctui.predictive_cno as predictive_cno , ccpm.work_content_type as work_content_type,cctui.is_top as is_top
        from <include refid="table_name" /> cctui
        left join cf_clew_packet_member ccpm
        on cctui.id=ccpm.cf_clew_tracker_id
        where cctui.is_delete = 0 and ccpm.is_delete = 0
        and ccpm.work_content_type in
        <foreach collection="workContentList" item="workContentType" open="(" close=")" separator=",">
            #{workContentType}
        </foreach>
    </select>

    <select id="getClewUserByPacketIdAndUserIdsAndWorkContentType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        SELECT
         b.id,b.create_time,b.user_id,b.user_name,b.phone,b.online_status,b.last_handle_online_time,b.tracker_type,b.cno,b.is_lock,a.work_content_type,
        a.online_time,a.latest_online,a.latest_suspend,b.is_top
        FROM
        cf_clew_packet_member a
        JOIN <include refid="table_name" /> b ON a.user_id = b.user_id and b.tracker_type = #{trackerType}
        WHERE 1=1
        <if test="packetIds!=null and packetIds.size()>0">
            and a.packet_id in
            <foreach collection="packetIds" item="packetId" open="(" close=")" separator=",">
                #{packetId}
            </foreach>
        </if>
        <if test="userIds!=null and userIds.size()>0">
            and a.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="workContentType!=null">
            and a.work_content_type = #{workContentType}
        </if>
        and a.is_delete=0 and b.is_delete = 0
    </select>

    <select id="listByUserIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where is_delete = 0 and user_id in
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listByCursor" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        select <include refid="baseSelect"/>
        from <include refid="table_name" />
        where is_delete = 0 and id &gt; #{beginId} order by id asc
        limit #{limit}
    </select>

    <update id="updateUserIsTop">
        update <include refid="table_name" />
        set is_top = #{isTop}
         where  tracker_type = #{trackType} and user_id in
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="clearQywechatIdByUserIdAndTrackerType">
        update <include refid="table_name" />
        set
<!--        qy_wechat_user_id = '',-->
        qy_wechat_user_id_encrypt = ''
        where user_id = #{userId}
        and tracker_type=#{trackerType}
    </update>

    <update id="updateDeleteUserInfo">
        update <include refid="table_name" />
        set is_delete = 1
        where id = #{id}
    </update>

    <select id="listClewTrackUserInfo" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO">
        SELECT
        cctui.id as id , cctui.create_time as create_time, cctui.user_id as user_id,
        cctui.user_name as user_name, cctui.phone as phone, cctui.online_status as online_status,
        cctui.last_handle_online_time as last_handle_online_time, cctui.tracker_type as tracker_type,
        cctui.cno as cno, cctui.is_lock as is_lock,
<!--        cctui.qy_wechat_user_id as qy_wechat_user_id,-->
        cctui.qy_wechat_user_id_encrypt as qy_wechat_user_id_encrypt,
        cctui.predictive_cno as predictive_cno,cctui.is_top as is_top,cctui.user_level as userLevel,
        ccpm.work_content_type as work_content_type,
        ccpm.today_max_assign_clew_count as today_max_assign_clew_count, ccpm.today_handle_clew_count as today_handle_clew_count,
        ccpm.today_assgin_clew_count as today_assgin_clew_count,
        ccpm.online_time,ccpm.latest_online,ccpm.latest_suspend,
        cctui.first_handle_online_time
        FROM
        cf_clew_packet_member ccpm
        JOIN <include refid="table_name" /> cctui ON ccpm.user_id = cctui.user_id and cctui.tracker_type = #{trackerType}
        WHERE 1=1
        <if test="packetId!=null">
            and ccpm.packet_id = #{packetId}
        </if>
        <if test="userIds!=null and userIds.size()>0">
            and ccpm.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="workContentType!=null">
            and ccpm.work_content_type = #{workContentType}
        </if>
        and ccpm.is_delete=0 and cctui.is_delete = 0
    </select>

    <update id="modifyOnWorkTime">
        update <include refid="table_name" />
        set on_work_time = #{onWorkTime}
        where is_delete = 0
        and user_id = #{userId}
    </update>

    <select id="getMisByUserNameList" resultType="java.util.Map">
        select distinct user_name as userName, user_id as userId
        from <include refid="table_name" />
        where is_delete = 0
        and user_name in
        <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
            #{userName}
        </foreach>
    </select>
    <select id="getOnlineQyWechatUserIdEncryptByUserIds" resultType="java.lang.String">
        select qy_wechat_user_id_encrypt
        from <include refid="table_name" />
        where is_delete = 0
        and qy_wechat_user_id_encrypt !=''
        and <![CDATA[ ((online_status>>(2*3))&7)=1 ]]>
        and to_days(last_handle_online_time)=to_days(now()) and user_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>
</mapper>
