<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCallRecordsDao">
    <sql id="fileds">
        id,user_id,clew_id,packet_id,task_id,phone_status,record_file,unique_id,answer_time,
        total_duration,satisfaction,is_delete,create_time,update_time,display_id,
        encrypt_customer_number,status,cno_start_time,cno_end_time,call_type,case_id,page_name,
        client_name,operator_type,org_name,cos_file,cos_handle_type,end_reason,
        business_line,business_no,app_code,call_source,scene_num
    </sql>
    <insert id="saveCallRecords" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO"
            useGeneratedKeys="true" keyProperty="id">
        insert ignore into cf_clew_call_records
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId!=null and userId!=''">
                user_id,
            </if>
            <if test="clewId!=null">
                clew_id,
            </if>
            <if test="packetId!=null">
                packet_id,
            </if>
            <if test="taskId!=null">
                task_id,
            </if>
            <if test="phoneStatus!=null">
                phone_status,
            </if>
            <if test="recordFile!=null and recordFile!=''">
                record_file,
            </if>
            <if test="uniqueId!=null and uniqueId!=''">
                unique_id,
            </if>
            <if test="answerTime!=null">
                answer_time,
            </if>
            <if test="totalDuration!=null">
                total_duration,
            </if>
            <if test="satisfaction!=null">
                satisfaction,
            </if>
            <if test="displayId != null and displayId!=''">
                display_id,
            </if>
            <if test="status !=null and status!=''">
                status,
            </if>
            <if test="encryptCustomerNumber != null and encryptCustomerNumber != ''">
                encrypt_customer_number,
            </if>
            <if test="callType != null ">
                call_type,
            </if>
            <if test="pageName != null and pageName != ''">
                page_name,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="clientName != null and clientName != ''">
                client_name,
            </if>
            <if test="operatorType != null and operatorType != ''">
                operator_type,
            </if>
            <if test="orgName != null and orgName != ''">
                org_name,
            </if>
            <if test="personId!=null and personId!=''">
                person_id,
            </if>
            <if test="uuid!=null and uuid!=''">
                uuid,
            </if>
            <if test="businessLine!=null">
                business_line,
            </if>
            <if test="businessNo != null">
                business_no,
            </if>
            <if test="appCode !=null and appCode !=''">
                app_code,
            </if>
            <if test="callSource !=null and callSource !=''">
                call_source,
            </if>
            <if test="sceneNum !=null and sceneNum !=''">
                scene_num,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId!=null and userId!=''">
                #{userId},
            </if>
            <if test="clewId!=null">
                #{clewId},
            </if>
            <if test="packetId!=null">
                #{packetId},
            </if>
            <if test="taskId!=null">
                #{taskId},
            </if>
            <if test="phoneStatus!=null">
                #{phoneStatus},
            </if>
            <if test="recordFile!=null and recordFile!=''">
                #{recordFile},
            </if>
            <if test="uniqueId!=null and uniqueId!=''">
                #{uniqueId},
            </if>
            <if test="answerTime!=null">
                #{answerTime},
            </if>
            <if test="totalDuration!=null">
                #{totalDuration},
            </if>
            <if test="satisfaction!=null">
                #{satisfaction},
            </if>
            <if test="displayId != null and displayId!=''">
                #{displayId},
            </if>
            <if test="status !=null and status!=''">
                #{status},
            </if>
            <if test="encryptCustomerNumber != null and encryptCustomerNumber != ''">
                #{encryptCustomerNumber} ,
            </if>
            <if test="callType != null ">
                #{callType} ,
            </if>
            <if test="pageName != null and pageName != ''">
                #{pageName} ,
            </if>
            <if test="caseId != null">
                #{caseId} ,
            </if>
            <if test="clientName != null and clientName != ''">
                #{clientName} ,
            </if>
            <if test="operatorType != null and operatorType != ''">
                #{operatorType} ,
            </if>
            <if test="orgName != null and orgName != ''">
                #{orgName},
            </if>
            <if test="personId!=null and personId!=''">
                #{personId},
            </if>
            <if test="uuid!=null and uuid!=''">
                #{uuid},
            </if>
            <if test="businessLine!=null">
                #{businessLine},
            </if>
            <if test="businessNo != null">
                #{businessNo},
            </if>
            <if test="appCode !=null and appCode !=''">
                #{appCode},
            </if>
            <if test="callSource !=null and callSource !=''">
                #{callSource},
            </if>
            <if test="sceneNum !=null and sceneNum !=''">
                #{sceneNum},
            </if>
        </trim>
    </insert>

    <update id="updateCallRecordsByUniqueId" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        update cf_clew_call_records
        <set>
            <if test="phoneStatus!=null">
                phone_status = #{phoneStatus},
            </if>
            <if test="recordFile!=null and recordFile!=''">
                record_file = #{recordFile},
            </if>
            <if test="totalDuration!=null">
                total_duration = #{totalDuration},
            </if>
            <if test="satisfaction!=null">
                satisfaction = #{satisfaction},
            </if>
            <if test="answerTime!=null">
                answer_time = #{answerTime},
            </if>
            <if test="status!=null and status!=''">
                status = #{status},
            </if>
            <if test="cnoStartTime!=null">
                cno_start_time = #{cnoStartTime},
            </if>
            <if test="cnoEndTime!=null">
                cno_end_time = #{cnoEndTime},
            </if>
            <if test="clientName != null and clientName != ''">
                client_name = #{clientName} ,
            </if>
            <if test="endReason != null and endReason != ''">
                end_reason = #{endReason}
            </if>
        </set>
        where unique_id = #{uniqueId}
    </update>

    <update id="updateCallRecordsStatusByUniqueId">
        update cf_clew_call_records set status = #{status}
        where unique_id = #{uniqueId}
    </update>




    <select id="getNoClewIdWithTaskIdCallRecordsByDisplayId" resultType="java.lang.Integer">
        select id
        from cf_clew_call_records
        where display_id = #{displayId} and clew_id = 0 and task_id = 0
    </select>
    <select id="getByTaskIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where task_id in
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="getCalledCallRecordsByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where create_time!=answer_time and clew_id in
        <foreach collection="clewIds" item="clewId" separator="," open="(" close=")">
            #{clewId}
        </foreach>
    </select>
    <select id="getCalledCallRecordsByUserIdAndPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where user_id=#{userId} and encrypt_customer_number=#{encryptCustomerNumber}
        and create_time <![CDATA[ >= ]]> #{startTime} order by id desc limit 1;
    </select>

    <update id="updateClewIdWithTaskIdById">
        update cf_clew_call_records
        set clew_id = #{clewId},task_id = #{taskId},packet_id=#{packetId}
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        order by id desc
    </update>
    <select id="getCfClewCallRecordsDOByUniqueId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where unique_id = #{uniqueId}
        limit 1;
    </select>
    <select id="getLaunchTimeWithin7DayCallRecord" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        SELECT <include refid="fileds"/> from cf_clew_call_records
        where clew_id=#{clewId}
        and task_id=#{taskId}
        and phone_status=200
        and #{launchTime} BETWEEN create_time and date_add(create_time, interval 7 day)
        limit 1;
    </select>
    <select id="getEarliestCalledCallRecordsByClewIdAndTaskId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        SELECT <include refid="fileds"/> from cf_clew_call_records
        where clew_id=#{clewId}
        and task_id=#{taskId}
        and phone_status=200
        order by create_time limit 1;
    </select>
    <select id="getUserCurrentDayCallsByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.model.CfUserCallRecordModel">
        SELECT
          user_id as clewUserId,
          count(distinct(task_id)) as calls,
          sum(total_duration) as totalDuration
        from cf_clew_call_records
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" close=")" separator=",">
            #{clewId}
        </foreach>
        and phone_status=200
        GROUP BY user_id;
    </select>
    <select id="getCfClewCallRecordsDOByMobile" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where is_delete=0
        and encrypt_customer_number = #{encryptCustomerNumber}
    </select>

    <update id="updateCallRecordsByUniqueIds">
        update cf_clew_call_records
        set case_id = #{caseId},
        operator_type = #{operatorType}
        where unique_id in
        <foreach collection="uniqueIds" item="uniqueId" separator="," open="(" close=")">
            #{uniqueId}
        </foreach>
    </update>
    <update id="updatePersonIdAndUuidByClewId">
        update cf_clew_call_records
        <set>
            <if test="personId!=null and personId!=''">
                person_id=#{personId},
            </if>
            <if test="uuid!=null and uuid!=''">
                uuid = #{uuid},
            </if>
        </set>
        where clew_id = #{clewId}
    </update>
    <update id="updatePersonIdAndUuidById">
        update cf_clew_call_records
        <set>
            <if test="personId!=null and personId!=''">
                person_id=#{personId},
            </if>
            <if test="uuid!=null and uuid!=''">
                uuid=#{uuid},
            </if>
        </set>
        where id=#{id}
    </update>
    <update id="updateCosFileById">
        update cf_clew_call_records
        set cos_file = #{cosFile},
        cos_handle_type = 1
        where id=#{id}
    </update>

    <select id="getCfClewCallRecordsDOByUniqueIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where is_delete=0 and unique_id in
        <foreach collection="uniqueIds" item="uniqueId" open="(" close=")" separator=",">
            #{uniqueId}
        </foreach>
    </select>
    <select id="getInRecordByIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and clew_id=0
    </select>
    <select id="getCallRecordByIdList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where is_delete = 0
        and id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        and clew_id !=0
    </select>

    <select id="listCallRecords" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        SELECT <include refid="fileds"/>
        from cf_clew_call_records
        where is_delete=0
        <if test="queryParam.clewId != null"> and clew_id=#{queryParam.clewId}</if>
        <if test="queryParam.taskId != null"> and task_id=#{queryParam.taskId}</if>
        <if test="encryptCustomerNumber != '' and encryptCustomerNumber != null">
            and encrypt_customer_number=#{encryptCustomerNumber}
        </if>
        <if test="queryParam.tab !=0">
            and phone_status=200
        </if>
        <if test="queryParam.excludePhones != null and queryParam.excludePhones.size() > 0">
            and encrypt_customer_number not in
            <foreach collection="queryParam.excludePhones" item="excludePhone" open="(" separator="," close=")">
                #{excludePhone}
            </foreach>
        </if>
        and create_time BETWEEN #{queryParam.dateQueryParam.startTime} AND #{queryParam.dateQueryParam.endTime}
    </select>


    <select id="listCallRecordsForFeign" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
    SELECT <include refid="fileds"/>
        from cf_clew_call_records
        where is_delete=0
        and encrypt_customer_number in <foreach collection="encryptPhoneList" item="encryptPhone" open="(" separator="," close=")">#{encryptPhone}</foreach>
        <if test="searchModel.tab == 1">
            and phone_status=200
        </if>
        <if test="searchModel.tab ==2">
            and phone_status!=200
        </if>
        <if test="searchModel.pageName != null and searchModel.pageName != ''">
            and page_name = #{searchModel.pageName}
        </if>
        <if test="searchModel.userId != null and searchModel.userId != ''">
            and user_id = #{searchModel.userId}
        </if>
        <if test="searchModel.startTime != null and searchModel.endTime != null">
            and create_time BETWEEN #{searchModel.startTime} AND #{searchModel.endTime}
        </if>
    </select>

    <select id="listCallRecordsByMisAndPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where is_delete=0 and user_id=#{userId}
        <if test="encryptSecondPhone != null and encryptSecondPhone != ''">
            and encrypt_customer_number in (#{encryptPhone},#{encryptSecondPhone})
        </if>
        <if test="encryptSecondPhone == null or encryptSecondPhone == ''">
            and encrypt_customer_number=#{encryptPhone}
        </if>
        and create_time <![CDATA[ >= ]]> #{taskAssignDate}
    </select>

    <select id="listAllCallRecordByClewIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where clew_id in
        <foreach collection="clewIds" item="clewId" separator="," open="(" close=")">
            #{clewId}
        </foreach>
    </select>


    <select id="listAllCallRecordByTimeAndPacket"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where create_time BETWEEN #{startTime} AND #{endTime}
        <if test="packetId != null">
            and packet_id = #{packetId}
        </if>

    </select>

    <select id="listCallRecordsByTimeAndMis" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from `cf_clew_call_records`
        where `create_time` between #{startTime} and #{endTime}
        <if test="userIds != null and userIds.size() > 0">
            and `user_id` in
            <foreach collection="userIds" item="userId" close=")" open="(" separator=",">
                #{userId}
            </foreach>
        </if>
    </select>

    <update id="updateCallRecordById" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        update cf_clew_call_records
        <set>
            <if test="phoneStatus!=null">
                phone_status = #{phoneStatus},
            </if>
            <if test="recordFile!=null and recordFile!=''">
                record_file = #{recordFile},
            </if>
            <if test="cosFile!=null and cosFile!=''">
                cos_file = #{cosFile},
            </if>
            <if test="totalDuration!=null">
                total_duration = #{totalDuration},
            </if>
            <if test="satisfaction!=null">
                satisfaction = #{satisfaction},
            </if>
            <if test="answerTime!=null">
                answer_time = #{answerTime},
            </if>
            <if test="status!=null and status!=''">
                status = #{status},
            </if>
            <if test="cnoStartTime!=null">
                cno_start_time = #{cnoStartTime},
            </if>
            <if test="cnoEndTime!=null">
                cno_end_time = #{cnoEndTime},
            </if>
            <if test="endReason != null and endReason != ''">
                end_reason = #{endReason},
            </if>
            <if test="cosHandleType != null and cosHandleType != ''">
                cos_handle_type = #{cosHandleType},
            </if>
        </set>
        where id = #{id}
    </update>


    <select id="listCallMsgFromPlatform" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO">
        select <include refid="fileds"/>
        from cf_clew_call_records
        where
        create_time >= #{startTime}
        and create_time &lt;= #{endTime}
        and call_source = 1
        and cos_handle_type = 0
    </select>


</mapper>
