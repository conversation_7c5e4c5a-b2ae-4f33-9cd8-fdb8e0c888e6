<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfOperatingRecordDao">
    <sql id="tableName">cf_operating_record</sql>
    <sql id="field">
        `id`,
        `operate_key`,
        `operate_type`,
        `operate_desc`,
        `content`,
        `operate_name`,
        `operate_user_id`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <select id="getOperatelogList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfOperatingRecordDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and operate_type=#{operateLogDTO.operateType}
        <if test="operateLogDTO.operateName!=null and operateLogDTO.operateName!='' ">and operate_name like concat('%',#{operateLogDTO.operateName},'%')</if>
        <if test="operateLogDTO.startTime!=null and operateLogDTO.startTime!='' ">and create_time>=#{operateLogDTO.startTime}</if>
        <if test="operateLogDTO.endTime!=null and operateLogDTO.endTime!='' ">and <![CDATA[  create_time<=#{operateLogDTO.endTime} ]]></if>

        <if test="operateLogDTO.isOuter!= null and operateLogDTO.isOuter !='' and operateLogDTO.isOuter==1">
            and operate_user_id <![CDATA[ >= ]]> 100000000
        </if>
        <if test="operateLogDTO.isOuter!= null and operateLogDTO.isOuter !='' and operateLogDTO.isOuter==2">
            and operate_user_id <![CDATA[ < ]]> 100000000
        </if>

        order by create_time desc
        limit #{offset},#{operateLogDTO.pageSize}
    </select>
    <select id="getOperatelogCount" resultType="java.lang.Long">
        select count(*) from <include refid="tableName"/>
        where is_delete = 0 and operate_type=#{operateLogDTO.operateType}
        <if test="operateLogDTO.operateName!=null and operateLogDTO.operateName!='' ">and operate_name like concat('%',#{operateLogDTO.operateName},'%')</if>
        <if test="operateLogDTO.startTime!=null and operateLogDTO.startTime!='' ">and create_time>=#{operateLogDTO.startTime}</if>
        <if test="operateLogDTO.endTime!=null and operateLogDTO.endTime!='' ">and <![CDATA[  create_time<=#{operateLogDTO.endTime} ]]></if>
        <if test="operateLogDTO.isOuter!= null and operateLogDTO.isOuter !='' and operateLogDTO.isOuter==1">
            and operate_user_id <![CDATA[ >= ]]> 100000000
        </if>
        <if test="operateLogDTO.isOuter!= null and operateLogDTO.isOuter !='' and operateLogDTO.isOuter==2">
            and operate_user_id <![CDATA[ < ]]> 100000000
        </if>
    </select>
    <insert id="saveOperateLog">
        insert into <include refid="tableName"/>
        (`operate_key`,`operate_type`,`operate_desc`,`content`
        <if test="operateName!=null and operateName !=''">,`operate_name`</if>
        <if test="operateUserId!=null ">,`operate_user_id`</if>)
        value
        (#{operateKey},#{operateType},#{operateDesc},#{content}
        <if test="operateName!=null and operateName !=''">,#{operateName}</if>
        <if test="operateUserId!=null">,#{operateUserId}</if>)
    </insert>

    <select id="listOperatelog" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfOperatingRecordDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and operate_type=#{operateType} and operate_key = #{operateKey}
        order by id desc
        <if test="offset != null and pageSize != null">
            limit #{offset},#{pageSize}
        </if>
    </select>

    <select id="listOperateLogCount" resultType="java.lang.Long">
        select count(*)
        from <include refid="tableName"/>
        where is_delete = 0 and operate_type = #{operateType} and operate_key = #{operateKey}
    </select>

    <select id="listOptLogRecordByOptKey" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfOperatingRecordDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and operate_key = #{optKey}
        <if test="optTypes != null and optTypes.size() != 0">
            and operate_type in
            <foreach collection="optTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
