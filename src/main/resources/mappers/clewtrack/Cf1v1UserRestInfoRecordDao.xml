<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.Cf1v1UserRestInfoRecordDao">
    <sql id="tableName">
        `cf_1v1_user_rest_info_record`
    </sql>
    <sql id="fileds">
        `id`,
        `operate_name`,
        `operate_id`,
        `import_status`,
        `file_name`,
        `file_url`,
        `user_id`,
        `rest_info_id`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operateName != null and operateName != ''">
                operate_name,
            </if>
            <if test="operateId != null and operateId != ''">
                operate_id,
            </if>
            <if test="importStatus != null">
                import_status,
            </if>
            <if test="fileName != null and fileName != ''">
                file_name,
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operateName != null and operateName != ''">
                #{operateName},
            </if>
            <if test="operateId != null and operateId != ''">
                #{operateId},
            </if>
            <if test="importStatus != null">
                #{importStatus},
            </if>
            <if test="fileName != null and fileName != ''">
                #{fileName},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                #{fileUrl}
            </if>
        </trim>
    </insert>

    <insert id="batchInsert">
        insert into
        <include refid="tableName"/>
        (operate_name, operate_id, import_status, rest_info_id)
        values
        <foreach collection="userRestInfoRecordDOList" item="userRestInfo" separator=",">
            (#{userRestInfo.operateName}, #{userRestInfo.operateId},
            #{userRestInfo.importStatus}, #{userRestInfo.restInfoId})
        </foreach>
    </insert>

    <select id="getImportUserRestRecord" resultType="com.shuidihuzhu.cf.clewtrack.domain.Cf1v1UserRestInfoRecordDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and import_status in
        <foreach collection="importStatusList" separator="," open="(" close=")" item="importStatus">
            #{importStatus}
        </foreach>
        and create_time > #{startDate}
        order by create_time desc
    </select>

    <select id="getModifyUserRestRecord" resultType="com.shuidihuzhu.cf.clewtrack.domain.Cf1v1UserRestInfoRecordDO">
        select <include refid="fileds"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and import_status = #{importStatus}
        and rest_info_id in
        <foreach collection="restInfoIds" item="restInfoId" close=")" open="(" separator=",">
            #{restInfoId}
        </foreach>
    </select>


</mapper>
