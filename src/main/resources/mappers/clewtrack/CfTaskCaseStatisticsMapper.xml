<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfTaskCaseStatisticsDao">

    <sql id="baseResult">
        `id`,
        `date_time`,
        `org_id`,
        `org_name`,
        `user_id`,
        `user_name`,
        `last_assign_type`,
        `curr_change_phone_case`,
        `curr_gw_faster_case`,
        `curr_before_assign_case`,
        `curr_assign_case`,
        `un_curr_assign_case`,
        `curr_donated_change_phone_case`,
        `curr_donated_gw_faster_case`,
        `curr_donated_before_assign_case`,
        `curr_donated_assign_case`,
        `un_curr_donated_assign_case`,
        `curr_case`,
        `curr_ready_case`,
        `curr_donated_case`,
        `first_reject_case`,
        `first_reject_phone_succ`,
        `first_reject_phone_fail45`,
        `no_donate_phone_succ`,
        `no_donate_phone_fail45`,
        `create_time`,
        `update_time`,
        `is_delete`,
        `work_content_type`,
        `work_content_desc`
    </sql>

    <sql id="tableName">cf_task_case_statistics</sql>
    <sql id="cfTaskCaseStatisticsVO">
        `id`,
        `org_name`,
        `date_time`,
        `user_id`,
        `user_name`,
        `last_assign_type`,
        `curr_change_phone_case`,
        `curr_gw_faster_case`,
        `curr_before_assign_case`,
        `curr_assign_case`,
        `un_curr_assign_case`,
        `curr_donated_change_phone_case`,
        `curr_donated_gw_faster_case`,
        `curr_donated_before_assign_case`,
        `curr_donated_assign_case`,
        `un_curr_donated_assign_case`,
        `curr_case`,
        `curr_ready_case`,
        `curr_donated_case`,
        `work_content_desc`,
        `first_reject_case`,
        `first_reject_phone_succ`,
        `first_reject_phone_fail45`,
        `no_donate_phone_succ`,
        `no_donate_phone_fail45`
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`date_time`,`org_id`,`org_name`,`user_id`,`user_name`,`last_assign_type`,`work_content_type`,`work_content_desc`)
        values
        (#{dateTime},#{orgId},#{orgName},#{userId},#{userName},#{lastAssignType},#{workContentType},#{workContentDesc})
    </insert>

    <select id="queryTaskCaseStatistics" resultType="com.shuidihuzhu.cf.clewtrack.model.CfTaskCaseStatisticsVO">
        select <include refid="cfTaskCaseStatisticsVO"/> from <include refid="tableName"/>
        where is_delete=0 and date_time=#{searchModel.dateTime}
        <if test="searchModel.orgIds!=null and searchModel.orgIds.size>0">
            and org_id in <foreach collection="searchModel.orgIds" item="orgId" open="(" close=")" separator=",">#{orgId}</foreach>
        </if>
        <if test="searchModel.searchUserIds!=null and searchModel.searchUserIds.size>0">
            and user_id in <foreach collection="searchModel.searchUserIds" item="userId" open="(" close=")" separator=",">#{userId}</foreach>
        </if>
        <if test="searchModel.lastAssignType!=null and searchModel.lastAssignType>=0">
            and last_assign_type=#{searchModel.lastAssignType}
        </if>
        <if test="searchModel.workContentTypes!=null and searchModel.workContentTypes.size()>0">
            and work_content_type in <foreach collection="searchModel.workContentTypes" item="workContentType" open="(" close=")" separator=",">#{workContentType}</foreach>
        </if>
    </select>

    <select id="queryTaskCaseStatisticsGroupOrg" resultType="com.shuidihuzhu.cf.clewtrack.model.CfTaskCaseStatisticsVO">
        select <include refid="cfTaskCaseStatisticsVO"/> from <include refid="tableName"/>
        where is_delete=0 and date_time=#{searchModel.dateTime}
        <if test="searchModel.orgIds!=null and searchModel.orgIds.size>0">
            and org_id in <foreach collection="searchModel.orgIds" item="orgId" open="(" close=")" separator=",">#{orgId}</foreach>
        </if>
        <if test="searchModel.lastAssignType!=null and searchModel.lastAssignType>=0">
            and last_assign_type=#{searchModel.lastAssignType}
        </if>
        <if test="searchModel.workContentTypes!=null and searchModel.workContentTypes.size()>0">
            and work_content_type in <foreach collection="searchModel.workContentTypes" item="workContentType" open="(" close=")" separator=",">#{workContentType}</foreach>
        </if>
        <if test="searchModel.searchUserIds != null and searchModel.searchUserIds.size() > 0">
            and user_id in <foreach collection="searchModel.searchUserIds" item="userId" separator="," close=")" open="(">#{userId}</foreach>
        </if>
    </select>

    <select id="queryOrgForStatistics" resultType="com.shuidihuzhu.cf.clewtrack.model.OptionModel">
        select distinct org_id as showValue,org_name as showKey from <include refid="tableName"/>
        where date_time=#{dateTime}
        <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
            and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
        </if>
        and is_delete=0
    </select>
    <select id="queryUserForStatistics" resultType="com.shuidihuzhu.cf.clewtrack.model.OptionModel">
        select distinct user_id as showValue,user_name as showKey from <include refid="tableName"/>
        where date_time=#{dateTime}
        <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
            and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
        </if>
        and is_delete=0
    </select>

    <update id="incrCurrChangePhoneCase">
        update <include refid="tableName"/> set `curr_change_phone_case`=curr_change_phone_case+1
        where id = #{id}
    </update>
    <update id="incrCurrGwFasterCase">
        update <include refid="tableName"/> set `curr_gw_faster_case`=curr_gw_faster_case+1
        where id = #{id}
    </update>
    <update id="incrCurrBeforeAssignCase">
        update <include refid="tableName"/> set `curr_before_assign_case`=curr_before_assign_case+1
        where id = #{id}
    </update>
    <update id="incrCurrAssignCase">
        update <include refid="tableName"/> set `curr_assign_case`=curr_assign_case+1
        where id = #{id}
    </update>
    <update id="incrUnCurrAssignCase">
        update <include refid="tableName"/> set `un_curr_assign_case`=un_curr_assign_case+1
        where id = #{id}
    </update>
    <update id="incrCurrDonatedChangePhoneCase">
        update <include refid="tableName"/> set `curr_donated_change_phone_case`=curr_donated_change_phone_case+1
        where id = #{id}
    </update>
    <update id="incrCurrDonatedGwFasterCase">
        update <include refid="tableName"/> set `curr_donated_gw_faster_case`=curr_donated_gw_faster_case+1
        where id = #{id}
    </update>
    <update id="incrCurrDonatedBeforeAssignCase">
        update <include refid="tableName"/> set `curr_donated_before_assign_case`=curr_donated_before_assign_case+1
        where id = #{id}
    </update>
    <update id="incrCurrDonatedAssignCase">
        update <include refid="tableName"/> set `curr_donated_assign_case`=curr_donated_assign_case+1
        where id = #{id}
    </update>
    <update id="incrUnCurrDonatedAssignCase">
        update <include refid="tableName"/> set `un_curr_donated_assign_case`=un_curr_donated_assign_case+1
        where id = #{id}
    </update>
    <update id="incrCurrCase">
        update <include refid="tableName"/> set `curr_case`=curr_case+1
        where id = #{id}
    </update>
    <update id="incrCurrReadyCase">
        update <include refid="tableName"/> set `curr_ready_case`=curr_ready_case+1
        where id = #{id}
    </update>

    <update id="incrCurrDonatedCase">
        update <include refid="tableName"/>set `curr_donated_case`=curr_donated_case+1
        where id = #{id}
    </update>

    <update id="incrFirstRejectCase">
        update <include refid="tableName"/>set `first_reject_case`=first_reject_case+1
        where id = #{id}
    </update>
    <update id="incrFirstRejectPhoneSucc">
        update <include refid="tableName"/>set `first_reject_phone_succ`=first_reject_phone_succ+1
        where id = #{id}
    </update>
    <update id="incrFirstRejectPhoneFail45">
        update <include refid="tableName"/>set `first_reject_phone_fail45`=first_reject_phone_fail45+1
        where id = #{id}
    </update>
    <update id="incrNoDonatePhoneSucc">
        update <include refid="tableName"/>set `no_donate_phone_succ`=no_donate_phone_succ+1
        where id = #{id}
    </update>
    <update id="incrNoDonatePhoneFail45">
        update <include refid="tableName"/>set `no_donate_phone_fail45`=no_donate_phone_fail45+1
        where id = #{id}
    </update>

    <select id="getByUserIdListAndDateTimeAndLastAssignType"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfTaskCaseStatisticsDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0 and date_time=#{dateTime} and is_delete=0 and last_assign_type=#{lastAssignType}
        and user_id in <foreach collection="userIds" item="userId" open="(" close=")" separator=",">#{userId}</foreach>
    </select>
    <select id="getCfTaskCaseStatisticsDOById" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfTaskCaseStatisticsDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0 and id=#{id}
    </select>
</mapper>
