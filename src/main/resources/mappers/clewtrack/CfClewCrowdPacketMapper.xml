<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCrowdPacketDao">
    <sql id="tableName">cf_clew_crowd_packet</sql>
    <sql id="field">
        `id`,
        `crowd_packet_name`,
        `workbench_type`,
        `is_occupy`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <select id="getClewCrowdPacket" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0 and id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
         insert into <include refid="tableName"/>
         (`crowd_packet_name`, `workbench_type`, `is_occupy`) values (#{crowdPacketName}, #{workbenchType}, #{isOccupy})
    </insert>

    <update id="delete">
        update <include refid="tableName"/> set
        is_delete = 1 where id = #{id}
    </update>

    <update id="updateIsOccupy">
        update <include refid="tableName"/> set
        is_occupy = #{occupy} where id = #{id}
    </update>

    <select id="listByIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0 and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
              #{id}
        </foreach>
    </select>


    <select id="findByClewPacketName" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0 and crowd_packet_name = #{packetName}
    </select>

    <select id="countPaginationByWorkbenchType" resultType="java.lang.Long">
        select count(*)
        from <include refid="tableName"/>
        where is_delete = 0
        <if test="workbenchType != null and workbenchType >= 0">
            and workbench_type = #{workbenchType}
        </if>
    </select>


    <select id="listPaginationByWorkbenchType" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0
        <if test="workbenchType != null and workbenchType >= 0">
            and workbench_type = #{workbenchType}
        </if>
        order by id desc
        limit #{offset}, #{limit}
    </select>
    <select id="getClewCrowdPacketList" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketDO">
        select <include refid="field"/>
        from <include refid="tableName"/>
        where is_delete = 0
        order by id desc
    </select>

</mapper>
