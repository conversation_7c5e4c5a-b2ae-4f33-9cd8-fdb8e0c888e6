<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTaskStageRecordDAO">
    <sql id="baseResult">
        `id`,
        `task_id`,
        `task_stage`,
        `user_id`,
        `status`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="tableName">cf_clew_task_stage_record</sql>

    <select id="findByTaskIdAndTaskStage" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskStageRecordDO">
        SELECT <include refid="baseResult"/> FROM <include refid="tableName"/>
        WHERE task_id = #{taskId} AND task_stage = #{taskStage} AND is_delete = 0
        limit 1
    </select>

    <select id="getByTaskId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskStageRecordDO">
        SELECT <include refid="baseResult"/> FROM <include refid="tableName"/>
        WHERE task_id = #{taskId} AND is_delete = 0
    </select>

    <update id="updateStatus">
        update <include refid="tableName"/>
        set status = #{status}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskStageRecordDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cf_clew_task_stage_record (task_stage, task_id, user_id)
        VALUES (#{taskStage}, #{taskId}, #{userId})
    </insert>

    <select id="getUnEndStageRecordsByUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskStageRecordDO">
        SELECT <include refid="baseResult"/> FROM <include refid="tableName"/>
        WHERE user_id = #{userId} AND is_delete = 0 AND status = 0
        and create_time between #{startTime} and #{endTime}
    </select>

</mapper>