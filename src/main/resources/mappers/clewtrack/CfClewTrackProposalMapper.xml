<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTrackProposalDao">
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackProposalDO"
            keyProperty="id" useGeneratedKeys="true">
        insert into cf_clew_track_proposal
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="phone!=null and phone !=''">
                phone,
            </if>
            <if test="proposal!=null and proposal!=''">
                proposal,
            </if>
            <if test="type!=null">
                `type`,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="phone!=null and phone !=''">
                #{phone},
            </if>
            <if test="proposal!=null and proposal!=''">
                #{proposal},
            </if>
            <if test="type!=null">
                #{type},
            </if>
        </trim>
    </insert>
</mapper>
