<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTransruleDao">

    <sql id="baseResult">
        rt.`id`,
        rt.`collection_id`,
        rt.`rule`,
        rt.`case_tag`,
        rt.`clew_tag`,
        rt.`result`,
        rt.`priority`,
        rt.`operator`,
        rt.`is_enable`,
        rt.`is_delete`,
        rt.`create_time`,
        rt.`update_time`,
        rt.`link_name`,
        rt.`work_bench_type`,
        rt.`work_content_type`
    </sql>

    <sql id="tableName">cf_clew_rule_trans</sql>

    <select id="getClewTransrule" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewTransruleModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/> rt join cf_clew_rule_collection rc on rt.collection_id = rc.id
        where rc.is_delete=0 and rt.is_delete=0 and rt.is_enable=1
        and sysdate() > rc.start_time and rc.end_time > sysdate()
        and rt.collection_id = #{collectionId}
        ORDER BY priority asc, update_time desc
    </select>

    <select id="getCaseTagWithId" resultType="java.lang.String">
        SELECT case_tag
        from <include refid="tableName"/>
        where is_delete=0 and is_enable=1
        and id = #{id}
    </select>
    <select id="getClewTransruleByCondition" parameterType="com.shuidihuzhu.cf.clewtrack.param.ClewTransruleParam"
            resultType="com.shuidihuzhu.cf.clewtrack.model.ClewTransruleModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/> rt join cf_clew_rule_collection rc on rt.collection_id = rc.id
        where rc.is_delete=0 and rt.is_delete=0
        and sysdate() > rc.start_time and rc.end_time > sysdate()
        and rt.collection_id = #{collectionId}
        <if test="isEnable != null">
            and is_enable = #{isEnable}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and (
                rule regexp ( CONCAT('\\(',#{channelCode}, '\\|') ) or rule regexp ( CONCAT('\\(',#{channelCode}, '\\)') )
                or rule regexp ( CONCAT('\\|',#{channelCode}, '\\|') ) or rule regexp ( CONCAT('\\|',#{channelCode}, '\\)') )
                )
        </if>
        <if test="tagCode != null and tagCode != ''">
            and rt.rule regexp #{tagCode}
        </if>
        <if test="result != null and result != ''">
            and result = #{result}
        </if>
        <if test="workBenchType != null ">
            and work_bench_type = #{workBenchType}
        </if>
        <if test="workContentType != null">
            and work_content_type = #{workContentType}
        </if>
        ORDER BY id asc
        limit #{offset},#{pageSize}
    </select>
    <select id="getClewTransruleCountByCondition" parameterType="com.shuidihuzhu.cf.clewtrack.param.ClewTransruleParam"
            resultType="java.lang.Integer">
        SELECT count(1)
        from <include refid="tableName"/> rt join cf_clew_rule_collection rc on rt.collection_id = rc.id
        where rc.is_delete=0 and rt.is_delete=0
        and sysdate() > rc.start_time and rc.end_time > sysdate()
        and rt.collection_id = #{collectionId}
        <if test="isEnable != null">
            and is_enable = #{isEnable}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and (
            rule regexp ( CONCAT('\\(',#{channelCode}, '\\|') ) or rule regexp ( CONCAT('\\(',#{channelCode}, '\\)') )
            or rule regexp ( CONCAT('\\|',#{channelCode}, '\\|') ) or rule regexp ( CONCAT('\\|',#{channelCode}, '\\)') )
            )
        </if>
        <if test="tagCode != null and tagCode != ''">
            and rt.rule like CONCAT('%', #{tagCode} , '%')
        </if>
        <if test="result != null and result != ''">
            and result = #{result}
        </if>
        <if test="workBenchType != null ">
            and work_bench_type = #{workBenchType}
        </if>
        <if test="workContentType != null">
            and (
            rule regexp ( CONCAT('\\(',#{workContentType}, '\\|') ) or rule regexp ( CONCAT('\\(',#{workContentType}, '\\)') )
            or rule regexp ( CONCAT('\\|',#{workContentType}, '\\|') ) or rule regexp ( CONCAT('\\|',#{workContentType}, '\\)') )
            )
        </if>
    </select>
    <insert id="insertClewTransrule" parameterType="com.shuidihuzhu.cf.clewtrack.param.ClewTransruleParam"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`collection_id`,
        `rule`,
        `case_tag`,
        `result`,
        `priority`,
        `operator`,
        `is_enable`,
        `create_time`,
        `update_time`
        <if test="clewTag != null">
            ,`clew_tag`
        </if>
        <if test="workBenchType != null ">
            ,work_bench_type
        </if>
        <if test="linkName != null and linkName != ''">
            ,link_name
        </if>
        <if test="workContentType != null">
            ,work_content_type
        </if>
        )
        values
        (#{collectionId},
        #{rule},
        #{caseTag},
        #{result},
        #{priority},
        #{operator},
        #{isEnable},
        sysdate(),
        sysdate()
        <if test="clewTag != null">
            ,#{clewTag}
        </if>
        <if test="workBenchType != null ">
            ,#{workBenchType}
        </if>
        <if test="linkName != null and linkName != ''">
            ,#{linkName}
        </if>
        <if test="workContentType != null">
            ,#{workContentType}
        </if>
        )
    </insert>
    <update id="updateClewTransrule" parameterType="com.shuidihuzhu.cf.clewtrack.param.ClewTransruleParam">
        update <include refid="tableName"/>
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId},
            </if>
            <if test="rule != null and rule != ''">
                rule = #{rule},
            </if>
            <if test="caseTag != null and caseTag != ''">
                case_tag = #{caseTag},
            </if>
            <if test="clewTag != null and clewTag != ''">
                clew_tag = #{clewTag},
            </if>
            <if test="result != null and result != ''">
                result = #{result},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="isEnable != null">
                is_enable = #{isEnable},
            </if>
            <if test="workBenchType != null ">
                work_bench_type = #{workBenchType},
            </if>
            <if test="linkName != null and linkName != ''">
                link_name = #{linkName},
            </if>
            <if test="workContentType != null">
                work_content_type = #{workContentType},
            </if>
            update_time = sysdate(),
        </set>
        where id = #{id}
    </update>
    <select id="selectClewTransruleByTag" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewTransruleModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/> rt join cf_clew_rule_collection rc on rt.collection_id = rc.id
        where rc.is_delete=0 and rt.is_delete=0 and rt.is_enable=1
        and sysdate() > rc.start_time and rc.end_time > sysdate()
        and rt.rule like CONCAT('%', #{tagStr} , '%')
    </select>

    <select id="getClewTransruleByConditionDesc"
            resultType="com.shuidihuzhu.cf.clewtrack.model.ClewTransruleModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/> rt join cf_clew_rule_collection rc on rt.collection_id = rc.id
        where rc.is_delete=0 and rt.is_delete=0
        and sysdate() > rc.start_time and rc.end_time > sysdate()
        and rt.collection_id = #{collectionId}
        <if test="isEnable != null">
            and is_enable = #{isEnable}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and (
            rule regexp ( CONCAT('\\(',#{channelCode}, '\\|') ) or rule regexp ( CONCAT('\\(',#{channelCode}, '\\)') )
            or rule regexp ( CONCAT('\\|',#{channelCode}, '\\|') ) or rule regexp ( CONCAT('\\|',#{channelCode}, '\\)') )
            )
        </if>
        <if test="tagCode != null and tagCode != ''">
            and rt.rule regexp #{tagCode}
        </if>
        <if test="result != null and result != ''">
            and result = #{result}
        </if>
        <if test="workBenchType != null ">
            and work_bench_type = #{workBenchType}
        </if>
        <if test="workContentType != null">
            and (
            rule regexp ( CONCAT('\\(',#{workContentType}, '\\|') ) or rule regexp ( CONCAT('\\(',#{workContentType}, '\\)') )
            or rule regexp ( CONCAT('\\|',#{workContentType}, '\\|') ) or rule regexp ( CONCAT('\\|',#{workContentType}, '\\)') )
            )
        </if>
        ORDER BY id desc
        limit #{offset},#{pageSize}
    </select>

</mapper>
