<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCallRobotRecordsDao">

  <sql id="tableFile">
    id, uuid, task_id, encrypt_phone, record_id, `status`, task_state, asr_int, asr_desc, 
    start_time, ans_time, end_time, ans_time_seconds, server_num, task_cusresult, custom_params, 
    insert_time, biz_name, is_delete, create_time, update_time, data_type, is_delay,delay_time
  </sql>
  <sql id="tableName">
    cf_clew_call_robot_records
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRobotRecordsDO">
    select 
    <include refid="tableFile" />
    from <include refid="tableName"/>
    where id = #{id}
  </select>
  <select id="selectByUuid" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRobotRecordsDO">
    select <include refid="tableFile"/>
    from <include refid="tableName"/>
    where
    uuid = #{uuid}
    limit 1
  </select>
    <select id="selectByTaskIdAndPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRobotRecordsDO">
        select
        <include refid="tableFile"/>
        from
        <include refid="tableName"/>
        where
        encrypt_phone = #{phone}
        and task_id = #{taskId}
        <if test="dataType!= null">
            and data_type = #{dataType}
        </if>
    </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRobotRecordsDO" useGeneratedKeys="true">
    insert into cf_clew_call_robot_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uuid != null">
        uuid,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="encryptPhone != null">
        encrypt_phone,
      </if>
      <if test="recordId != null">
        record_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="taskState != null">
        task_state,
      </if>
      <if test="asrInt != null">
        asr_int,
      </if>
      <if test="asrDesc != null">
        asr_desc,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="ansTime != null">
        ans_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="ansTimeSeconds != null">
        ans_time_seconds,
      </if>
      <if test="serverNum != null">
        server_num,
      </if>
      <if test="taskCusresult != null">
        task_cusresult,
      </if>
      <if test="customParams != null">
        custom_params,
      </if>
      <if test="insertTime != null">
        insert_time,
      </if>
      <if test="bizName != null">
        biz_name,
      </if>
      <if test="isDelete != null">
          is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="isDelay != null">
        is_delay,
      </if>
      <if test="delayTime != null">
        delay_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uuid != null">
        #{uuid},
      </if>
      <if test="taskId != null">
        #{taskId},
      </if>
      <if test="encryptPhone != null">
        #{encryptPhone},
      </if>
      <if test="recordId != null">
        #{recordId},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="taskState != null">
        #{taskState},
      </if>
      <if test="asrInt != null">
        #{asrInt},
      </if>
      <if test="asrDesc != null">
        #{asrDesc},
      </if>
      <if test="startTime != null">
        #{startTime},
      </if>
      <if test="ansTime != null">
        #{ansTime},
      </if>
      <if test="endTime != null">
        #{endTime},
      </if>
      <if test="ansTimeSeconds != null">
        #{ansTimeSeconds},
      </if>
      <if test="serverNum != null">
        #{serverNum},
      </if>
      <if test="taskCusresult != null">
        #{taskCusresult},
      </if>
      <if test="customParams != null">
        #{customParams},
      </if>
      <if test="insertTime != null">
        #{insertTime},
      </if>
      <if test="bizName != null">
        #{bizName},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="dataType != null">
        #{dataType},
      </if>
      <if test="isDelay != null">
        #{isDelay},
      </if>
      <if test="delayTime != null">
        #{delayTime},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRobotRecordsDO">
    update <include refid="tableName"/>
    <set>
      <if test="uuid != null">
        uuid = #{uuid},
      </if>
      <if test="taskId != null">
        task_id = #{taskId},
      </if>
      <if test="encryptPhone != null">
        encrypt_phone = #{encryptPhone},
      </if>
      <if test="recordId != null">
        record_id = #{recordId},
      </if>
      <if test="status != null">
        `status` = #{status},
      </if>
      <if test="taskState != null">
        task_state = #{taskState},
      </if>
      <if test="asrInt != null">
        asr_int = #{asrInt},
      </if>
      <if test="asrDesc != null">
        asr_desc = #{asrDesc},
      </if>
      <if test="startTime != null">
        start_time = #{startTime},
      </if>
      <if test="ansTime != null">
        ans_time = #{ansTime},
      </if>
      <if test="endTime != null">
        end_time = #{endTime},
      </if>
      <if test="ansTimeSeconds != null">
        ans_time_seconds = #{ansTimeSeconds},
      </if>
      <if test="serverNum != null">
        server_num = #{serverNum},
      </if>
      <if test="taskCusresult != null">
        task_cusresult = #{taskCusresult},
      </if>
      <if test="customParams != null">
        custom_params = #{customParams},
      </if>
      <if test="insertTime != null">
        insert_time = #{insertTime},
      </if>
      <if test="bizName != null">
        biz_name = #{bizName},
      </if>
      <if test="isDelete != null">
          is_delete = #{isDelete},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="dataType != null">
        data_type = #{dataType},
      </if>
      <if test="isDelay != null">
        is_delay = #{isDelay},
      </if>
      <if test="delayTime != null">
        delay_time = #{delayTime},
      </if>
    </set>
    where id = #{id}
  </update>

</mapper>