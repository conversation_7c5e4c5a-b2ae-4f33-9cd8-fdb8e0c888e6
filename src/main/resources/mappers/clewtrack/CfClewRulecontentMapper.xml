<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewRulecontentDao">

    <sql id="baseResult">
        rt.`id`,
        rt.`collection_id`,
        rt.`field_name`,
        rt.`priority`,
        rt.`describe`,
        rt.`operator`,
        rt.`create_time`,
        rt.`update_time`
    </sql>

    <sql id="tableName">cf_clew_rule_content</sql>

    <select id="getClewRulecontent" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewRulecontentModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/> rt join cf_clew_rule_collection rc on rt.collection_id = rc.id
        where rc.is_delete=0 and rt.is_delete=0
        and sysdate() > rc.start_time and rc.end_time > sysdate()
        and rt.collection_id = #{collectionId}
        ORDER BY priority asc
    </select>

</mapper>
