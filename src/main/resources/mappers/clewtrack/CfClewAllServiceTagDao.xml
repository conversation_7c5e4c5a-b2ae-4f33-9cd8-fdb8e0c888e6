<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewAllServiceTagDao">

    <sql id="tableName">`cf_clew_all_service_tag`</sql>

    <sql id="baseResult">
        `id`,
        `task_id`,
        `task_type`,
        `phone`,
        `clew_id`,
        `disease_name`,
        `channel`,
        `user_id`,
        `clew_type`,
        `assign_time`,
        `assign_type`,
        `clew_level`,
        `assign_limit`,
        `clew_tag_by_content`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">
                `task_id`,
            </if>
            <if test="taskType != null and taskType != ''">
                `task_type`,
            </if>
            <if test="phone != null and phone != ''">
                `phone`,
            </if>
            <if test="clewId != null and clewId != ''">
                `clew_id`,
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                `disease_name`,
            </if>
            <if test="channel != null and channel != ''">
                `channel`,
            </if>
            <if test="userId != null and userId != ''">
                `user_id`,
            </if>
            <if test="clewType != null and clewType != ''">
                `clew_type`,
            </if>
            <if test="assignTime != null">
                `assign_time`,
            </if>
            <if test="assignType != null">
                `assign_type`,
            </if>
            <if test="clewLevel != null">
                `clew_level`,
            </if>
            <if test="clewTagByContent != null">
                `clew_tag_by_content`,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">
                #{taskId},
            </if>
            <if test="taskType != null and taskType != ''">
                #{taskType},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="clewId != null and clewId != ''">
                #{clewId},
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                #{diseaseName},
            </if>
            <if test="channel != null and channel != ''">
                #{channel},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="clewType != null and clewType != ''">
                #{clewType},
            </if>
            <if test="assignTime != null">
                #{assignTime},
            </if>
            <if test="assignType != null">
                #{assignType},
            </if>
            <if test="clewLevel != null">
                #{clewLevel},
            </if>
            <if test="clewTagByContent != null">
                #{clewTagByContent},
            </if>
        </trim>
    </insert>

    <select id="getAllServiceTaskByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        select
        <include refid="baseResult"/>
        from
        <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIds" open="(" close=")" separator="," item="clewId">
            #{clewId}
        </foreach>
        and is_delete = 0
    </select>

    <select id="getAllServiceTaskByTaskIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        select
        <include refid="baseResult"/>
        from
        <include refid="tableName"/>
        where task_id in
        <foreach collection="taskIds" open="(" close=")" separator="," item="taskId">
            #{taskId}
        </foreach>
        and is_delete = 0
    </select>

    <select id="getServiceTagDo" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id = #{id}
        and is_delete = 0
    </select>

    <select id="getAllServiceTagByPhone" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        select
        <include refid="baseResult"/>
        from
        <include refid="tableName"/>
        where phone = #{phone}
        and is_delete = 0
    </select>

    <update id="deleteAllServiceTag">
        update
        <include refid="tableName"/>
        set is_delete = 1
        where clew_id in
        <foreach collection="clewIds" open="(" close=")" separator="," item="clewId">
            #{clewId}
        </foreach>
    </update>

    <update id="updateClewType">
        update
        <include refid="tableName"/>
        set clew_type = #{clewType}
        where id = #{id}
    </update>


    <update id="updateTaskIdByClewId">
        update
        <include refid="tableName"/>
        <set>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="assignLimit != null">
                assign_limit = #{assignLimit},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
        </set>
        where clew_id = #{clewId} and task_type = #{taskType} and is_delete = 0
    </update>

    <select id="getClewInfoByParam" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        select
        <include refid="baseResult"/>
        from
        <include refid="tableName"/>
        <where>
            is_delete = 0
            and task_type = 3
            <if test="queryParam.phone != null and queryParam.phone != ''">
                and phone = #{queryParam.phone}
            </if>
            <if test="queryParam.clewType != null">
                and clew_type = #{queryParam.clewType}
            </if>
            <if test="queryParam.startTime != null and queryParam.startTime != '' and queryParam.endTime != null and queryParam.endTime != ''">
                and create_time between #{queryParam.startTime} and #{queryParam.endTime}
            </if>
            <if test="queryParam.assignStartTime != null and queryParam.assignStartTime != '' and queryParam.assignEndTime != null and queryParam.assignEndTime != ''">
                and assign_time between #{queryParam.assignStartTime} and #{queryParam.assignEndTime}
            </if>
            <if test="queryParam.assignType != null">
                and assign_type = #{queryParam.assignType}
            </if>
            <if test="queryParam.assignLimit != null">
                and assign_limit = #{queryParam.assignLimit}
            </if>
            <if test="queryParam.clewLevel != null and queryParam.clewLevel != ''">
                and clew_level = #{queryParam.clewLevel}
            </if>
        </where>
        order by create_time desc
        limit #{queryParam.offset},#{queryParam.pageSize}
    </select>

    <select id="getClewCountByParam" resultType="java.lang.Integer">
        select count(*)
        from
        <include refid="tableName"/>
        <where>
            is_delete = 0
            and task_type = 3
            <if test="queryParam.phone != null and queryParam.phone != ''">
                and phone = #{queryParam.phone}
            </if>
            <if test="queryParam.clewType != null">
                and clew_type = #{queryParam.clewType}
            </if>
            <if test="queryParam.startTime != null and queryParam.startTime != '' and queryParam.endTime != null and queryParam.endTime != ''">
                and create_time between #{queryParam.startTime} and #{queryParam.endTime}
            </if>
            <if test="queryParam.assignStartTime != null and queryParam.assignStartTime != '' and queryParam.assignEndTime != null and queryParam.assignEndTime != ''">
                and assign_time between #{queryParam.assignStartTime} and #{queryParam.assignEndTime}
            </if>
            <if test="queryParam.assignType != null">
                and assign_type = #{queryParam.assignType}
            </if>
            <if test="queryParam.assignLimit != null">
                and assign_limit = #{queryParam.assignLimit}
            </if>
            <if test="queryParam.clewLevel != null and queryParam.clewLevel != ''">
                and clew_level = #{queryParam.clewLevel}
            </if>
        </where>
    </select>

    <update id="updateClewTaskStatus">
        update <include refid="tableName"/>
        <set>
            <if test="assignType != null">
                assign_type = #{assignType},
            </if>
            <if test="assignTime != null">
                assign_time = #{assignTime},
            </if>
        </set>
        where task_id = #{taskId}
    </update>

    <select id="listNeedAssignTaskByParam" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        <where>
            is_delete = 0
            and create_time between #{param.startDate} and #{param.endDate}
            <if test="param.taskIds != null and param.taskIds.size() > 0">
                and task_id in
                <foreach collection="param.taskIds" open="(" close=")" separator="," item="taskId">
                    #{taskId}
                </foreach>
            </if>
            <if test="param.taskType != null">
                and task_type = #{param.taskType}
            </if>
            <if test="param.assignLimit != null">
                and assign_limit = #{param.assignLimit}
            </if>
            <if test="param.clewGrade != null and param.clewGrade != ''">
                and clew_level = #{param.clewGrade}
            </if>
            <if test="param.clewChannelList != null and param.clewChannelList.size() > 0">
                and channel in
                <foreach collection="param.clewChannelList" open="(" close=")" separator="," item="clewChannel">
                    #{clewChannel}
                </foreach>
            </if>
            <if test="param.assignType != null">
                and assign_type = #{param.assignType}
            </if>
        </where>
    </select>

    <update id="reOpenTask">
        update <include refid="tableName"/>
        set is_delete = 0
        where task_id = #{taskId}
    </update>

    <select id="getAllServiceDelTaskByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAllServiceTagDO">
        select
        <include refid="baseResult"/>
        from
        <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIds" open="(" close=")" separator="," item="clewId">
            #{clewId}
        </foreach>
    </select>

</mapper>
