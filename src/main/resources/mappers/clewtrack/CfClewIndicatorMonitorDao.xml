<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.ICfClewIndicatorMonitorDao">

    <sql id="baseResult">
        `id`,
        `day_key`,
        `biz_type`,
        `donated_assign_ratio_target`,
        `donated_case_target`,
        `assignee_mis`,
        `assignee_name`,
        `assignor_mis`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <sql id="tableName">cf_clew_indicator_monitor</sql>

    <insert id="batchInsert">
        insert into <include refid="tableName"/>
        (`day_key`,`biz_type`,`donated_assign_ratio_target`,`donated_case_target`,
        `assignee_mis`,`assignee_name`,`assignor_mis`)
        values
        <foreach collection="insertList" item="item"  separator=",">
            (#{item.dayKey} ,#{item.bizType} ,#{item.donatedAssignRatioTarget} ,
            #{item.donatedCaseTarget} ,#{item.assigneeMis} ,#{item.assigneeName},#{item.assignorMis} )
        </foreach>
    </insert>

    <update id="batchUpdate">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="donated_assign_ratio_target =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.donatedAssignRatioTarget !=null">
                        when id=#{item.id} then #{item.donatedAssignRatioTarget}
                    </if>
                </foreach>
            </trim>
            <trim prefix="donated_case_target =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.donatedCaseTarget !=null">
                        when id=#{item.id} then #{item.donatedCaseTarget}
                    </if>
                </foreach>
            </trim>
            <trim prefix="assignor_mis =case" suffix="end,">
                <foreach collection="updateList" item="item" index="index">
                    <if test="item.assignorMis !=null">
                        when id=#{item.id} then #{item.assignorMis}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="updateList" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="listIndicatorItem" resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfClewIndicatorMonitorVO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0 and day_key = #{dayKey} and biz_type = #{bizType}
        <if test="assigneeMisList != null and assigneeMisList.size() != 0">
            and assignee_mis in
            <foreach collection="assigneeMisList" item="assigneeMis" open="(" separator="," close=")">
                #{assigneeMis}
            </foreach>
        </if>
    </select>

    <select id="listIndicatorByDayKeyAndBiztype"
            resultType="com.shuidihuzhu.cf.clewtrack.model.vo.CfClewIndicatorMonitorVO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0 and day_key = #{dayKey} and biz_type = #{bizType}
    </select>

</mapper>
