<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCrowdPacketUserRelDao">
    <sql id="tableName">cf_clew_crowd_packet_user_rel</sql>
    <sql id="field">
        `id`,
        `crowd_packet_id`,
        `user_id`,
        `user_name`,
        `task_upper_limit`,
        `current_day_assign_count`,
        `current_day_assign_count_details`,
        `last_assign_time`,
        `workbench_type`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <insert id="banchInsert">
        insert into <include refid="tableName"/>
        (crowd_packet_id, user_id, user_name, task_upper_limit, workbench_type)
        values
        <foreach collection="userRelDOList" item="userRelDO" separator=",">
           (#{userRelDO.crowdPacketId}, #{userRelDO.userId}, #{userRelDO.userName},
            #{userRelDO.taskUpperLimit}, #{userRelDO.workbenchType})
        </foreach>
    </insert>

    <update id="batchDelete">
        update <include refid="tableName"/>
        set is_delete = 1 where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
              #{id}
        </foreach>
    </update>

    <update id="updateUpperLimit">
        update <include refid="tableName"/>
        set task_upper_limit = #{upperLimit}
        where id = #{id}
    </update>

    <select id="countPagination" resultType="java.lang.Long">
        select count(*) from <include refid="tableName"/>
        where is_delete = 0 and user_name = #{volunteerName}
        <if test="workbenchType != null and workbenchType >= 0">
            and workbench_type = #{workbenchType}
        </if>
    </select>

    <select id="listPagination" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketUserRelDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and user_name = #{volunteerName}
        <if test="workbenchType != null and workbenchType >= 0">
            and workbench_type = #{workbenchType}
        </if>
        order by id desc
        limit #{offset}, #{limit}
    </select>

    <select id="listByPacketId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketUserRelDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and crowd_packet_id = #{packetId}
    </select>
    <select id="getPreFilterCanAssignUserRelDO"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketUserRelDO">
        select a.*
        from <include refid="tableName"/> a left join cf_clew_track_user_info b on a.user_id = b.user_id and b.tracker_type = #{trackerType}
        where a.is_delete = 0 and a.crowd_packet_id = #{crowdPacketId} and a.task_upper_limit > a.current_day_assign_count and b.is_delete=0
        order by a.current_day_assign_count,b.is_top desc,a.last_assign_time
    </select>
    <update id="updateCurrentDayAssign">
        update <include refid="tableName"/>
        set `current_day_assign_count` = #{currentDayAssignCount},
        `current_day_assign_count_details` = #{currentDayAssignCountDetails},
        `last_assign_time` = #{lastAssignTime}
        where id = #{id}
    </update>
    <update id="resetClewBaseLayerCounter">
        update <include refid="tableName"/>
        set `current_day_assign_count` = 0,
        `current_day_assign_count_details` = ''
        where is_delete = 0 and id in <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>
    <select id="getNeedResetClewBaseLayerCounter"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCrowdPacketUserRelDO">
        select <include refid="field"/> from <include refid="tableName"/>
        where is_delete = 0 and current_day_assign_count>0
        limit 100
    </select>


</mapper>
