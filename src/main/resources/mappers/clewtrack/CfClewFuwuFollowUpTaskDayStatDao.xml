<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewFuwuFollowUpTaskDayStatDao">

    <sql id="insert_field">
        day_key,
        org_name,
        user_name,
        user_id,
        online_status,
        online_status_desc,

        handle_count_d0,
        un_handle_count_d0,
        d0_follow_phone_handle_ratio,
        un_handle0_count_d0,
        un_handle2_count_d0,
        follow_group_count_d0,
        follow_phone_count_d0,

        handle_count_d1,
        un_handle_count_d1,
        d1_follow_phone_handle_ratio,
        un_handle0_count_d1,
        un_handle2_count_d1,
        follow_phone_count_d1,
        follow_group_count_d1,

        handle_count_d2,
        un_handle_count_d2,
        d2_follow_phone_handle_ratio,
        un_handle0_count_d2,
        un_handle2_count_d2,
        follow_phone_count_d2,
        follow_group_count_d2,

        handle_count_d3,
        un_handle_count_d3,
        d3_follow_phone_handle_ratio,
        un_handle0_count_d3,
        un_handle2_count_d3,
        follow_phone_count_d3,
        follow_group_count_d3,

        un_handle_count_hist,
        follow_gene_count,
        follow_handle_count,
        follow_manu_handle_count,
        follow_phone_count,
        follow_group_count,
        follow_handle_ratio,
        follow_manu_handle_ratio,
        follow_phone_handle_ratio

    </sql>

    <sql id="tableName">cf_clew_fuwu_follow_up_task_day_stat</sql>

    <insert id="batchInsert" >
        insert into <include refid="tableName"/>
        (<include refid="insert_field"/>)
        values
        <foreach collection="list" item="item" separator="," >
        (
            #{dayKey},
            <if test="item.orgName !=null ">
                #{item.orgName} ,
            </if>
            <if test="item.orgName == null ">
                "",
            </if>
            <if test="item.userName !=null ">
                #{item.userName} ,
            </if>
            <if test="item.userName == null ">
                "",
            </if>
            <if test="item.userId !=null ">
                #{item.userId} ,
            </if>
            <if test="item.userId == null ">
                "",
            </if>
            <if test="item.onlineStatus !=null ">
                #{item.onlineStatus} ,
            </if>
            <if test="item.onlineStatus == null ">
                -1,
            </if>
            <if test="item.onlineStatusDesc !=null ">
                #{item.onlineStatusDesc} ,
            </if>
            <if test="item.onlineStatusDesc == null ">
                "",
            </if>
            #{item.handleCountD0} ,
            #{item.unHandleCountD0} ,
            <if test="item.d0FollowPhoneHandleRatio !=null ">
                #{item.d0FollowPhoneHandleRatio} ,
            </if>
            <if test="item.d0FollowPhoneHandleRatio == null ">
                "",
            </if>
            #{item.unHandle0CountD0} ,
            #{item.unHandle2CountD0} ,
            #{item.followGroupCountD0} ,
            #{item.followPhoneCountD0} ,


            #{item.handleCountD1} ,
            #{item.unHandleCountD1} ,
            <if test="item.d1FollowPhoneHandleRatio !=null ">
                #{item.d1FollowPhoneHandleRatio} ,
            </if>
            <if test="item.d1FollowPhoneHandleRatio == null ">
                "",
            </if>
            #{item.unHandle0CountD1} ,
            #{item.unHandle2CountD1} ,
            #{item.followGroupCountD1} ,
            #{item.followPhoneCountD1} ,

            #{item.handleCountD2} ,
            #{item.unHandleCountD2} ,
            <if test="item.d2FollowPhoneHandleRatio !=null ">
                #{item.d1FollowPhoneHandleRatio} ,
            </if>
            <if test="item.d2FollowPhoneHandleRatio == null ">
                "",
            </if>
            #{item.unHandle0CountD2} ,
            #{item.unHandle2CountD2} ,
            #{item.followGroupCountD2} ,
            #{item.followPhoneCountD2} ,

            #{item.handleCountD3} ,
            #{item.unHandleCountD3} ,
            <if test="item.d3FollowPhoneHandleRatio !=null ">
                #{item.d1FollowPhoneHandleRatio} ,
            </if>
            <if test="item.d3FollowPhoneHandleRatio == null ">
                "",
            </if>
            #{item.unHandle0CountD3} ,
            #{item.unHandle2CountD3} ,
            #{item.followGroupCountD3} ,
            #{item.followPhoneCountD3} ,

            #{item.unHandleCountHist} ,
            #{item.followGeneCount} ,
            #{item.followHandleCount} ,
            #{item.followManuHandleCount} ,
            #{item.followPhoneCount} ,
            #{item.followGroupCount} ,
            <if test="item.followHandleRatio !=null ">
                #{item.followHandleRatio} ,
            </if>
            <if test="item.followHandleRatio == null ">
                "",
            </if>
            <if test="item.followManuHandleRatio !=null ">
                #{item.followHandleRatio} ,
            </if>
            <if test="item.followManuHandleRatio == null ">
                "",
            </if>
            <if test="item.followPhoneHandleRatio !=null ">
                #{item.followHandleRatio}
            </if>
            <if test="item.followPhoneHandleRatio == null ">
                ""
            </if>
        )
        </foreach>
    </insert>

</mapper>