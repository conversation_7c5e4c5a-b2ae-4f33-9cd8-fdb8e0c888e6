<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewAnnotationRecordsDao">

    <sql id="tableFiled">
    id, task_id, annotator, annotator_mis, be_annotator, be_annotator_mis, encrypt_phone,
    workbench_type, question_type, `status`, org_name, remark, is_delete, create_time,
    update_time,org_id,expect_complete_time
    </sql>
    <sql id="tableName">
        cf_clew_annotation_records
    </sql>

    <select id="selectByPrimaryKey" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnotationRecordsDO">
        select
        <include refid="tableFiled"/>
        from cf_clew_annotation_records
        where id = #{id}
    </select>

    <select id="selectAnnotationRecordsPageByParam"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnotationRecordsDO">
        select
        <include refid="tableFiled"/>
        from
        <include refid="tableName"/>
        where
        is_delete=0
        <if test="param.beAnnotatorMis != null and param.beAnnotatorMis !=''">
            and be_annotator_mis = #{param.beAnnotatorMis}
        </if>
        <if test="param.annotatorMis != null and param.annotatorMis != ''">
            and annotator_mis = #{param.annotatorMis}
        </if>
        <if test="param.encryptPhone != null and param.encryptPhone !=''">
            and encrypt_phone = #{param.encryptPhone}
        </if>
        <if test="param.workbenchType != null">
            and workbench_type = #{param.workbenchType}
        </if>
        <if test="param.startTime != null">
            and create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.taskId != null and param.taskId !=''">
            and task_id = #{param.taskId}
        </if>
        <if test="param.clewMis != null and param.clewMis !=''">
            and be_annotator_mis = #{param.clewMis}
        </if>
        <if test="param.orgId != null and param.orgId !=''">
            and org_id = #{param.orgId}
        </if>
        <if test="param.outerBindInnerOrgIdList != null and param.outerBindInnerOrgIdList.size()>0">
            and ( org_id in
            <foreach collection="param.outerBindInnerOrgIdList" index="index" item="item" close=")" open="("
                     separator=",">
                #{item}
            </foreach>
            or annotator_mis = #{param.clewUserId}
            )
        </if>
        <if test="param.annotatorMisList!= null and param.annotatorMisList.size()>0">
            and annotator_mis in
            <foreach collection="param.annotatorMisList" index="index" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.beAnnotatorMisList!= null and param.beAnnotatorMisList.size()>0">
            and be_annotator_mis in
            <foreach collection="param.beAnnotatorMisList" index="index" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        order by create_time desc
        limit #{offSet},#{param.pageSize}
    </select>

    <select id="selectAnnotationRecordsByParam"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnotationRecordsDO">
        select
        <include refid="tableFiled"/>
        from
        <include refid="tableName"/>
        where
        is_delete=0
        <if test="param.beAnnotatorMis != null and param.beAnnotatorMis !=''">
            and be_annotator_mis = #{param.beAnnotatorMis}
        </if>
        <if test="param.annotatorMis != null and param.annotatorMis !='' ">
            and annotator_mis = #{param.annotatorMis}
        </if>
        <if test="param.encryptPhone != null and param.encryptPhone !=''">
            and encrypt_phone = #{param.encryptPhone}
        </if>
        <if test="param.workbenchType != null">
            and workbench_type = #{param.workbenchType}
        </if>
        <if test="param.startTime != null">
            and create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.taskId != null and param.taskId !=''">
            and task_id = #{param.taskId}
        </if>
        <if test="param.clewMis != null and param.clewMis !=''">
            and be_annotator_mis = #{param.clewMis}
        </if>
        <if test="param.orgId != null and param.orgId !=''">
            and org_id = #{param.orgId}
        </if>
        <if test="param.outerBindInnerOrgIdList != null and param.outerBindInnerOrgIdList.size()>0">
            and ( org_id in
            <foreach collection="param.outerBindInnerOrgIdList" index="index" item="item" close=")" open="("
                     separator=",">
                #{item}
            </foreach>
            or annotator_mis = #{param.clewUserId}
            )
        </if>
        <if test="param.annotatorMisList!= null and param.annotatorMisList.size()>0">
            and annotator_mis in
            <foreach collection="param.annotatorMisList" index="index" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.beAnnotatorMisList!= null and param.beAnnotatorMisList.size()>0">
            and be_annotator_mis in
            <foreach collection="param.beAnnotatorMisList" index="index" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        order by create_time desc
    </select>
    <select id="selectAnnotationRecordsCountByParam" resultType="java.lang.Integer">
        select
        count(*)
        from
        <include refid="tableName"/>
        where
        is_delete=0
        <if test="param.beAnnotatorMis != null and param.beAnnotatorMis !=''">
            and be_annotator_mis = #{param.beAnnotatorMis}
        </if>
        <if test="param.annotatorMis != null and param.annotatorMis != ''">
            and annotator_mis = #{param.annotatorMis}
        </if>
        <if test="param.encryptPhone != null and param.encryptPhone !=''">
            and encrypt_phone = #{param.encryptPhone}
        </if>
        <if test="param.workbenchType != null">
            and workbench_type = #{param.workbenchType}
        </if>
        <if test="param.startTime != null">
            and create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.taskId != null and param.taskId !=''">
            and task_id = #{param.taskId}
        </if>
        <if test="param.clewMis != null and param.clewMis !=''">
            and be_annotator_mis = #{param.clewMis}
        </if>
        <if test="param.outerBindInnerOrgIdList != null and param.outerBindInnerOrgIdList.size()>0">
            and ( org_id in
            <foreach collection="param.outerBindInnerOrgIdList" index="index" item="item" close=")" open="("
                     separator=",">
                #{item}
            </foreach>
            or annotator_mis = #{param.clewUserId}
            )
        </if>
        <if test="param.annotatorMisList!= null and param.annotatorMisList.size()>0">
            and annotator_mis in
            <foreach collection="param.annotatorMisList" index="index" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.beAnnotatorMisList!= null and param.beAnnotatorMisList.size()>0">
            and be_annotator_mis in
            <foreach collection="param.beAnnotatorMisList" index="index" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnotationRecordsDO" useGeneratedKeys="true">
        insert into cf_clew_annotation_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="annotator != null">
                annotator,
            </if>
            <if test="annotatorMis != null">
                annotator_mis,
            </if>
            <if test="beAnnotator != null">
                be_annotator,
            </if>
            <if test="beAnnotatorMis != null">
                be_annotator_mis,
            </if>
            <if test="encryptPhone != null">
                encrypt_phone,
            </if>
            <if test="workbenchType != null">
                workbench_type,
            </if>
            <if test="questionType != null">
                question_type,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="expectCompleteTime!= null">
                expect_complete_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="annotator != null">
                #{annotator},
            </if>
            <if test="annotatorMis != null">
                #{annotatorMis},
            </if>
            <if test="beAnnotator != null">
                #{beAnnotator},
            </if>
            <if test="beAnnotatorMis != null">
                #{beAnnotatorMis},
            </if>
            <if test="encryptPhone != null">
                #{encryptPhone},
            </if>
            <if test="workbenchType != null">
                #{workbenchType},
            </if>
            <if test="questionType != null">
                #{questionType},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="orgName != null">
                #{orgName},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="orgId != null">
                #{orgId},
            </if>
            <if test="expectCompleteTime!= null">
                #{expectCompleteTime},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnotationRecordsDO">
        update cf_clew_annotation_records
        <set>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="annotator != null">
                annotator = #{annotator},
            </if>
            <if test="annotatorMis != null">
                annotator_mis = #{annotatorMis},
            </if>
            <if test="beAnnotator != null">
                be_annotator = #{beAnnotator},
            </if>
            <if test="beAnnotatorMis != null">
                be_annotator_mis = #{beAnnotatorMis},
            </if>
            <if test="encryptPhone != null">
                encrypt_phone = #{encryptPhone},
            </if>
            <if test="workbenchType != null">
                workbench_type = #{workbenchType},
            </if>
            <if test="questionType != null">
                question_type = #{questionType},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="orgName != null">
                org_name = #{orgName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="orgId != null">
                org_id = #{orgId},
            </if>
            <if test="expectCompleteTime!= null">
                expect_complete_time = #{expectCompleteTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="listRecordByTaskIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAnnotationRecordsDO">
        select
        <include refid="tableFiled"/>
        from
        <include refid="tableName"/>
        where
        is_delete=0 and task_id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId}
        </foreach>
    </select>

</mapper>