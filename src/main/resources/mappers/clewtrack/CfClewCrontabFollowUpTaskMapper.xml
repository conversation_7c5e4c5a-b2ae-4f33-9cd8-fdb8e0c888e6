<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCrontabFollowUpTaskDao">

    <sql id="baseResult">
        `id`,
        `task_id`,
        `follow_up_task_id`,
        `handle_status`,
        `service_status`,
        `service_phase`,
        `last_handle_time`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="tableName">cf_clew_crontab_follow_up_task</sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CrontabFollowUpTaskDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into <include refid="tableName"/>
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="taskId!=null">
            task_id,
        </if>
        <if test="followUpTaskId!=null">
            follow_up_task_id,
        </if>
        <if test="handleStatus != null">
            handle_status,
        </if>
        <if test="serviceStatus != null" >
            service_status,
        </if>
        <if test="servicePhase != null ">
            service_phase,
        </if>
        <if test="lastHandleTime != null ">
            last_handle_time,
        </if>
        <if test="isDelete!=null">
            is_delete,
        </if>
        <if test="createTime != null">
            create_time,
        </if>
        <if test="updateTime != null">
            update_time,
        </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides="," >
        <if test="taskId!=null">
            #{taskId} ,
        </if>
        <if test="followUpTaskId!=null">
            #{followUpTaskId} ,
        </if>
        <if test="handleStatus != null">
            #{handleStatus} ,
        </if>
        <if test="serviceStatus != null" >
            #{serviceStatus} ,
        </if>
        <if test="servicePhase != null ">
            #{servicePhase} ,
        </if>
        <if test="lastHandleTime != null ">
            #{lastHandleTime} ,
        </if>
        <if test="isDelete!=null">
            #{isDelete},
        </if>
        <if test="createTime != null">
            #{createTime} ,
        </if>
        <if test="updateTime != null">
            #{updateTime} ,
        </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewFollowUpTaskDO">
    update <include refid="tableName"/>
    <set>
        <if test="followUpTaskId!=null">
            follow_up_task_id=#{followUpTaskId} ,
        </if>
        <if test="handleStatus != null">
            handle_status=#{handleStatus} ,
        </if>
        <if test="serviceStatus != null" >
            service_status=#{serviceStatus} ,
        </if>
        <if test="servicePhase != null ">
            service_phase=#{servicePhase} ,
        </if>
        <if test="lastHandleTime != null ">
            last_handle_time=#{lastHandleTime} ,
        </if>
        <if test="isDelete!=null">
            is_delete=#{isDelete} ,
        </if>
    </set>
    where id=#{id}
    </update>

    <select id="getInfoByTaskId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CrontabFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete= 0 and task_id = #{taskId}
        order by id desc limit 1
    </select>

    <select id="getInfoListByTime" resultType="com.shuidihuzhu.cf.clewtrack.domain.CrontabFollowUpTaskDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete= 0
        and last_handle_time between #{startTime} and #{endTime}
        and service_status = #{serviceStatus}
        and handle_status in
        <foreach collection="handleStatusList" item="handleStatus" separator="," open="(" close=")">
            #{handleStatus}
        </foreach>
    </select>

</mapper>
