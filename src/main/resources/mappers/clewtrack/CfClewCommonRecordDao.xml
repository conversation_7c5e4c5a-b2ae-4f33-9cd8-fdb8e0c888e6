<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCommonRecordDao">

    <sql id="tableName">cf_clew_common_record</sql>

    <sql id="selectFiled">
        `id`,
        `business_id` as businessId,
        `operator_id` as operatorId,
        `record_type` as recordType,
        `content` as content,
        `create_time` as createTime,
         operator_name
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCommonRecordDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        (`business_id`,`operator_id`,`content`,`record_type`,operator_name)
        values
        (#{businessId}, #{operatorId}, #{content}, #{recordType},#{operatorName})
    </insert>

    <select id="listByBusinessId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCommonRecordDO">
        select <include refid="selectFiled"/> from
        <include refid="tableName"/>
        where `business_id` = #{businessId} and record_type = 0  order by create_time desc
    </select>

    <select id="listByBusinessIdAndType" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCommonRecordDO">
        select <include refid="selectFiled"/> from
        <include refid="tableName"/>
        where `business_id` = #{businessId} and record_type = #{recordType}  order by create_time desc
    </select>

</mapper>
