<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewAttachChangeInfoDao">

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachChangeInfoDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into cf_clew_attach_change_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="clewId!=null">
                clew_id,
            </if>
            <if test="sourceUserId!=null and sourceUserId!=''">
                source_user_id,
            </if>
            <if test="sourceUserName!=null and sourceUserName!=''">
                source_user_name,
            </if>
            <if test="sourceAssginTime!=null">
                source_assgin_time,
            </if>
            <if test="targetUserId!=null and targetUserId!=''">
                target_user_id,
            </if>
            <if test="targetUserName!=null and targetUserName!=''">
                target_user_name,
            </if>
            <if test="targetAssginTime!=null">
                target_assgin_time,
            </if>
            <if test="attachChangeReason!=null and attachChangeReason!=''">
                attach_change_reason,
            </if>
            <if test="attachChangeOperator!=null and attachChangeOperator!=''">
                attach_change_operator,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="clewId!=null">
                #{clewId},
            </if>
            <if test="sourceUserId!=null and sourceUserId!=''">
                #{sourceUserId},
            </if>
            <if test="sourceUserName!=null and sourceUserName!=''">
                #{sourceUserName},
            </if>
            <if test="sourceAssginTime!=null">
                #{sourceAssginTime},
            </if>
            <if test="targetUserId!=null and targetUserId!=''">
                #{targetUserId},
            </if>
            <if test="targetUserName!=null and targetUserName!=''">
                #{targetUserName},
            </if>
            <if test="targetAssginTime!=null">
                #{targetAssginTime},
            </if>
            <if test="attachChangeReason!=null and attachChangeReason!=''">
                #{attachChangeReason},
            </if>
            <if test="attachChangeOperator!=null and attachChangeOperator!=''">
                #{attachChangeOperator},
            </if>
        </trim>
    </insert>
</mapper>
