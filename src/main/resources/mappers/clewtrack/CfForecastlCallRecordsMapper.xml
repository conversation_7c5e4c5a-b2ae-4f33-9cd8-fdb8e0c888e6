<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfForecastCallRecordsDao">

    <sql id="tableName">cf_forecast_call_records</sql>

    <sql id="baseResult">
        `id`,
	    `task_id`,
	    `task_file_id`,
	    `clew_id`,
	    `clew_task_id`,
	    `status`,
	    `end_reason`,
	    `unique_id`,
        `customer_province`,
        `customer_city`,
        `cno`,
        `callee_number` ,
        `start_time` ,
	    `end_time` ,
        `answer_time` ,
	    `bridge_time`,
        `bridge_duration` ,
	    `total_duration`,
        `sip_cause`,
        `create_time`,
        `update_time`,
        `task_type`,
        `sort_type`,
	    `is_delete`,
	    `is_import`,
	    `encrypt_phone`
    </sql>

    <sql id="BATCH_INSERT_FIELDS">
        `task_id`,
	    `clew_id`,
	    `clew_task_id`,
        `task_type`,
        `sort_type`,
        `is_import`,
        `encrypt_phone`
    </sql>

    <insert id="addBatchByTaskId" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO">
        INSERT IGNORE INTO
        <include refid="tableName"/>
        (<include refid="BATCH_INSERT_FIELDS"/>)
        VALUES
        <foreach collection="items" item="item" separator=",">
            (#{item.taskId},#{item.clewId},#{item.clewTaskId},#{item.taskType},#{item.sortType},#{item.isImport},#{item.encryptPhone})
        </foreach>
    </insert>

    <select id="getCfForecastCallRecordsByTaskId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO">
        select <include refid= "baseResult"/>
        from  <include refid="tableName"/>
        where task_id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId}
        </foreach>
    </select>

    <update id="batchUpdateCfForecastCallRecords" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_file_id =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.taskFileId !=null">
                        when id=#{item.id} then #{item.taskFileId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="status =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.status !=null">
                        when id=#{item.id} then #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="end_reason =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.endReason !=null">
                        when id=#{item.id} then #{item.endReason}
                    </if>
                </foreach>
            </trim>
            <trim prefix="unique_id =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.uniqueId !=null">
                        when id=#{item.id} then #{item.uniqueId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="customer_province =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.customerProvince !=null">
                        when id=#{item.id} then #{item.customerProvince}
                    </if>
                </foreach>
            </trim>
            <trim prefix="customer_city =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.customerCity !=null">
                        when id=#{item.id} then #{item.customerCity}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cno =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.cno !=null">
                        when id=#{item.id} then #{item.cno}
                    </if>
                </foreach>
            </trim>
            <trim prefix="callee_number =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.calleeNumber !=null">
                        when id=#{item.id} then #{item.calleeNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="start_time =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.startTime !=null">
                        when id=#{item.id} then #{item.startTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="end_time =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.endTime !=null">
                        when id=#{item.id} then #{item.endTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="answer_time =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.answerTime !=null">
                        when id=#{item.id} then #{item.answerTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bridge_time =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.bridgeTime !=null">
                        when id=#{item.id} then #{item.bridgeTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bridge_duration =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.bridgeDuration !=null">
                        when id=#{item.id} then #{item.bridgeDuration}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_duration =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.totalDuration !=null">
                        when id=#{item.id} then #{item.totalDuration}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sip_cause =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.sipCause !=null">
                        when id=#{item.id} then #{item.sipCause}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="items" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        order by id desc
    </update>

    <update id="batchUpdateClewIdByUniqueId" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO">
        update <include refid="tableName"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="clew_id =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.clewId !=null">
                        when id=#{item.id} then #{item.clewId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_update =case" suffix="end,">
                <foreach collection="items" item="item" index="index">
                    <if test="item.id !=null">
                        when id=#{item.id} then 1
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="items" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        order by id desc
    </update>

    <select id="getCfForecastCallRecordsByCreatTime"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO">
        select <include refid= "baseResult"/>
        from  <include refid="tableName"/>
        where
        <if test="uniqueId !=null">
            unique_id = '' and
        </if>
         create_time between #{startDate} and #{endDate}
        order by id;
    </select>
    <select id="getCfForecastCallRecordsByTaskIdAndIsUpdate"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO">
        select <include refid= "baseResult"/>
        from  <include refid="tableName"/>
        where task_id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId}
        </foreach>
        and is_update = 0 and unique_id != '';
    </select>

    <select id="getImportedPhonesByCreatTime" resultType="java.lang.String">
        select distinct encrypt_phone
        from  <include refid="tableName"/>
        where
        task_id = #{taskId}
        and
        create_time between #{startTime} and #{endTime}
    </select>

    <select id="getRecordsIdsByTaskId" resultType="java.lang.Long">
        select id
        from  <include refid="tableName"/>
        where
        task_id = #{taskId}
    </select>

    <select id="getLatestCfForecastCallRecordsByCreatTimeAndPhone"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO">
        select <include refid= "baseResult"/>
        from  <include refid="tableName"/>
        where encrypt_phone = #{encryptPhone}
        and create_time >= #{startDate}
        and is_delete = 0
        ORDER BY create_time DESC
        limit 1
    </select>
    <select id="getForecastHandledCount" resultType="com.shuidihuzhu.cf.clewtrack.model.CFDengjiDataPanelModel">
        select task_type as taskType, count(*) handledCount
        from  <include refid="tableName"/>
        where is_delete = 0
            and create_time > #{startDate}
            and task_type in (0,1)
          group by task_type;
    </select>
    <select id="getRecallhandledCount" resultType="java.lang.Integer">
        select count(*)
        from  <include refid="tableName"/>
        where is_delete = 0
            and create_time > #{startDate}
            and task_Id != 0
            and task_type = 2
            and is_import = 1
    </select>
    <select id="getRecallUnhandleCount" resultType="java.lang.Integer">
        select count(*)
        from  <include refid="tableName"/>
        where is_delete = 0
        and create_time > #{startDate}
        and task_Id = 0
        and is_import = 0
    </select>
    <select id="getCalllossCount" resultType="com.shuidihuzhu.cf.clewtrack.model.CFDengjiDataPanelModel">
        select task_type as taskType, count(*) as callLossCount
        from  <include refid="tableName"/>
        where is_delete = 0
        and create_time > #{startDate}
        and status in
            <foreach collection="callLossStatus" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
        group by task_type
    </select>

    <update id="batchUpdateTaskIdAndIsImportByIds">
        update <include refid="tableName"/>
        <set>
        <if test="taskId != null ">
            task_id = #{taskId} ,
        </if>
            is_import = 1
        </set>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getRecallPredictiveCallPhone" resultType="com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel">
        select id as recordId, encrypt_phone as encrypt_phone, clew_id as clewId, clew_task_id as taskId
        from <include refid="tableName"/>
        where task_id = 0
        and is_import = 0
        and create_time BETWEEN #{startTime} AND #{endTime}
        order by sort_type desc,create_time desc
        limit #{importPhoneNums}
    </select>

</mapper>
