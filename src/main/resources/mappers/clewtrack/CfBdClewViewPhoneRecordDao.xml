<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfBdClewViewPhoneRecordDao">

    <sql id="tableFiled">
        `id`,
        `clew_id`,
        `unique_code`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <sql id="tableName">
        cf_bd_clew_view_phone_record
    </sql>

    <insert id="insertCfBdClewViewPhoneRecord" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>
        (`clew_id`, `unique_code`)
        values
        (#{clewId}, #{uniqueCode})
    </insert>

    <select id="getRecordByClewIdAndUniqueCode" resultType="com.shuidihuzhu.cf.clewtrack.domain.bdcrm.CfBdClewViewPhoneRecordDO">
        select <include refid="tableFiled"/>
        from <include refid="tableName"/>
        where clew_id = #{clewId} and unique_code = #{uniqueCode}
        and is_delete = 0
    </select>


</mapper>