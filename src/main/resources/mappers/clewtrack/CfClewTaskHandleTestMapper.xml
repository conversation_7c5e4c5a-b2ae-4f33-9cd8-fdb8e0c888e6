<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTaskHandleTestDao">

    <sql id="baseResult">
        `id`,
        `mobile`,
        `record_type`,
        `reg_type`,
        `send_time`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="tableName">cf_clew_task_handle_test</sql>

    <insert id="insertClewTaskHandleTest" parameterType="com.shuidihuzhu.cf.clewtrack.param.CfTaskTypeTestQueryParam"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (`mobile`,
        `record_type`,
        `reg_type`,
        `send_time`,
        `create_time`,
        `update_time`)
        values
        (#{phone},
        #{recordType},
        #{regType},
        #{sendTime},
        sysdate(),
        sysdate())
    </insert>

    <select id="getClewTaskHandleTestList" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewTaskHandleTestModel">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where id in (
            select max(id) from <include refid="tableName"/>
            where mobile = #{phone} and record_type in (1,2)
            group by record_type
        )
    </select>

    <select id="getClewTaskHandleTest" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewTaskHandleTestModel">
        SELECT <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete=0
        and mobile = #{phone}
        and record_type = #{recordType}
        order by create_time desc
        limit 1
    </select>

</mapper>
