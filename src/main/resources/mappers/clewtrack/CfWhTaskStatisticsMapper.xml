<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfWhTaskStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.clewtrack.domain.CfWhTaskStatisticsDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date_time" jdbcType="VARCHAR" property="dateTime" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="primary_channel" jdbcType="VARCHAR" property="primaryChannel" />
    <result column="work_content_type" jdbcType="INTEGER" property="workContentType" />
    <result column="work_content_desc" jdbcType="VARCHAR" property="workContentDesc" />
    <result column="cur_wait_assign_num" jdbcType="INTEGER" property="curWaitAssignNum" />
    <result column="cur_assigned_num" jdbcType="INTEGER" property="curAssignedNum" />
    <result column="cur_assigned_call_connected_num" jdbcType="INTEGER" property="curAssignedCallConnectedNum" />
    <result column="un_cur_assigned_call_conn_num" jdbcType="INTEGER" property="unCurAssignedCallConnectedNum" />
    <result column="cur_assigned_call_connected_rate" jdbcType="VARCHAR" property="curAssignedCallConnectedRate" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="cur_assigned_flow_1v1_num" jdbcType="INTEGER" property="curAssignedFlow1v1Num" />
    <result column="un_cur_assigned_flow_1v1_num" jdbcType="INTEGER" property="unCurAssignedFlow1v1Num" />
    <result column="cur_flow_1v1_num" jdbcType="INTEGER" property="curFlow1v1Num" />
    <result column="cur_assigned_flow_1v1_num_rate" jdbcType="VARCHAR" property="curAssignedFlow1v1NumRate" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="cur_total_call_time" jdbcType="INTEGER" property="curTotalCallTime" />
    <result column="cur_mean_call_time" jdbcType="VARCHAR" property="curMeanCallTime" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="cur_total_call_num" jdbcType="INTEGER" property="curTotalCallNum" />
    <result column="cur_total_can_call_num" jdbcType="INTEGER" property="curTotalCanCallNum" />
    <result column="cur_assigned_mean_call_num" jdbcType="VARCHAR" property="curAssignedMeanCallNum" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="cur_call_connected_num" jdbcType="INTEGER" property="curCallConnectedNum" />
    <result column="cur_call_connected_30s_num" jdbcType="INTEGER" property="curCallConnected30sNum" />
    <result column="cur_call_connected_30s_rate" jdbcType="VARCHAR" property="curCallConnected30sRate" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="cur_attendance_num" jdbcType="INTEGER" property="curAttendanceNum" />
    <result column="cur_efficiency_rate" jdbcType="VARCHAR" property="curEfficiencyRate" typeHandler="org.apache.ibatis.type.DoubleTypeHandler"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    id, date_time, org_id, org_name, user_id, user_name, primary_channel, work_content_type,
    work_content_desc, cur_wait_assign_num, cur_assigned_num, cur_assigned_call_connected_num,
    un_cur_assigned_call_conn_num, cur_assigned_call_connected_rate, cur_assigned_flow_1v1_num,
    un_cur_assigned_flow_1v1_num, cur_flow_1v1_num, cur_assigned_flow_1v1_num_rate, cur_total_call_time,
    cur_mean_call_time, cur_total_call_num, cur_total_can_call_num, cur_assigned_mean_call_num, cur_call_connected_num,
    cur_call_connected_30s_num, cur_call_connected_30s_rate, cur_attendance_num, cur_efficiency_rate,
    create_time, update_time, is_delete
  </sql>
  <sql id="table_name">cf_wh_task_statistics</sql>
  <insert id="batchInsert">
    insert into <include refid="table_name"/> (date_time, org_id, org_name,
      user_id, user_name, primary_channel,
      work_content_type, work_content_desc, cur_wait_assign_num,
      cur_assigned_num, cur_assigned_call_connected_num,
      un_cur_assigned_call_conn_num, cur_assigned_call_connected_rate,
      cur_assigned_flow_1v1_num, un_cur_assigned_flow_1v1_num,
      cur_flow_1v1_num, cur_assigned_flow_1v1_num_rate,
      cur_total_call_time, cur_total_can_call_num, cur_mean_call_time, cur_total_call_num,
      cur_assigned_mean_call_num, cur_call_connected_num,
      cur_call_connected_30s_num, cur_call_connected_30s_rate,
      cur_attendance_num, cur_efficiency_rate)
    values
    <foreach collection="modelList" item="model" separator=",">
     (#{model.dateTime,jdbcType=VARCHAR}, #{model.orgId,jdbcType=BIGINT}, #{model.orgName,jdbcType=VARCHAR},
      #{model.userId,jdbcType=VARCHAR}, #{model.userName,jdbcType=VARCHAR}, #{model.primaryChannel,jdbcType=VARCHAR},
      #{model.workContentType,jdbcType=INTEGER}, #{model.workContentDesc,jdbcType=VARCHAR}, #{model.curWaitAssignNum,jdbcType=INTEGER},
      #{model.curAssignedNum,jdbcType=INTEGER}, #{model.curAssignedCallConnectedNum,jdbcType=INTEGER},
      #{model.unCurAssignedCallConnectedNum,jdbcType=INTEGER}, #{model.curAssignedCallConnectedRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      #{model.curAssignedFlow1v1Num,jdbcType=INTEGER}, #{model.unCurAssignedFlow1v1Num,jdbcType=INTEGER},
      #{model.curFlow1v1Num,jdbcType=INTEGER}, #{model.curAssignedFlow1v1NumRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      #{model.curTotalCallTime,jdbcType=INTEGER}, #{model.curTotalCanCallNum},#{model.curMeanCallTime,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler}, #{model.curTotalCallNum,jdbcType=INTEGER},
      #{model.curAssignedMeanCallNum,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler}, #{model.curCallConnectedNum,jdbcType=INTEGER},
      #{model.curCallConnected30sNum,jdbcType=INTEGER}, #{model.curCallConnected30sRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      #{model.curAttendanceNum,jdbcType=INTEGER}, #{model.curEfficiencyRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler})
    </foreach>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfWhTaskStatisticsDO">
    update <include refid="table_name"/>
    set date_time = #{dateTime,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      primary_channel = #{primaryChannel,jdbcType=VARCHAR},
      work_content_type = #{workContentType,jdbcType=INTEGER},
      work_content_desc = #{workContentDesc,jdbcType=VARCHAR},
      cur_wait_assign_num = #{curWaitAssignNum,jdbcType=INTEGER},
      cur_assigned_num = #{curAssignedNum,jdbcType=INTEGER},
      cur_assigned_call_connected_num = #{curAssignedCallConnectedNum,jdbcType=INTEGER},
      un_cur_assigned_call_conn_num = #{unCurAssignedCallConnectedNum,jdbcType=INTEGER},
      cur_assigned_call_connected_rate = #{curAssignedCallConnectedRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      cur_assigned_flow_1v1_num = #{curAssignedFlow1v1Num,jdbcType=INTEGER},
      un_cur_assigned_flow_1v1_num = #{unCurAssignedFlow1v1Num,jdbcType=INTEGER},
      cur_flow_1v1_num = #{curFlow1v1Num,jdbcType=INTEGER},
      cur_assigned_flow_1v1_num_rate = #{curAssignedFlow1v1NumRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      cur_total_call_time = #{curTotalCallTime,jdbcType=INTEGER},
      cur_mean_call_time = #{curMeanCallTime,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      cur_total_call_num = #{curTotalCallNum,jdbcType=INTEGER},
      cur_total_can_call_num = #{curTotalCanCallNum,jdbcType=INTEGER},
      cur_assigned_mean_call_num = #{curAssignedMeanCallNum,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      cur_call_connected_num = #{curCallConnectedNum,jdbcType=INTEGER},
      cur_call_connected_30s_num = #{curCallConnected30sNum,jdbcType=INTEGER},
      cur_call_connected_30s_rate = #{curCallConnected30sRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler},
      cur_attendance_num = #{curAttendanceNum,jdbcType=INTEGER},
      cur_efficiency_rate = #{curEfficiencyRate,jdbcType=VARCHAR,typeHandler=org.apache.ibatis.type.DoubleTypeHandler}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listCurDayData" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from <include refid="table_name"/>
    where is_delete=0 and date_time=#{dateTime}
  </select>
  <select id="groupByDateTime" resultType="com.shuidihuzhu.cf.clewtrack.model.CfWhTaskStatisticsBaseModel">
    select
      sum(cur_total_can_call_num)  as curTotalCanCallNum,
      sum(cur_wait_assign_num)  as curWaitAssignNum,
      sum(cur_assigned_num)  as curAssignedNum,
      sum(cur_assigned_call_connected_num)  as curAssignedCallConnectedNum,
      sum(un_cur_assigned_call_conn_num)  as unCurAssignedCallConnectedNum,
      sum(cur_assigned_flow_1v1_num)  as curAssignedFlow1v1Num,
      sum(un_cur_assigned_flow_1v1_num)  as unCurAssignedFlow1v1Num,
      sum(cur_flow_1v1_num)  as curFlow1v1Num,
      sum(cur_total_call_time)  as curTotalCallTime,
      sum(cur_total_call_num)  as curTotalCallNum,
      sum(cur_call_connected_num)  as curCallConnectedNum,
      sum(cur_call_connected_30s_num)  as curCallConnected30sNum
    from <include refid="table_name"/>
    where is_delete = 0 and date_time=#{dateTime}
    <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
       and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
    </if>
  </select>

  <select id="getWaitAssignNum" resultType="java.lang.Long">
    select
    sum(cur_wait_assign_num)
    from <include refid="table_name"/>historyTimes
    where
    is_delete = 0
    <if test="historyTimes != null and historyTimes.size() != 0">
      and date_time in  <foreach collection="historyTimes" item="time" open="(" separator="," close=")">#{time}</foreach>
    </if>
    <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
      and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
    </if>
  </select>

  <select id="getAssignedNumGroupByOrgIdWithUserId" resultType="com.shuidihuzhu.cf.clewtrack.model.CfWhTaskAssignModel">
    select org_id as orgId, user_id as userId, sum(cur_assigned_num) as curAssignedNum
    from <include refid="table_name"/>
    where is_delete = 0 and date_time=#{dateTime} and user_id not in ('ROBOT','')
    <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
       and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
    </if>
    group by org_id, user_id
  </select>
  <select id="groupByOrgId" resultType="com.shuidihuzhu.cf.clewtrack.model.CfWhTaskStatisticsModel">
    select org_id as orgId, org_name as showName,
      sum(cur_total_can_call_num)  as curTotalCanCallNum,
      sum(cur_assigned_num)  as curAssignedNum,
      sum(cur_assigned_call_connected_num)  as curAssignedCallConnectedNum,
      sum(un_cur_assigned_call_conn_num)  as unCurAssignedCallConnectedNum,
      sum(cur_assigned_flow_1v1_num)  as curAssignedFlow1v1Num,
      sum(un_cur_assigned_flow_1v1_num)  as unCurAssignedFlow1v1Num,
      sum(cur_flow_1v1_num)  as curFlow1v1Num,
      sum(cur_total_call_time)  as curTotalCallTime,
      sum(cur_total_call_num)  as curTotalCallNum,
      sum(cur_call_connected_num)  as curCallConnectedNum,
      sum(cur_call_connected_30s_num)  as curCallConnected30sNum
    from <include refid="table_name"/>
    where is_delete = 0 and date_time=#{dateTime}
    <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
       and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
    </if>
    group by org_id, org_name
  </select>

  <select id="groupByChannel" resultType="com.shuidihuzhu.cf.clewtrack.model.CfWhTaskStatisticsModel">
    select primary_channel as showName,
      sum(cur_total_can_call_num)  as curTotalCanCallNum,
      sum(cur_wait_assign_num)  as curWaitAssignNum,
      sum(cur_assigned_num)  as curAssignedNum,
      sum(cur_assigned_call_connected_num)  as curAssignedCallConnectedNum,
      sum(un_cur_assigned_call_conn_num)  as unCurAssignedCallConnectedNum,
      sum(cur_assigned_flow_1v1_num)  as curAssignedFlow1v1Num,
      sum(un_cur_assigned_flow_1v1_num)  as unCurAssignedFlow1v1Num,
      sum(cur_flow_1v1_num)  as curFlow1v1Num,
      sum(cur_total_call_time)  as curTotalCallTime,
      sum(cur_total_call_num)  as curTotalCallNum,
      sum(cur_call_connected_num)  as curCallConnectedNum,
      sum(cur_call_connected_30s_num)  as curCallConnected30sNum
    from <include refid="table_name"/>
    where is_delete = 0 and date_time=#{dateTime}
    <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
        and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
    </if>
    group by primary_channel
  </select>

  <select id="getWaitAssignNumGroupByChannel" resultType="java.util.HashMap">
    select primary_channel as channel,
    sum(cur_wait_assign_num)  as waitAssignNum
    from <include refid="table_name"/>
    where
    is_delete = 0
    <if test="historyTimes != null and historyTimes.size() != 0">
      and date_time in  <foreach collection="historyTimes" item="time" open="(" separator="," close=")">#{time}</foreach>
    </if>
    <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
      and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
    </if>
    group by primary_channel
  </select>

  <select id="groupByOrgIdWithUserId" resultType="com.shuidihuzhu.cf.clewtrack.model.CfWhTaskStatisticsMemberVO">
      select org_id as orgId, org_name as orgName, user_id as userId, user_name as userName, primary_channel as primaryChannel,
          sum(cur_total_can_call_num)  as curTotalCanCallNum,
          sum(cur_assigned_num)  as curAssignedNum,
          sum(cur_assigned_call_connected_num)  as curAssignedCallConnectedNum,
          sum(un_cur_assigned_call_conn_num)  as unCurAssignedCallConnectedNum,
          sum(cur_assigned_flow_1v1_num)  as curAssignedFlow1v1Num,
          sum(un_cur_assigned_flow_1v1_num)  as unCurAssignedFlow1v1Num,
          sum(cur_flow_1v1_num)  as curFlow1v1Num,
          sum(cur_total_call_time)  as curTotalCallTime,
          sum(cur_total_call_num)  as curTotalCallNum,
          sum(cur_call_connected_num)  as curCallConnectedNum,
          sum(cur_call_connected_30s_num)  as curCallConnected30sNum
        from <include refid="table_name"/>
        where is_delete = 0 and date_time=#{dateTime}
        <if test="searchUserId != null and searchUserId.size() != 0">
            and user_id in <foreach collection="searchUserId" item="userId" open="(" separator="," close=")">#{userId}</foreach>
        </if>
        group by user_id,org_id
        order by user_id
  </select>

  <select id="groupByChannelWithUserId" resultType="com.shuidihuzhu.cf.clewtrack.model.CfWhTaskStatisticsModel">
    select primary_channel as showName,
    sum(cur_total_can_call_num)  as curTotalCanCallNum,
    sum(cur_assigned_num)  as curAssignedNum,
    sum(cur_assigned_call_connected_num)  as curAssignedCallConnectedNum,
    sum(un_cur_assigned_call_conn_num)  as unCurAssignedCallConnectedNum,
    sum(cur_assigned_flow_1v1_num)  as curAssignedFlow1v1Num,
    sum(un_cur_assigned_flow_1v1_num)  as unCurAssignedFlow1v1Num,
    sum(cur_flow_1v1_num)  as curFlow1v1Num,
    sum(cur_total_call_time)  as curTotalCallTime,
    sum(cur_total_call_num)  as curTotalCallNum,
    sum(cur_call_connected_num)  as curCallConnectedNum,
    sum(cur_call_connected_30s_num)  as curCallConnected30sNum
    from <include refid="table_name"/>
    where is_delete = 0 and date_time=#{dateTime}
    and user_id = #{userId}
    group by primary_channel
  </select>

</mapper>
