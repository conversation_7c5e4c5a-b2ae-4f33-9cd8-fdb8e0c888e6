<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewWxCorpMsgDao">
    <sql id="tableName">cf_clew_wx_corp_msg</sql>

    <sql id="fields">
        `id`,
        `corp_name`,
        `corp_id`,
        `corp_secret`,
        `agent_id`,
        `note_secret`,
        `token`,
        `aes_key`,
        `callback_id`,
        `callback_url`,
        `msg_type`,
        `operator`,
        `corp_type`,
        `is_delete`,
        `create_time`,
        `update_time`
    </sql>

    <select id="getWxCorpMsg" resultType="com.shuidihuzhu.client.cf.clewtrack.model.CfClewWxCorpMsgDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
    </select>

    <select id="getWxCorpMsgByCallbackId" resultType="com.shuidihuzhu.client.cf.clewtrack.model.CfClewWxCorpMsgDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and callback_id = #{callbackId}
        limit 1
    </select>

</mapper>
