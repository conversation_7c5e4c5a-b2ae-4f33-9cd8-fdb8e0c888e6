<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfDonateTaskCaseStatisticDao">

    <sql id="tableName">cf_donate_task_case_statistics</sql>

    <sql id="baseResult">
        id,
        date_time,
        org_id,
        org_name,
        user_id,
        user_name,
        service_type,
        work_content_type,
        work_content_desc,
        cur_assign_case,
        cur_call_succ,
        cur_calls,
        cur_total_duration,
        cur_share_sum_d0,
        cur_share_sum_d3,
        cur_donate_sum_d0,
        cur_donate_sum_d3,
        cur_fundraiser_share_sum_d0,
        cur_fundraiser_share_sum_d3,
        cur_donate_0_d3,
        cur_donate_50_d3,
        cur_donate_100_d3,
        cur_donate_199_d3,
        cur_donate_299_d3,
        cur_donate_399_d3,
        cur_donate_499_d3,
        cur_donate_999_d3,
        cur_donate_1999_d3,
        cur_donate_2000_d3,
        cur_case_end_d3,
        fundraiser_share_sum,
        cur_assign_case_sum,
        create_time,
        update_time,
        is_delete
    </sql>

    <insert id="insertOrUpdate" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid = "tableName"/>
        (date_time,org_id,org_name,user_id,user_name,service_type,work_content_type,work_content_desc,
         cur_assign_case,cur_call_succ,cur_calls,cur_total_duration,cur_share_sum_d0,cur_share_sum_d3,cur_donate_sum_d0,
         cur_donate_sum_d3,cur_fundraiser_share_sum_d0,cur_fundraiser_share_sum_d3,cur_donate_0_d3,cur_donate_50_d3,
         cur_donate_100_d3,cur_donate_199_d3,cur_donate_299_d3,cur_donate_399_d3,cur_donate_499_d3,cur_donate_999_d3,
         cur_donate_1999_d3,cur_donate_2000_d3,cur_case_end_d3,fundraiser_share_sum,cur_assign_case_sum)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.dateTime},#{entity.orgId},#{entity.orgName},#{entity.userId},#{entity.userName},#{entity.serviceType},#{entity.workContentType},#{entity.workContentDesc},
             #{entity.curAssignCase},#{entity.curCallSucc},#{entity.curCalls}, #{entity.curTotalDuration},#{entity.curShareSumD0},#{entity.curShareSumD3},#{entity.curDonateSumD0},
             #{entity.curDonateSumD3},#{entity.curFundraiserShareSumD0},#{entity.curFundraiserShareSumD3},#{entity.curDonate0D3},#{entity.curDonate50D3},
             #{entity.curDonate100D3},#{entity.curDonate199D3},#{entity.curDonate299D3},#{entity.curDonate399D3},#{entity.curDonate499D3},#{entity.curDonate999D3},
             #{entity.curDonate1999D3},#{entity.curDonate2000D3},#{entity.curCaseEndD3},#{entity.fundraiserShareSum},#{entity.curAssignCaseSum}
            )
        </foreach>
        on duplicate key update
        cur_assign_case = values(cur_assign_case),
        cur_call_succ = values(cur_call_succ),
        cur_calls= values(cur_calls),
        cur_total_duration = values(cur_total_duration),
        cur_share_sum_d0 = values(cur_share_sum_d0),
        cur_share_sum_d3 = values(cur_share_sum_d3),
        cur_donate_sum_d0 = values(cur_donate_sum_d0),
        cur_donate_sum_d3 = values(cur_donate_sum_d3),
        cur_fundraiser_share_sum_d0 = values(cur_fundraiser_share_sum_d0),
        cur_fundraiser_share_sum_d3 = values(cur_fundraiser_share_sum_d3),
        cur_donate_0_d3 = values(cur_donate_0_d3),
        cur_donate_50_d3 = values(cur_donate_50_d3),
        cur_donate_100_d3 = values(cur_donate_100_d3),
        cur_donate_199_d3 = values(cur_donate_199_d3),
        cur_donate_299_d3 = values(cur_donate_299_d3),
        cur_donate_399_d3 = values(cur_donate_399_d3),
        cur_donate_499_d3 = values(cur_donate_499_d3),
        cur_donate_999_d3 = values(cur_donate_999_d3),
        cur_donate_1999_d3 = values(cur_donate_1999_d3),
        cur_donate_2000_d3 = values(cur_donate_2000_d3),
        cur_case_end_d3 = values(cur_case_end_d3),
        fundraiser_share_sum = values(fundraiser_share_sum),
        cur_assign_case_sum = values(cur_assign_case_sum)
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateTaskCaseStatisticDo">
        update <include refid="tableName"/>
        <set>
            cur_share_sum_d3 = #{curShareSumD3},
            cur_donate_sum_d3 = #{curDonateSumD3},
            cur_fundraiser_share_sum_d3 = #{curFundraiserShareSumD3},
            cur_donate_0_d3 = #{curDonate0D3},
            cur_donate_50_d3 = #{curDonate50D3},
            cur_donate_100_d3 = #{curDonate100D3},
            cur_donate_199_d3 = #{curDonate199D3},
            cur_donate_299_d3 = #{curDonate299D3},
            cur_donate_399_d3 = #{curDonate399D3},
            cur_donate_499_d3 = #{curDonate499D3},
            cur_donate_999_d3 = #{curDonate999D3},
            cur_donate_1999_d3 = #{curDonate1999D3},
            cur_donate_2000_d3 = #{curDonate2000D3},
            cur_case_end_d3 = #{curCaseEndD3}
        </set>
        where date_time=#{dateTime} and user_id = #{userId} and service_type = #{serviceType}
    </update>

    <select id="statisticDonateCaseOrg"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateTaskCaseStatisticDo">
        select date_time as dateTime,
         org_id as orgId,
         org_name as orgName,
         sum(cur_assign_case) as curAssignCase,
         sum(cur_call_succ) as curCallSucc,
         sum(cur_calls) as curCalls,
         sum(cur_assign_case_sum) as curAssignCaseSum,
         sum(fundraiser_share_sum) as fundraiserShareSum,
         sum(cur_total_duration) as curTotalDuration,
         sum(cur_share_sum_d0) as curShareSumD0,
         sum(cur_share_sum_d3) as curShareSumD3,
         sum(cur_donate_sum_d0) as curDonateSumD0,
         sum(cur_donate_sum_d3) as curDonateSumD3,
         sum(cur_fundraiser_share_sum_d0) as curFundraiserShareSumD0,
         sum(cur_fundraiser_share_sum_d3) as curFundraiserShareSumD3,
         sum(cur_donate_0_d3) as curDonate0D3,
         sum(cur_donate_50_d3) as curDonate50D3,
         sum(cur_donate_100_d3) as curDonate100D3,
         sum(cur_donate_199_d3) as curDonate199D3,
         sum(cur_donate_299_d3) as curDonate299D3,
         sum(cur_donate_399_d3) as curDonate399D3,
         sum(cur_donate_499_d3) as curDonate499D3,
         sum(cur_donate_999_d3) as curDonate999D3,
         sum(cur_donate_1999_d3) as curDonate1999D3,
         sum(cur_donate_2000_d3) as curDonate2000D3,
         sum(cur_case_end_d3) as curCaseEndD3,
         count(*) as attendance_num
        from <include refid="tableName"/>
        where date_time=#{dateTime}
        <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
            and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
        </if>
        and service_type = 0 and is_delete=0 and org_id > 0
        group by org_id
    </select>

    <select id="statisticDonateCaseOrgService"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateTaskCaseStatisticDo">
        select date_time as dateTime,
        org_id as orgId,
        org_name as orgName,
        service_type as serviceType,
        sum(cur_assign_case) as curAssignCase,
        sum(cur_call_succ) as curCallSucc,
        sum(cur_assign_case_sum) as curAssignCaseSum,
        sum(fundraiser_share_sum) as fundraiserShareSum,
        sum(cur_total_duration) as curTotalDuration,
        sum(cur_share_sum_d0) as curShareSumD0,
        sum(cur_share_sum_d3) as curShareSumD3,
        sum(cur_donate_sum_d0) as curDonateSumD0,
        sum(cur_donate_sum_d3) as curDonateSumD3,
        sum(cur_fundraiser_share_sum_d0) as curFundraiserShareSumD0,
        sum(cur_fundraiser_share_sum_d3) as curFundraiserShareSumD3,
        sum(cur_donate_0_d3) as curDonate0D3,
        sum(cur_donate_50_d3) as curDonate50D3,
        sum(cur_donate_100_d3) as curDonate100D3,
        sum(cur_donate_199_d3) as curDonate199D3,
        sum(cur_donate_299_d3) as curDonate299D3,
        sum(cur_donate_399_d3) as curDonate399D3,
        sum(cur_donate_499_d3) as curDonate499D3,
        sum(cur_donate_999_d3) as curDonate999D3,
        sum(cur_donate_1999_d3) as curDonate1999D3,
        sum(cur_donate_2000_d3) as curDonate2000D3,
        sum(cur_case_end_d3) as curCaseEndD3,
        count(*) as attendance_num
        from <include refid="tableName"/>
        where date_time=#{dateTime} and service_type in (1,2) and is_delete=0 and org_id > 0
        group by org_id,serviceType
    </select>

    <select id="statisticDonateCaseUser"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfDonateTaskCaseStatisticDo">
        select <include refid="baseResult"/> from <include refid="tableName"/>
        where is_delete=0 and date_time = #{searchModel.dateTime} and service_type = 0 and org_id > 0
        <if test="searchModel.orgIds != null and searchModel.orgIds.size() != 0">
            and org_id in 
            <foreach collection="searchModel.orgIds" item="orgId" open="(" close=")" separator=",">
                #{orgId} 
            </foreach> 
        </if>
        <if test="searchModel.searchUserIds != null and searchModel.searchUserIds.size() != 0">
            and user_id in 
            <foreach collection="searchModel.searchUserIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="queryOrg4Statistics" resultType="com.shuidihuzhu.cf.clewtrack.model.OptionModel">
        select distinct org_id as showValue,org_name as showKey
        from <include refid="tableName"/>
        where date_time=#{dateTime}
        <if test="waiBaoUserIdList != null and waiBaoUserIdList.size() != 0">
            and user_id in  <foreach collection="waiBaoUserIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
        </if>
        and is_delete=0 and org_id > 0
    </select>

</mapper>
