<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewManualAssignInfoDao">

    <sql id="file">
    id, assign_id, task_id, user_id, user_name, encrypt_phone, create_time, update_time, 
    is_delete,task_status
  </sql>
    <sql id="fileName">
    cf_clew_manual_assign_info
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignInfoDO">
        select
        <include refid="file"/>
        from <include refid="fileName"/>
        where id = #{id}
    </select>
    <select id="selectGroupByAssignIdAndUserName"
            resultType="com.shuidihuzhu.cf.clewtrack.model.CfClewAssignInfoModel">
        SELECT assign_id as assignId,user_name as acceptor,count(*) as assignCount FROM
        <include refid="fileName"/>
        where is_delete = 0
        and task_status=0
        and assign_id in
        <foreach collection="idList" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        GROUP BY assign_id, user_name
    </select>
    <select id="selectNotAssignByAssignId"
            resultType="com.shuidihuzhu.cf.clewtrack.model.CfClewAssignInfoModel">
        SELECT assign_id as assignId,user_name as acceptor,count(*) as assignCount FROM
        <include refid="fileName"/>
        where is_delete = 0
        and task_status=1
        and assign_id in
        <foreach collection="idList" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
        GROUP BY assign_id, user_name
    </select>
    <select id="selectByAssignId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignInfoDO">
        select
        <include refid="file"/>
        from
        <include refid="fileName"/>
        where is_delete =0
        and task_status=0
        and assign_id =#{assignId}
    </select>
    <select id="selectAssignRecordByUserIdAndAssignId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignInfoDO">
        select <include refid="file"/>
        from <include refid="fileName"/>
        where  is_delete =0
        and task_status=0
        <if test="assignId != null">
            and assign_id = #{assignId}
        </if>
        <if test="userId != null and userId !=''">
            and user_id =#{userId}
        </if>
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignInfoDO" useGeneratedKeys="true">
        insert into cf_clew_manual_assign_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assignId != null">
                assign_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="encryptPhone != null">
                encrypt_phone,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="taskStatus!=null">
                task_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assignId != null">
                #{assignId},
            </if>
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="userName != null">
                #{userName},
            </if>
            <if test="encryptPhone != null">
                #{encryptPhone},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
            <if test="taskStatus !=null">
                #{taskStatus}
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewManualAssignInfoDO">
        update cf_clew_manual_assign_info
        <set>
            <if test="assignId != null">
                assign_id = #{assignId},
            </if>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="encryptPhone != null">
                encrypt_phone = #{encryptPhone},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="taskStatus!=null">
                task_status = #{taskStatus},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>