<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewRecordContentDao">

    <sql id="TABLE_NAME">
        cf_clew_record_content
    </sql>
    <sql id="base_result">
        id,clew_id,task_id,create_time,`type`,type_desc,content,operator_name
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRecordContentDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into cf_clew_record_content
    (clew_id,content,operator_name,task_id,`type`,type_desc)
    values (#{clewId},#{content},#{operatorName},#{taskId},#{type},#{typeDesc})
    </insert>
    <insert id="batchInsert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRecordContentDO"
            useGeneratedKeys="true" keyProperty="id">
    insert into cf_clew_record_content
    (clew_id,content,operator_name,task_id,`type`,type_desc)
    values
    <foreach collection="recordContentDOList" item="recordContentDO" separator=",">
        (#{recordContentDO.clewId},#{recordContentDO.content},#{recordContentDO.operatorName},#{recordContentDO.taskId},#{recordContentDO.type},#{recordContentDO.typeDesc})
    </foreach>
    </insert>
    <select id="getRecordContentsByClewId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRecordContentDO">
        select <include refid="base_result"/> from <include refid="TABLE_NAME"/>
        where clew_id=#{clewId}
        order by id desc
        limit #{offset},#{pageSize}
    </select>
    <select id="getCountRecordContentsByClewId" resultType="java.lang.Long">
        select count(*) from <include refid="TABLE_NAME"/>
        where clew_id=#{clewId}
    </select>
    <select id="getRecordContentByClewId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRecordContentDO">
        select <include refid="base_result"/> from <include refid="TABLE_NAME"/>
        where clew_id=#{clewId}
        order by id desc
        limit 1;
    </select>
    <select id="getCfClewRecordContentDOByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRecordContentDO">
        select <include refid="base_result"/> from <include refid="TABLE_NAME"/>
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </select>
    <select id="getRecordContentByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.model.ClewRecordContentListModel">
        select c.clew_id as clew_id,c.content as content from cf_clew_record_content as c
          inner join
          (
            select max(id) as id from cf_clew_record_content where `type` = #{type}
            and clew_id in
            <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
                #{clewId}
            </foreach>
            group by clew_id
            limit #{size}
          ) as t
        on c.id = t.id;
    </select>
    <select id="getRecordContentByClewIdsAndType" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRecordContentDO">
        select <include refid="base_result"/> from <include refid="TABLE_NAME"/>
        where `type` = #{type}
        and clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
    </select>
    <select id="getFollowUpRecordByClewId" resultType="com.shuidihuzhu.cf.clewtrack.model.bdcrm.ClewFollowUpRecordModel">
        select clew_id,`type`,type_desc,content,create_time,operator_name
        from <include refid="TABLE_NAME"/>
        where clew_id=#{clewId}
        order by update_time desc
        <if test="offset!=null and pageSize!=null">
            limit #{offset},#{pageSize}
        </if>
    </select>
    <select id="getCountFollowUpRecordByClewId" resultType="java.lang.Long">
        select count(*)
        from <include refid="TABLE_NAME"/>
        where clew_id=#{clewId}
    </select>

    <select id="getLatestRecordContentByClewId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewRecordContentDO">
        select <include refid="base_result"/> from <include refid="TABLE_NAME"/>
        where clew_id=#{clewId} and `type` = #{type}
        order by id desc
        limit 1
    </select>

    <select id="getRecordContentsByTaskId" resultType="com.shuidihuzhu.cf.clewtrack.model.bdcrm.ClewFollowUpRecordModel">
        select `clew_id`, `task_id`, `type`, type_desc, content, create_time, operator_name
        from <include refid="TABLE_NAME"/>
        where task_id = #{taskId}
        and is_delete = 0
    </select>

</mapper>
