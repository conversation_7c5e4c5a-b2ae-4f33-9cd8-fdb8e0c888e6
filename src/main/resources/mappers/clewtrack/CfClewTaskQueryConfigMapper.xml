<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTaskQueryConfigDAO">

    <sql id="baseResult">
        id,
        query_config,
        create_time,
        update_time,
        is_delete
    </sql>

    <sql id="tableName">cf_clew_task_query_config</sql>

    <select id="getConfig" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskQueryConfigDO">
        SELECT <include refid="baseResult"/>
        FROM cf_clew_task_query_config
        WHERE is_delete = 0 limit 1
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskQueryConfigDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (query_config)
        VALUES (#{queryConfig})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskQueryConfigDO">
        UPDATE cf_clew_task_query_config
        SET query_config = #{queryConfig}
        WHERE id = #{id} AND is_delete = 0
    </update>

</mapper>