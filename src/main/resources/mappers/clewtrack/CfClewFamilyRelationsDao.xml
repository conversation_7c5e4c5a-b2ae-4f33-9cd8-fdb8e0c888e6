<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewFamilyRelationsDao">
    <sql id="tableName">cf_clew_family_relations</sql>

    <sql id="fields">
        `id`,
        `phone`,
        `relationship`,
        `occupation`,
        `has_forward`,
        `nick_name`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.model.CfClewFamilyRelationsModel" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test ="phone != null and phone != ''">
                `phone`,
            </if>
            <if test ="relationship != null and relationship != ''">
                `relationship`,
            </if>
            <if test ="occupation != null and occupation != ''">
                `occupation`,
            </if>
            <if test ="hasForward != null">
                `has_forward`,
            </if>
            <if test ="nickName != null and nickName != ''">
                `nick_name`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test ="phone != null and phone != ''">
                #{phone},
            </if>
            <if test ="relationship!= null and relationship!= ''">
                #{relationship},
            </if>
            <if test ="occupation!= null and occupation!= ''">
                #{occupation},
            </if>
            <if test ="hasForward!= null">
                #{hasForward},
            </if>
            <if test ="nickName!= null and nickName!= ''">
                #{nickName},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.clewtrack.model.CfClewFamilyRelationsModel">
        update <include refid="tableName"/>
        <set>
            <if test ="relationship != null and relationship != ''">
                `relationship`= #{relationship},
            </if>
            <if test ="occupation != null and occupation != ''">
                `occupation`= #{occupation},
            </if>
            <if test ="hasForward != null">
                `has_forward`= #{hasForward},
            </if>
            <if test ="nickName != null and nickName != ''">
                `nick_name`= #{nickName},
            </if>
        </set>
        where `id`= #{id}
    </update>

    <update id="deleteRelationById" parameterType="java.lang.Long">
        update <include refid="tableName"/>
        <set>
            is_delete = 1
        </set>
        where `id`= #{id}
    </update>

    <select id="queryRelationByPhones" resultType = "com.shuidihuzhu.cf.clewtrack.domain.CfClewFamilyRelationsDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where `is_delete` = 0
        and `phone` in
        <foreach collection="phoneList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>