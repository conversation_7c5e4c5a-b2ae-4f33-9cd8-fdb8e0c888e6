<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfWxCorpBindClewuserInfoDao">
    <sql id="tableName">cf_wx_corp_bind_clewuser_info</sql>

    <sql id="fields">
        `id`,
	    `user_id`,
        `corp_name`,
        `corp_id`,
	    `qy_wechat_user_id`,
	    `is_show`,
	    `head_url`,
        `qy_wechat_qr_code`,
        `create_time`,
        `update_time`,
        `is_delete`,
        `is_send_msg`,
        `user_name`,
        `alias_name`,
        `status`,
        `bind_status`,
        `encrypt_phone`,
        `qr_code_real`,
        `open_userid`,
        `department_details`
    </sql>
    <sql id="insertField">
        `corp_name`, `corp_id`, `user_name`, `qy_wechat_user_id`, `alias_name`, `status`, `encrypt_phone`, `head_url`, `qy_wechat_qr_code`, `qr_code_real`, `open_userid`, `department_details`
    </sql>
    <insert id="batchInsert">
        insert into <include refid="tableName"/>
        (<include refid="insertField"/>)
        values
        <foreach collection="modelList" item="model" separator=",">
            (#{model.corpName},#{model.corpId},#{model.userName},#{model.qyWechatUserId},#{model.aliasName},#{model.status},
            #{model.encryptPhone},#{model.headUrl},#{model.qyWechatQrCode},#{model.qrCodeReal},#{model.openUserid},#{model.departmentDetails})
        </foreach>
    </insert>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="corpName != null and corpName != ''">
                corp_name,
            </if>
            <if test="corpId != null and corpId != ''">
                corp_id,
            </if>
            <if test="qyWechatUserId != null and qyWechatUserId != ''">
                qy_wechat_user_id,
            </if>
            <if test="isShow != null">
                is_show,
            </if>
            <if test="headUrl != null and headUrl != ''">
                head_url,
            </if>
            <if test="qyWechatQrCode != null and qyWechatQrCode != ''">
                qy_wechat_qr_code,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDelete!=null">
                is_delete,
            </if>
            <if test="isSendMsg != null">
                is_send_msg,
            </if>
            <if test="userName!=null and userName!='' ">`user_name`,</if>
            <if test="aliasName!=null and aliasName!='' ">`alias_name`,</if>
            <if test="status!=null">`status`,</if>
            <if test="bindStatus!=null  ">`bind_status`,</if>
            <if test="encryptPhone!=null and encryptPhone!='' ">`encrypt_phone`,</if>
            <if test="qrCodeReal!=null and qrCodeReal!='' ">`qr_code_real`,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                #{userId} ,
            </if>
            <if test="corpName != null and corpName != ''">
                #{corpName} ,
            </if>
            <if test="corpId != null and corpId != ''">
                #{corpId} ,
            </if>
            <if test="qyWechatUserId != null and qyWechatUserId != ''">
                #{qyWechatUserId} ,
            </if>
            <if test="isShow != null">
                #{isShow} ,
            </if>
            <if test="headUrl != null and headUrl != ''">
                #{headUrl} ,
            </if>
            <if test="qyWechatQrCode != null and qyWechatQrCode != ''">
                #{qyWechatQrCode} ,
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isDelete!=null">
                #{isDelete},
            </if>
            <if test="isSendMsg != null">
                #{isSendMsg},
            </if>
            <if test="userName!=null and userName!='' ">#{userName},</if>
            <if test="aliasName!=null and aliasName!='' ">#{aliasName},</if>
            <if test="status!=null">#{status},</if>
            <if test="bindStatus!=null  ">#{bindStatus},</if>
            <if test="encryptPhone!=null and encryptPhone!='' ">#{encryptPhone},</if>
            <if test="qrCodeReal!=null and qrCodeReal!='' ">#{qrCodeReal},</if>
        </trim>
    </insert>

    <update id="updateBindQywechatInfo" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        update <include refid="tableName"/>
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId} ,
            </if>
            <if test="corpName != null and corpName != ''">
                corp_name = #{corpName} ,
            </if>
            <if test="corpId != null and corpId != ''">
                corp_id = #{corpId} ,
            </if>
            <if test="qyWechatUserId != null">
                qy_wechat_user_id = #{qyWechatUserId} ,
            </if>
            <if test="isShow != null">
                is_show = #{isShow} ,
            </if>
            <if test="headUrl != null">
                head_url = #{headUrl} ,
            </if>
            <if test="qyWechatQrCode != null">
                qy_wechat_qr_code = #{qyWechatQrCode} ,
            </if>
            <if test="isDelete!=null">
                is_delete = #{isDelete},
            </if>
            <if test="isSendMsg!=null">
                is_send_msg = #{isSendMsg},
            </if>
            <if test="userName!=null and userName!='' ">user_name = #{userName},</if>
            <if test="aliasName!=null and aliasName!='' ">alias_name = #{aliasName},</if>
            <if test="status!=null">status = #{status},</if>
            <if test="bindStatus!=null  ">bind_status = #{bindStatus},</if>
            <if test="encryptPhone!=null and encryptPhone!='' ">encrypt_phone = #{encryptPhone},</if>
            <if test="qrCodeReal!=null and qrCodeReal!='' ">qr_code_real = #{qrCodeReal},</if>
        </set>
        where id=#{id}
    </update>
 <update id="updateBindQywechatInfoByCorpIdWithQyWechatUserId" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        update <include refid="tableName"/>
        <set>
            <if test="userName!=null and userName!='' ">user_name = #{userName},</if>
            <if test="aliasName!=null and aliasName!='' ">alias_name = #{aliasName},</if>
            <if test="status!=null">status = #{status},</if>
            <if test="headUrl != null"> head_url = #{headUrl} , </if>
            <if test="qyWechatQrCode != null"> qy_wechat_qr_code = #{qyWechatQrCode} , </if>
            <if test="qrCodeReal!=null and qrCodeReal!='' ">qr_code_real = #{qrCodeReal},</if>
            <if test="encryptPhone!=null and encryptPhone!='' ">encrypt_phone = #{encryptPhone},</if>
            <if test="openUserid!=null and openUserid!='' ">open_userid = #{openUserid},</if>
            <if test="departmentDetails!=null and departmentDetails!='' ">department_details = #{departmentDetails},</if>
        </set>
        where qy_wechat_user_id = #{qyWechatUserId} and corp_id = #{corpId}
 </update>

    <select id="getCorpBindUserInfoByUserId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
    </select>

    <select id="getQyWechatQrCodeByUserId" resultType="java.lang.String">
        select qy_wechat_qr_code from <include refid="tableName"/>
        where is_delete = 0
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
        <if test="qyWechatUserId != null and qyWechatUserId != ''">
            and qy_wechat_user_id = #{qyWechatUserId}
        </if>
    </select>

    <select id="getBindQywechatInfoById"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and id = #{id}
    </select>

    <select id="getCorpBindUserInfoByCorpIdAndQyWechatUserId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and corp_id = #{corpId} and qy_wechat_user_id = #{qyWechatUserId}
        limit 1
    </select>

    <select id="getCorpBindUserInfoByUserIdAndCorpIdAndQyWechatId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and user_id = #{userId} and corp_id = #{corpId} and qy_wechat_user_id = #{qyWechatUserId}
    </select>

    <select id="getBindQywechatInfoByUserIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and is_show = 1
        and user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <select id="getCanSendMsgUserIdList"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0 and user_id in <foreach collection="userIdList" item="userId" open="(" separator="," close=")">#{userId}</foreach>
        and is_send_msg = 1
    </select>

    <select id="getCanSendMsgCorpIdList"
            resultType="java.lang.String">
        select distinct corp_id
        from <include refid="tableName"/>
        where is_delete = 0 and corp_id in <foreach collection="corpIdList" item="corpId" open="(" separator="," close=")">#{corpId} </foreach>
        and qy_wechat_user_id = #{qyWechatUserId}
        and is_send_msg = 1
    </select>
    <select id="getQixinhao" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/> from <include refid="tableName"/>
        where is_delete = 0
        <if test="cfQywxSearchParam.status!=-1 and cfQywxSearchParam.status!=null">and status=#{cfQywxSearchParam.status}</if>
        <if test="cfQywxSearchParam.bindStatus!=-1  and cfQywxSearchParam.bindStatus!=null">and bind_status=#{cfQywxSearchParam.bindStatus}</if>
        <if test="cfQywxSearchParam.corpId!='-1' and cfQywxSearchParam.corpId!=null">and corp_id=#{cfQywxSearchParam.corpId}</if>
        <if test="cfQywxSearchParam.userName!='' and cfQywxSearchParam.userName!=null">and user_name=#{cfQywxSearchParam.userName}</if>
        <if test="cfQywxSearchParam.qyWechatUserId!='' and cfQywxSearchParam.qyWechatUserId!=null ">and qy_wechat_user_id=#{cfQywxSearchParam.qyWechatUserId}</if>
        order by create_time desc
        limit #{offset},#{cfQywxSearchParam.pageSize}
    </select>
    <select id="getQixinhaoCount" resultType="java.lang.Long">
        select count(*) from <include refid="tableName"/>
        where is_delete = 0
        <if test="cfQywxSearchParam.status!=null and cfQywxSearchParam.status!=-1">and status=#{cfQywxSearchParam.status}</if>
        <if test="cfQywxSearchParam.bindStatus!=null and cfQywxSearchParam.bindStatus!=-1">and bind_status=#{cfQywxSearchParam.bindStatus}</if>
        <if test="cfQywxSearchParam.corpId!=null and cfQywxSearchParam.corpId!='-1'">and corp_id=#{cfQywxSearchParam.corpId}</if>
        <if test="cfQywxSearchParam.userName!=null and cfQywxSearchParam.userName!=''">and user_name=#{cfQywxSearchParam.userName}</if>
        <if test="cfQywxSearchParam.qyWechatUserId!=null and cfQywxSearchParam.qyWechatUserId!=''">and qy_wechat_user_id=#{cfQywxSearchParam.qyWechatUserId}</if>
    </select>
    <select id="getExistQyWechatIdList" resultType="java.lang.String">
        select qy_wechat_user_id from <include refid="tableName"/>
        where is_delete = 0 and corp_id=#{corpId} and qy_wechat_user_id in <foreach collection="qyWechatIds" item="qyWechatId" open="(" close=")" separator=",">#{qyWechatId}</foreach>
    </select>
    <update id="unBind">
        update <include refid="tableName"/>
        set user_id = '' , bind_status = 0
        where id = #{id}
    </update>
    <select id="getBindQywechatInfoByCorpIdWithQyWechatUserId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/> from <include refid="tableName"/>
        where is_delete = 0 and corp_id=#{corpId}
        and qy_wechat_user_id=#{qyWechatUserId}
        limit 1
    </select>

    <select id="getBindQywechatInfoByQyWxUserIdAndCorpId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/> from <include refid="tableName"/>
        where is_delete = 0 and bind_status = 1 and corp_id=#{corpId}
        and qy_wechat_user_id=#{qyWechatUserId}
        limit 1
    </select>

    <select id="listByUserIds"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and user_id in
        <foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <select id="getBindQyWechatInfoByCorpId"
            resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where is_delete = 0
        and corp_id = #{corpId}
        and qy_wechat_user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
    </select>

    <select id="listByUserIdsAndCorpId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfWxCorpBindClewuserInfoDo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where is_delete = 0
        and user_id in
        <foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        and corp_id = #{corpId}
    </select>
</mapper>
