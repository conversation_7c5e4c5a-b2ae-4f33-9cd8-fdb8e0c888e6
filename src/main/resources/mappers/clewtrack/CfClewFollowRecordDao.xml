<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewFollowRecordDao">

    <sql id="baseResult">
        `id`,
        `clew_id`,
        `task_id`,
        `volunteer_unique_code`,
        `feed_back_record_second_level`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <sql id="tableName">cf_clew_follow_record</sql>

    <insert id="insertCfClewFollowRecord" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clewId != null">
                clew_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="volunteerUniqueCode != null and volunteerUniqueCode != ''">
                volunteer_unique_code,
            </if>
            <if test="feedBackRecordSecondLevel != null and feedBackRecordSecondLevel != ''">
                feed_back_record_second_level,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clewId != null">
                #{clewId},
            </if>
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="volunteerUniqueCode != null and volunteerUniqueCode != ''">
                #{volunteerUniqueCode},
            </if>
            <if test="feedBackRecordSecondLevel != null and feedBackRecordSecondLevel != ''">
                #{feedBackRecordSecondLevel},
            </if>
        </trim>
    </insert>

    <select id="getCfClewFollowRecordByClewId" resultType="com.shuidihuzhu.cf.clewtrack.domain.bdcrm.CfClewFollowRecordDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where task_id = #{taskId}
        and is_delete = 0
    </select>

    <update id="updateFollowLevel">
        update <include refid="tableName"/>
        set feed_back_record_second_level = #{feedBackRecordSecondLevel}
        where task_id = #{taskId}
        and is_delete = 0
    </update>

    <select id="getCfClewFollowRecordByClewIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.bdcrm.CfClewFollowRecordDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where clew_id in
        <foreach collection="clewIds" item="clewId" open="(" separator="," close=")">
            #{clewId}
        </foreach>
        and is_delete = 0
    </select>

    <select id="getVolunteerTransformClewRecord"
            resultType="com.shuidihuzhu.client.cf.clewtrack.model.CfClewVolunteerFollowCountModel">
        select volunteer_unique_code as `unique_code`, sum(case when feed_back_record_second_level in
        <foreach collection="remoteServiceTypeList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        then 1 else 0 end) as `un_face_to_face_count`
        from <include refid="tableName"/>
        where is_delete = 0
        <if test="uniqueCodeList != null and uniqueCodeList.size != 0">
            and volunteer_unique_code in
            <foreach collection="uniqueCodeList" open="(" close=")" separator="," item="uniqueCode">
                #{uniqueCode}
            </foreach>
        </if>
        and create_time between #{startDate} and #{endDate}
        group by volunteer_unique_code
    </select>


</mapper>
