<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewCallRecordsExtDao">

  <sql id="filed">
    id, call_id, score, score_date, score_name, is_delete, create_time, update_time,score_mis,call_resource
  </sql>

  <sql id="fileName">
    cf_clew_call_records_ext
  </sql>
  <select id="selectByCallId" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsExtDO">
    select
    <include refid="filed" />
    from <include refid="fileName"/>
    where  call_id = #{callId} and call_resource = #{callResource}
    limit 1
  </select>

  <select id="listByCallIds" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsExtDO">
    select
    <include refid="filed" />
    from <include refid="fileName"/>
    where call_id in
    <foreach collection="callIds" item="callId" open="(" separator="," close=")">
      #{callId}
    </foreach>
     and call_resource = #{callResource}
  </select>

  <select id="selectByParam" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsExtDO">
    select <include refid="filed"/>
    from <include refid="fileName"/>
    where is_delete =0
    <if test="param.scoreStartTime != null">
    and  score_date <![CDATA[ >= ]]> #{param.scoreStartTime}
    </if>
    <if test="param.scoreEndTime !=null">
    and  score_date <![CDATA[ <= ]]>  #{param.scoreEndTime}
    </if>
    <if test="param.scoreFrom != null">
     and score  <![CDATA[ >= ]]>  #{param.scoreFrom}
    </if>
    <if test="param.scoreTo != null">
     and score <![CDATA[ <= ]]> #{param.scoreTo}
    </if>
      order by score desc, score_date desc
    <if test="param.offSet != null">
      limit #{param.offSet} ,#{param.pageSize}
    </if>
  </select>
  <select id="selectNumByParam" resultType="java.lang.Integer">
    select count(*)
    from <include refid="fileName"/>
    where is_delete =0
    <if test="param.scoreStartTime != null">
     and score_date <![CDATA[ >= ]]> #{param.scoreStartTime}
    </if>
    <if test="param.scoreEndTime !=null">
     and score_date <![CDATA[ <= ]]>  #{param.scoreEndTime}
    </if>
    <if test="param.scoreFrom != null">
     and score  <![CDATA[ >= ]]>  #{param.scoreFrom}
    </if>
    <if test="param.scoreTo != null">
     and score <![CDATA[ <= ]]> #{param.scoreTo}
    </if>
  </select>


  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsExtDO" useGeneratedKeys="true">
    insert into <include refid="fileName"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="callId != null">
        call_id,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="scoreDate != null">
        score_date,
      </if>
      <if test="scoreName != null">
        score_name,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="scoreMis !=null">
        score_mis,
      </if>
      <if test="callResource !=null">
        call_resource,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="callId != null">
        #{callId},
      </if>
      <if test="score != null">
        #{score},
      </if>
      <if test="scoreDate != null">
        #{scoreDate},
      </if>
      <if test="scoreName != null">
        #{scoreName},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="scoreMis!=null">
        #{scoreMis},
      </if>
      <if test="callResource !=null">
        #{callResource},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsExtDO">
    update <include refid="fileName"/>
    <set>
      <if test="callId != null">
        call_id = #{callId},
      </if>
      <if test="score != null">
        score = #{score},
      </if>
      <if test="scoreDate != null">
        score_date = #{scoreDate},
      </if>
      <if test="scoreName != null">
        score_name = #{scoreName},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="scoreMis !=null">
        score_mis = #{scoreMis},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateById" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsExtDO">
    update <include refid="fileName"/>
    <set>
      <if test="score != null">
        score = #{score},
      </if>
      <if test="scoreDate != null">
        score_date = #{scoreDate},
      </if>
      <if test="scoreName != null">
        score_name = #{scoreName},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="scoreMis !=null">
        score_mis = #{scoreMis},
      </if>
      <if test="callResource !=null">
        call_resource = #{callResource},
      </if>
    </set>
    where id = #{id}
  </update>

</mapper>
