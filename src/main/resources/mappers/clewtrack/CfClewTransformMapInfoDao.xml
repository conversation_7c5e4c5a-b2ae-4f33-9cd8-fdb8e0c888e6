<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfClewTransformMapInfoDao">

    <sql id="baseResult">
        `id`,
        `hospital_name`,
        `city_name`,
        `partition_name`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>

    <sql id="tableName">cf_clew_transform_map_info</sql>

    <select id="getTransformMapInfo" resultType="com.shuidihuzhu.cf.clewtrack.domain.CfClewTransformMapInfoDO">
        select <include refid="baseResult"/>
        from <include refid="tableName"/>
        where is_delete = 0
        <if test="hospitalName != null and hospitalName != ''">
            and hospital_name = #{hospitalName}
        </if>
        <if test="cityName != null and cityName != ''">
            and city_name = #{cityName}
        </if>
    </select>


</mapper>
