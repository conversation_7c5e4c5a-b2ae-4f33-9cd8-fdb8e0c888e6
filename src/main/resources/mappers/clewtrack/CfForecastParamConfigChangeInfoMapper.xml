<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.CfForecastParamConfigChangeInfoDao">

    <sql id="tableName">cf_forecast_parameter_change_info</sql>

    <sql id="baseResult">
      `id` ,
      `paramconfig_id`,
      `origin_start_time`,
      `origin_end_time`,
      `origin_answer_rate`,
      `origin_predict_adjust`,
      `mod_start_time`,
      `mod_end_time`,
      `mod_answer_rate`,
      `mod_predict_adjust`,
      `user_id`,
      `create_time`,
      `update_time`,
      `is_delete`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.clewtrack.domain.CfForecastParamConfigChangeInfoDo"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="paramconfigId != null">
                paramconfig_id,
            </if>
            <if test="originStartTime != null ">
                origin_start_time,
            </if>
            <if test="originEndTime != null">
                origin_end_time,
            </if>
            <if test="originAnswerRate != null">
                origin_answer_rate,
            </if>
            <if test="originPredictAdjust != null">
                origin_predict_adjust,
            </if>
            <if test="modStartTime != null">
                mod_start_time,
            </if>
            <if test="modEndTime != null">
                mod_end_time,
            </if>
            <if test="modAnswerRate != null">
                mod_answer_rate,
            </if>
            <if test="modPredictAdjust != null">
                mod_predict_adjust,
            </if>
            <if test="userId != null">
                user_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="paramconfigId != null">
                #{paramconfigId} ,
            </if>
            <if test="originStartTime != null ">
                #{originStartTime} ,
            </if>
            <if test="originEndTime != null">
                #{originEndTime} ,
            </if>
            <if test="originAnswerRate != null">
                #{originAnswerRate} ,
            </if>
            <if test="originPredictAdjust != null">
                #{originPredictAdjust} ,
            </if>
            <if test="modStartTime != null">
                #{modStartTime} ,
            </if>
            <if test="modEndTime != null">
                #{modEndTime} ,
            </if>
            <if test="modAnswerRate != null">
                #{modAnswerRate} ,
            </if>
            <if test="modPredictAdjust != null">
                #{modPredictAdjust} ,
            </if>
            <if test="userId != null">
                #{userId} ,
            </if>
        </trim>
    </insert>


</mapper>
