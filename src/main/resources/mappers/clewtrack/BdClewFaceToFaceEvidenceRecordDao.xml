<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.clewtrack.BdClewFaceToFaceEvidenceRecordDao">

    <sql id="tableFiled">
        `id`,
        `clew_id`,
        `unique_code`,
        `face_to_face_evidence`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>
    <sql id="tableName">
        bd_clew_face_to_face_evidence_record
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="tableName"/>
        (`clew_id`, `unique_code`, `face_to_face_evidence`, `task_id`)
        values
        (#{clewId}, #{uniqueCode}, #{faceToFaceEvidence}, #{taskId})
    </insert>

    <select id="getByTaskId" resultType="com.shuidihuzhu.cf.clewtrack.domain.bdcrm.BdClewFaceToFaceEvidenceRecordDO">
        select <include refid="tableFiled"/>
        from <include refid="tableName"/>
        where task_id = #{taskId}
        and is_delete = 0
        order by id desc
        limit 1
    </select>


</mapper>