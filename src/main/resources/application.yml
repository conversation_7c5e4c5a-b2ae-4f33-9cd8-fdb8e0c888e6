server:
  port: 8186

regCenter:
  #  serverList: 127.0.0.1:2181
  namespace: cf-clewtrack-api


auth:
  saas:
    appCode: sl56s1sw
    login:
      interceptorEnable: true
      required-path:
        - /api/cf/clewtrack/sea/**
        - /api/cf/clewtrack/clew-pool/**
        - /api/cf/clewtrack/crm-undertake-clew/**
        - /api/cf/clewtrack/clewlayer/**
        - /api/cf/clewtrack/cf-call-common/**
        - /api/cf/clewtrack/myClewRemind*/**
      white-list-path:
        - /innerapi/cf/*/**
        - /api/cf/clewtrack/bdcrm*/**
        - /api/cf/clewtrack/bdcrm-clew/get-follow-up-record
        - /api/cf/clewtrack/qywxapp/*/**
        - /api/cf/clewtrack/qywxapp/common/get-token
    permission:
      interceptorEnable: true
      required-path:
        - /api/cf/clewtrack/**
        - /api/cf/clewtrack/sea/**
        - /api/cf/clewtrack/clew-pool/**
        - /api/cf/clewtrack/crm-undertake-clew/**
        - /api/cf/clewtrack/clewlayer/**
      white-list-path:
        - /innerapi/cf/*/**
        - /api/cf/clewtrack/bdcrm*/**
        - /api/cf/clewtrack/bdcrm-clew/get-follow-up-record
        - /api/cf/clewtrack/qywxapp/*/**
        - /api/cf/clewtrack/qywxapp/common/get-token

spring:
  main:
    allow-circular-references: true
  config:
    use-legacy-processing: true


web:
  wrapper:
    argument-resolver:
      enable: true