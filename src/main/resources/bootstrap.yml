spring:
  application:
    name: @app.name@
    version: @project.version@
  profiles:
    active: debug
  cloud:
    config:
      enabled: true
      uri: http://configserver.shuiditech.com
      label: develop
  main:
    allow-bean-definition-overriding: true
logging:
  pattern:  # 增加logging.pattern，目前暂时不生效，等未来完全切换到kube环境后删除logback-spring.xml文件即可生效
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSSZ}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%t]){faint} %clr(%logger{39}.%M:%L){cyan} %m%n"
  file:
    path: ${app.log.path:${user.home}/logs}
spring.main.allow-bean-definition-overriding: true