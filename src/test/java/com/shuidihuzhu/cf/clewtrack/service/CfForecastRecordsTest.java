package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO;
import com.shuidihuzhu.cf.clewtrack.enums.TianRunEnums;
import com.shuidihuzhu.cf.clewtrack.facade.ICfFundraiseDemandFacade;
import com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel;
import com.shuidihuzhu.cf.dao.clewtrack.CfForecastCallRecordsDao;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-10-10
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class CfForecastRecordsTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
    @Autowired
    private ICfForecastCallRecordService cfForecastCallRecordService;
    @Autowired
    private CfForecastCallRecordsDao cfForecastCallRecordsDao;
    @Autowired
    private ICfClewTaskReadService cfClewTaskReadServiceImpl;
    @Autowired
    private ICfClewCallRecordsService cfClewCallRecordsService;
    @Autowired
    private ICfClewCallRecordsService clewCallRecordsService;
    @Autowired
    private ICfFundraiseDemandFacade cfFundraiseDemandFacade;

    @Test
    public void badTelQuery(){
        Date startDate = DateUtil.addDay(DateUtil.getCurrentDate(),-20);
        Date endDate = DateUtil.getCurrentDate();
        List<CfForecastCallRecordsDO> cfForecastCallRecordsDOS = cfForecastCallRecordService.getCfForecastCallRecordsByCreatTime("",startDate,endDate);
        log.info("cfForecastCallRecordsDOS:{}",cfForecastCallRecordsDOS);
    }

    @Test
    public void query(){
        Date startTime = DateUtil.getCurrentDate();
        Date endTime = DateUtil.addDay(startTime,1);
        List<CfForecastCallRecordsDO> forecastHandleClewList = cfForecastCallRecordsDao.getCfForecastCallRecordsByCreatTime(null,startTime,endTime);
        log.info("forecastHandleClewList:{}",forecastHandleClewList.size());
    }

    @Test
    public void getIds(){
        List<Long> ids = cfForecastCallRecordService.getRecordsIdsByTaskId(2451);
        log.info("ids size:{}",ids.size());
    }

    @Test
    public void batchUpdateTaskIdAndIsDeleteByIds(){
        List<Long> ids = cfForecastCallRecordService.getRecordsIdsByTaskId(2486);
        cfForecastCallRecordService.batchUpdateTaskIdAndIsImportByIds(null,ids);
    }

    @Test
    public void getRecallPredictiveCallPhone(){
        cfForecastCallRecordService.getRecallPredictiveCallPhone(300);
    }

    @Test
    public void addBatchCfForecastCallRecord(){
        List<TaskPhoneModel> onlineTaskPhoneModelList = cfClewTaskReadServiceImpl.getRealTimePredictiveCallPhone(2);
        cfForecastCallRecordService.batchSaveRecordByTaskId(-1,onlineTaskPhoneModelList,0);
    }

    @Test
    public void  saveCallRecords(){
        CfClewCallRecordsDO cfClewCallRecordsDO = CfClewCallRecordsDO.builder()
                .userId("lichengjin")
                .uniqueId("e212331231")
                .customerNumber("18310535756")
                .status(TianRunEnums.TianRunCnoStatusEnum.RINGING.getStatus())
                .build();
        cfClewCallRecordsService.saveCallRecords(cfClewCallRecordsDO);
    }

    @Test
    public void  queryForecastCallRecords(){
        List<CfForecastCallRecordsDO> cfForecastCallRecordsDOList = cfForecastCallRecordService.getCfForecastCallRecordsByTaskId(Sets.newHashSet(2842,2848));
        log.info("cfForecastCallRecordsDOList:{}",cfForecastCallRecordsDOList);
        Date date = DateUtil.getCurrentDate();
        CfForecastCallRecordsDO cfForecastCallRecordsDO = cfForecastCallRecordService.getLatestCfForecastCallRecordsByCreatTimeAndPhone(date,"14001300687");
        log.info("cfForecastCallRecordsDO:{}",cfForecastCallRecordsDO);
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDay(DateUtil.getCurrentDate(),1);
        Set<String> phones = cfForecastCallRecordService.getImportedPhonesByCreatTime(2842,startDate,endDate);
        log.info("phones:{}",phones);
        List<TaskPhoneModel> taskPhoneModelList = cfForecastCallRecordService.getRecallPredictiveCallPhone(100);
        log.info("taskPhoneModelList:{}",taskPhoneModelList);
    }

    @Test
    public void  queryCallRecords(){
        List<CfClewCallRecordsDO> cfClewCallRecordsDOList = cfClewCallRecordsService.getByTaskIdList(Lists.newArrayList(0L));
        log.info("cfClewCallRecordsDOList:{}",cfClewCallRecordsDOList);
        CfClewCallRecordsDO cfClewCallRecordsDO = cfClewCallRecordsService.getCfClewCallRecordsDOByUniqueId("10.10.56.80-1572318720.55131");
        log.info("cfClewCallRecordsDO:{}",cfClewCallRecordsDO);
        CfClewCallRecordsDO cfClewCallRecordsDO1 = cfClewCallRecordsService.getCalledCallRecordsByUserIdAndPhone("lichengjin","18310535756");
        log.info("cfClewCallRecordsDO1:{}",cfClewCallRecordsDO1);
        List<CfClewCallRecordsDO> cfClewCallRecordsDOList1 = cfClewCallRecordsService.getCfClewCallRecordsDOByMobile("13821677335");
        log.info("cfClewCallRecordsDOList1:{}",cfClewCallRecordsDOList1);
    }

    @Test
    public void getInfo(){
        clewCallRecordsService.getCfClewCallRecordsDOByUniqueIds(Lists.newArrayList("10.10.56.80-1575293496.206056"));
    }

    @Test
    public void getPredictiveCallPhone(){
        List<TaskPhoneModel> nightclew = cfClewTaskReadServiceImpl.getPredictiveCallPhone(200);
        List<TaskPhoneModel> realClew= cfClewTaskReadServiceImpl.getRealTimePredictiveCallPhone(200);
        log.info("nightClew:{}", JSON.toJSONString(nightclew));
        log.info("realClew:{}",JSON.toJSONString(realClew));
    }

    @Test
    public void doDemandInvalid(){
        Date date = DateUtil.getCurrentDate();
        cfFundraiseDemandFacade.doDemandAutoInvalid(date);
    }
}
