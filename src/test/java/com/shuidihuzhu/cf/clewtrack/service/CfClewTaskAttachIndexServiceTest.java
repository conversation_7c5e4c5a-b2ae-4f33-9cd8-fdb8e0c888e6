package com.shuidihuzhu.cf.clewtrack.service;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewAttachInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.service.clewindex.ICfClewTaskAttachIndexService;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewAttachInfoDao;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseInfoDao;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewTaskDao;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class) // 指定我们SpringBoot工程的Application启动类
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class
})
@WebAppConfiguration
@Slf4j
public class CfClewTaskAttachIndexServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Autowired
    private ICfClewTaskAttachIndexService cfClewTaskAttachIndexServiceImpl;
    @Autowired
    private CfClewAttachInfoDao cfClewAttachInfoDao;
    @Autowired
    private CfClewTaskDao cfClewTaskDao;
    @Autowired
    private CfClewBaseInfoDao cfClewBaseInfoDao;

    @Test
    public void updateClewTaskAttachIndexByAttachInfo(){
        List<CfClewAttachInfoDO> attachInfoByIds = cfClewAttachInfoDao.getAttachByIds(Sets.newHashSet(1139L));
        cfClewTaskAttachIndexServiceImpl.updateClewTaskAttachIndexByAttachInfo(attachInfoByIds.get(0));
    }

    @Test
    public void updateClewTaskAttachIndexByTaskDO(){
        List<CfClewTaskDO> cfClewTaskDOs = cfClewTaskDao.getTaskByTaskIds(Sets.newHashSet(3201L));
        cfClewTaskAttachIndexServiceImpl.updateClewTaskAttachIndexByTaskDO(cfClewTaskDOs.get(0));
    }

    @Test
    public void updateClewTaskAttachIndexByClewBase(){
        List<CfClewBaseInfoDO> cfClewBaseInfoDOS = cfClewBaseInfoDao.getClewInfoByIds(Sets.newHashSet(2510L));
        cfClewTaskAttachIndexServiceImpl.updateClewTaskAttachIndexByClewBase(cfClewBaseInfoDOS.get(0));
    }

    @Test
    public void addCallNum(){
        cfClewTaskAttachIndexServiceImpl.addCallNum(39986L,2,"1v1服务平台-服务列表","1111");
    }

}

