package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.IQyWxSdkClientDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.FuWuClewWaitAssginDelayConsumer;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.WhClewWaitAssginDelayConsumer;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.fromcfapi.ClewReceiveConsumer;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class QywxSdkClientTest {
    @Autowired
    private IQyWxSdkClientDelegate qyWxSdkClientDelegate;
    @Autowired
    private ClewReceiveConsumer clewReceiveConsumer;
    @Autowired
    private WhClewWaitAssginDelayConsumer whClewWaitAssginDelayConsumer;
    @Autowired
    private FuWuClewWaitAssginDelayConsumer fuWuClewWaitAssginDelayConsumer;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void getToken() {
        String accessToken = qyWxSdkClientDelegate.getAccessToken(0);
        System.err.println("----------accessToken:"+accessToken);
    }

    @Test
    public void sendMessage() {
        String caseId = "123456";
        String mobile = "123456789";
        String realName = "tomcat";
        String rejectContent = "\n求助说明中没有填写患者的疾病，请在求助说明中补充患者疾病名称和病情\n图片展示过少或没有上传，请补充";
        String content = "send msg test 【初审驳回】\n案例ID：" + caseId + "\n发起手机号：" + mobile +"\n患者姓名：" + realName + "\n初审被驳回 \n驳回原因：" + rejectContent;

        OpResult result = qyWxSdkClientDelegate.sendMsgWechatShuidiChou(Lists.newArrayList("lichengjin"), content);
        System.err.println("----------result:" + result);
    }

    @Test
    public void testWhConsumer(){
        ConsumerMessage<CfClewTaskDO> mqMessage = new ConsumerMessage<CfClewTaskDO>();
        CfClewTaskDO cfClewTaskDO = new CfClewTaskDO();
        cfClewTaskDO.setId(35847L);
        mqMessage.setPayload(cfClewTaskDO);
        //id=35847, clewId=28815, packetId=0, createTime=Thu Feb 13 16:21:54 CST 2020, updateTime=Thu Feb 13 16:21:54 CST 2020,
        // taskStatus=0, delayHandle=0, taskType=1, expectLastHandleTime=Thu Feb 13 21:30:00 CST 2020, expectLastAssginTime=Thu Feb 13 16:51:54 CST 2020,
        // assginTime=Thu Feb 13 16:21:54 CST 2020, lastHandleTime=Thu Feb 13 16:21:54 CST 2020, userId=, userName=, teamId=2, isDelete=0,
        // workOrderId=0, isRecycle=0, oldQyWechatUserId=null, sourceType=0, workContentType=0, eagleHandle=7, priority=0, isClosed=0, exeCount=null

        whClewWaitAssginDelayConsumer.handle(mqMessage);
    }

    @Test
    public void testFuwuConsumer(){
        ConsumerMessage<CfClewTaskDO> mqMessage = new ConsumerMessage<CfClewTaskDO>();
        CfClewTaskDO cfClewTaskDO = new CfClewTaskDO();
        cfClewTaskDO.setId(36278L);
        cfClewTaskDO.setTaskType(3);
        cfClewTaskDO.setWorkContentType(70);
        mqMessage.setPayload(cfClewTaskDO);

        fuWuClewWaitAssginDelayConsumer.handle(mqMessage);
    }

}
