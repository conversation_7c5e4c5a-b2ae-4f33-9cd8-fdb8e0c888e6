package com.shuidihuzhu.cf.clewtrack.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewExtInfoDO;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewBaseInfoEnums;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.fromcfapi.ClewReceiveConsumer;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseInfoDao;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewExtInfoDao;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewReceiveModel;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Set;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ClewChannelTest {

    @Autowired
    private ClewReceiveConsumer clewReceiveConsumer;
    @Autowired
    private CfClewBaseInfoDao cfClewBaseInfoDao;
    @Autowired
    private CfClewExtInfoDao cfClewExtInfoDao;
    @Autowired
    private ICfClewReceiveService cfClewReceiveServiceImpl;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Test
    public void testClewChannel() {

        Set<Long> clewIds = Sets.newHashSet(11861L, 11887L, 12371L);
        List<CfClewBaseInfoDO> cfClewBaseInfoDOS = cfClewBaseInfoDao.getClewInfoByIds(clewIds);

        for(CfClewBaseInfoDO clewBaseInfoDO : cfClewBaseInfoDOS) {
            List<CfClewExtInfoDO> cfClewExtInfoDOS = cfClewExtInfoDao.getClewExtInfoByClewIds(Lists.newArrayList(clewBaseInfoDO.getId()));
            CfClewExtInfoDO cfClewExtInfoDO = cfClewExtInfoDOS.get(0);
            //clewReceiveConsumer.reportKuaishouActivate(cfClewExtInfoDO, clewBaseInfoDO);
        }
        System.err.println();

    }

    @Test
    public void testRegister(){
        ClewReceiveModel clewReceiveModel = new ClewReceiveModel();
        clewReceiveModel.setPhone("19010451248");
        clewReceiveModel.setProvince("北京");
        clewReceiveModel.setCity("北京");
        clewReceiveModel.setRaiserPatientRelation(ClewReceiveModel.RaiserPatientRelationEnum.DEFAULT);
        clewReceiveModel.setRegisterName("李成金");
        clewReceiveModel.setDisease("哈哈");
        clewReceiveModel.setHospital("北医三院");
        clewReceiveModel.setDepartment("测试");
        clewReceiveModel.setSickroom("301");
        clewReceiveModel.setSickbed("2");
        clewReceiveModel.setAddbed("01");
        clewReceiveModel.setChannel(CfClewBaseInfoEnums.PrimaryChannelEnum.INSURANCE.getPrimaryChannel()+"insurance");
        clewReceiveModel.setPrimaryKeyChannel(CfClewBaseInfoEnums.PrimaryChannelEnum.INSURANCE.getPrimaryChannel());
        CfClewBaseInfoDO cfClewBaseInfoDO = cfClewReceiveServiceImpl.parseClewBaseInfoDOByReceiveModel(clewReceiveModel);
        OpResult<Boolean> receiveClewOpResult = cfClewReceiveServiceImpl.receiveClewFromInsurance(cfClewBaseInfoDO);
    }

}
