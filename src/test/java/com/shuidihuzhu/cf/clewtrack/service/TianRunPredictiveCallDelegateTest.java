package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.ITianRunPredictiveCallDelegate;
import com.shuidihuzhu.cf.clewtrack.facade.ITianRunPredictiveCallFacade;
import com.shuidihuzhu.cf.clewtrack.model.TaskPhoneModel;
import com.shuidihuzhu.cf.clewtrack.model.tianrun.predictivecall.*;
import com.shuidihuzhu.cf.clewtrack.mq.producer.IMqProducerService;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/7/25 10:23 AM
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TianRunPredictiveCallDelegateTest {
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private ITianRunPredictiveCallDelegate tianRunPredictiveCallDelegate;
    @Autowired
    private ITianRunPredictiveCallFacade tianRunPredictiveCallFacade;
    @Autowired
    private ICfClewInfoService cfClewInfoServiceImpl;
    @Autowired
    private IMqProducerService mqProducerServiceImpl;
    @Autowired
    private ICfClewTaskReadService cfClewTaskReadServiceImpl;

    @Test
    public void testCreate() throws UnsupportedEncodingException {
        TianRunPredictiveCallResultModel<TaskProperty> model = tianRunPredictiveCallDelegate.create("0001","测试任务"+System.currentTimeMillis(), "用于测试",70,80);
        log.error("test create result:{}",model);
        TianRunPredictiveCallResultModel<String> resultModel = tianRunPredictiveCallDelegate.importTaskTel(
                model.getData().getId(),
                Lists.newArrayList("18147668612"),null);
        log.error("test importTaskTel result:{}",resultModel);
        TianRunPredictiveCallResultModel resultModel1 = tianRunPredictiveCallDelegate.start(model.getData().getId());
        log.error("test start result:{}",resultModel1);
    }
    @Test
    public void testDelete() {
        TianRunPredictiveCallResultModel model = tianRunPredictiveCallDelegate.delete(842);
        log.error("test delete result:{}",model);
    }
    @Test
    public void testQuery() {
        TianRunPredictiveCallResultModel<TaskProperties> model = tianRunPredictiveCallDelegate.query(null,null,null);
        log.error("test query result:{}",model);
    }
    @Test
    public void testimportTaskTel(){
        List<TaskTel> taskTelList = Lists.newArrayList();
        TaskTel taskTel15711495750 = new TaskTel();
        taskTel15711495750.setPriority(0);
        taskTel15711495750.setTel("17601629731");
        taskTelList.add(taskTel15711495750);

        TaskTel taskTel18510745173 = new TaskTel();
        taskTel18510745173.setPriority(0);
        taskTel18510745173.setTel("18201534934");
        taskTelList.add(taskTel18510745173);


        TianRunPredictiveCallResultModel<String> resultModel = tianRunPredictiveCallDelegate.importTaskTel(4420,Lists.newArrayList("18310535756","15313242351","15532087892"),null);
        log.error("test importTaskTel result:{}",resultModel);
    }
    @Test
    public void testdeleteTaskTel(){
        TianRunPredictiveCallResultModel resultModel = tianRunPredictiveCallDelegate.deleteTaskTel(839, null, null, null, null);
        log.error("test deleteTaskTel result:{}",resultModel);
    }
    @Test
    public void teststart(){
        TianRunPredictiveCallResultModel resultModel = tianRunPredictiveCallDelegate.start(848);
        log.error("test start result:{}",resultModel);
    }

    @Test
    public void testqueryCallRecord(){
        TianRunPredictiveCallResultModel resultModel = tianRunPredictiveCallDelegate.queryCallRecord(2342,0,1000);
        log.error("test queryCallRecord result:{}",resultModel);
    }
    @Test
    public void testgetOneCallRecordDetail(){
        TianRunPredictiveCallResultModel resultModel = tianRunPredictiveCallDelegate.getOneCallRecordDetail("sip-2-1564317600.6");
        log.error("test getOneCallRecordDetail result:{}",resultModel);
    }
    @Test
    public void listTaskTel(){
        TianRunPredictiveCallResultModel<TaskTelList> resultModel = tianRunPredictiveCallDelegate.listTaskTel(867,null,null,null,null,null);
        log.error("test listTaskTel result:{}",resultModel);
    }
    @Test
    public void testHandleRecord(){
        Integer taskId = 905;
        TianRunPredictiveCallResultModel<List<PredictiveCallCdr>> predictiveCallResultModel = tianRunPredictiveCallDelegate.queryCallRecord(taskId, 0, 1000);
        if (predictiveCallResultModel.isNotOk()){
            log.error(this.getClass().getSimpleName()+" queryCallRecord 未能成功  predictiveCallResultModel:{}",predictiveCallResultModel);
            return;
        }
        List<String> allPhoneByTaskId = Lists.newArrayList();
        TianRunPredictiveCallResultModel<TaskTelList> resultModel = tianRunPredictiveCallDelegate.listTaskTel(taskId, null, null, null, null, null);
        if (resultModel.isOk()){
            allPhoneByTaskId = resultModel.getData().getTaskTelList().stream().map(TaskTelExt::getTel).collect(Collectors.toList());
        }
        log.error("---------- allPhoneByTaskId:{}",allPhoneByTaskId);
        // 拿到所有的badTel
        allPhoneByTaskId.removeAll(predictiveCallResultModel.getData().stream().map(PredictiveCallCdr::getCustomerNumber).collect(Collectors.toList()));
        log.error("---------- allPhoneByTaskId:{}",allPhoneByTaskId);
        List<PredictiveCallCdr> noCalledRecordList = predictiveCallResultModel.getData().stream().filter(predictiveCallCdr -> {
            if (predictiveCallCdr.getAnswerTime()==0||predictiveCallCdr.getBridgeTime()==0){
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        log.debug("predictiveCallResultModel file predictiveCallCdr.getAnswerTime()==0||predictiveCallCdr.getBridgeTime()==0   result:{}",noCalledRecordList);

    }

    @Test
    public void testLogin(){
        TianRunPredictiveCallResultModel online = tianRunPredictiveCallDelegate.online("0001","18310535756");
        log.debug("online:{}",online);
//        TianRunPredictiveCallResultModel offline = tianRunPredictiveCallDelegate.offline("0001");
//        log.debug("offline:{}",offline);
    }

    @Test
    public void getCnoStatus(){
        TianRunPredictiveCallResultModel<CnoAgent> cnoAgent = tianRunPredictiveCallDelegate.getCnoStatus("2006");
        log.info("cnoAgent:{}",cnoAgent);
    }

    @Test
    public void queryRecord(){
        tianRunPredictiveCallFacade.handleNoCalledByTime();
    }

    @Test
    public void queryBadTelRecord(){
        Date startDate = DateUtil.addDay(DateUtil.getCurrentDate(),-1);
        Date endDate = DateUtil.getCurrentDate();
        tianRunPredictiveCallFacade.handleBadCalledByTime(startDate,endDate);
    }

    @Test
    public void queryUnqiueCodeByTime(){
        Date startDate = DateUtil.addDay(DateUtil.getCurrentDate(),-1);
        cfClewInfoServiceImpl.getClewBaseInfoByUniqueIds(startDate,DateUtil.nowTime(),Lists.newArrayList("sip-14-1566547926.124154", "sip-8-1566547875.123098"));
    }

    @Test
    public void testMqStartPhone(){
        PredictiveCallImportAndStartModel predictiveCallImportAndStartModel = new PredictiveCallImportAndStartModel();
        predictiveCallImportAndStartModel.setTaskId(2433);
        predictiveCallImportAndStartModel.setTaskType(1);
        predictiveCallImportAndStartModel.setOriginPredictiveCallPhone(Lists.newArrayList());
        predictiveCallImportAndStartModel.setPredictiveCallPhone(Lists.newArrayList());
        mqProducerServiceImpl.sendDelayAttemptStartPredictiveTask(predictiveCallImportAndStartModel);
    }

    @Test
    public void get(){
        int taskId = 2464;
        TianRunPredictiveCallResultModel<TaskPropertyData> model = tianRunPredictiveCallDelegate.get(taskId);
        if (model.isOk() && model.getData() != null){
            TaskProperty taskProperty = model.getData().getTaskProperty();
            //如果任务状态是暂停或初始，那么尝试重新启动任务
            if (taskProperty.getStatus().equals(PredictiveCallTaskStatus.PAUSE.getStatus()) ||
                    taskProperty.getStatus().equals(PredictiveCallTaskStatus.DEFAULT.getStatus())){
                log.info("taskProperty:{}", JSON.toJSONString(model.getData()));
            }
        }else {
            log.info("taskProperty2:{}",JSONObject.toJSONString(model));
        }
    }

    @Test
    public void queryRealTimeTask(){
        List<TaskPhoneModel> onlineTaskPhoneModelList = cfClewTaskReadServiceImpl.getRealTimePredictiveCallPhone(300);
        log.info("onlineTaskPhoneModelList:{}",onlineTaskPhoneModelList.size());
    }

    @Test
    public void queryAgentDatas(){
        TianRunPredictiveCallResultModel<AgentData> datas = tianRunPredictiveCallDelegate.queryAgents(100);
        log.info("datas:{}",datas.getData().getAgents());
    }

    @Test
    public void taskMonitor(){
        OpResult<Void> opResult = tianRunPredictiveCallFacade.forecastTaskMonitor();
        log.info("opResut:{}",opResult);
    }

    @Test
    public void userMonitor(){
        OpResult<Void> opResult = tianRunPredictiveCallFacade.forecastUserMonitor();
        System.out.println(opResult);
    }

}
