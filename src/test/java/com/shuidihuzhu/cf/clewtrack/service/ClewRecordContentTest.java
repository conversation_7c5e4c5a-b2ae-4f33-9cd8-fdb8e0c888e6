package com.shuidihuzhu.cf.clewtrack.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfForecastCallRecordsDO;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewBaseInfoEnums;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewErrorCode;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewTaskEnums;
import com.shuidihuzhu.cf.clewtrack.facade.ICfClewTaskFeignFacade;
import com.shuidihuzhu.cf.clewtrack.model.ClewRecordContentListModel;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewCallRecordsDao;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewRecordContentDao;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class) // 指定我们SpringBoot工程的Application启动类
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class
})
@WebAppConfiguration
@Slf4j
public class ClewRecordContentTest {

    @Autowired
    private CfClewRecordContentDao clewRecordContentDao;
    @Resource
    private ICfClewInfoService clewInfoService;
    @Autowired
    private ICfForecastCallRecordService cfForecastCallRecordServiceImpl;
    @Autowired
    private CfClewCallRecordsDao clewCallRecordsDao;
    @Autowired
    private ICfClewTaskFeignFacade cfClewTaskFeignFacade;
    @Autowired
    private ICfClewCallRecordsService cfClewCallRecordsService;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void get(){

        List<Long> clewIds = Lists.newArrayList(8966L,9513L,9392L,9424L,9389L);
        List<ClewRecordContentListModel> clewRecordMap = clewRecordContentDao.getRecordContentByClewIds(clewIds,clewIds.size(),13);
        System.err.println("clewRecordMap:"+clewRecordMap.size());
    }

    @Test
    public void getClewBaseInfo(){
        CfClewBaseInfoDO clewBaseInfoByClewId = clewInfoService.getClewBaseInfoByClewId(32250L);
        log.info("clewBaseInfoByClewId:{},id:{}",clewBaseInfoByClewId,clewBaseInfoByClewId.getId());
        clewBaseInfoByClewId.setRegisterMode(CfClewBaseInfoEnums.RegisterModeEnum.DOUBLECALL.getCode());
        clewBaseInfoByClewId.setRegisterName(CfClewBaseInfoEnums.RegisterModeEnum.DOUBLECALL.getDesc());
        clewInfoService.setPreClewIdAndOriginClewId(clewBaseInfoByClewId,clewBaseInfoByClewId);
        OpResult<CfClewBaseInfoDO> result = clewInfoService.createNewClewInfoBase(clewBaseInfoByClewId,false);
        if (result.isSuccessWithNotNullData()){
            CfClewBaseInfoDO cfClewBaseInfoDONew = result.getData();
            log.info("cfClewBaseInfoDONew:{}",cfClewBaseInfoDONew);
        }
    }

    @Test
    public void getLatestCfRecordByCreatimeAndPhone(){
        CfForecastCallRecordsDO cfForecastCallRecordsDO = cfForecastCallRecordServiceImpl.getLatestCfForecastCallRecordsByCreatTimeAndPhone(DateUtil.getCurrentDate(),"17600501918");
        if (cfForecastCallRecordsDO == null){
            log.error("getCfForecastCallRecordsByCreatTimeAndPhone result is null");
        }
        log.info("cfForecastCallRecordsDO:{}",cfForecastCallRecordsDO);
        CfClewBaseInfoDO originClewBaseInfoDO = clewInfoService.getClewBaseInfoByClewId(cfForecastCallRecordsDO.getClewId());
        log.info("originClewBaseInfoDO:{}",originClewBaseInfoDO);
    }

    @Test
    public void insert(){
        CfClewCallRecordsDO cfClewCallRecordsDO = new CfClewCallRecordsDO();
        cfClewCallRecordsDO.setClientName("1");
        cfClewCallRecordsDO.setOperatorType("bd");
        cfClewCallRecordsDO.setOrgName("华北-山西");
        clewCallRecordsDao.saveCallRecords(cfClewCallRecordsDO);
    }

    @Test
    public void query(){
        OpResult<List<CfClueInfoModel>> opResult = cfClewTaskFeignFacade.listCfClueInfo(Lists.newArrayList(233695L));
        log.info("list:{}",opResult.getData());
    }

    @Test
    public void test(){
        String mis = "lifeifei";String phone = "mc/3xpugFQM38a1TatjBMA==",secondPhone = "";
        String taskAssignTime = "2020-08-20";
        List<CfClewCallRecordsDO> list = cfClewCallRecordsService.listCallRecordsByMisAndPhone(mis,phone,secondPhone,taskAssignTime);
        log.info("list:{}",list);
    }
}
