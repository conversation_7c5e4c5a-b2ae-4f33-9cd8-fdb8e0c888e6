package com.shuidihuzhu.cf.clewtrack.service.myclewreminder;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.model.myclewremindconfig.CfClewRemindMQSendPayload;
import com.shuidihuzhu.cf.clewtrack.model.myclewremindconfig.MyClewReminderTimeoutLeaderPayload;
import com.shuidihuzhu.cf.clewtrack.service.ICfClewTaskReadService;
import com.shuidihuzhu.cf.clewtrack.service.runner.AbstractTest;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/2/26
 */
public class ReminderTest extends AbstractTest {

    @Resource
    ReminderHandler reminderHandler;

    @Resource
    ICfClewTaskReadService taskReadService;

    @Test
    public void remindMQSendTest() {
        long taskId = 515299L;
        CfClewTaskDO cfClewTaskDO = taskReadService.getTaskInfoByTaskId(taskId);
        boolean res = reminderHandler.remindMQSend(cfClewTaskDO);
        System.out.println(res);
    }

    @Test
    public void remindSendTest() {
        String json = "{\"batchNum\":1,\"cfClewReminderConfigDTO\":{\"consecutiveMissedFollowUpCount\":{\"count\":0,\"text\":\"\"},\"dailyReminderConfigs\":[{\"count\":2,\"intervalBetweenReminders\":[{\"duration\":3,\"timeUnit\":\"MINUTES\"},{\"duration\":4,\"timeUnit\":\"MINUTES\"}]}],\"followUpDuration\":{\"duration\":20,\"timeUnit\":\"MINUTES\"},\"followUpRequired\":true,\"followUpSkipConfigTypes\":[1],\"reminderDays\":1,\"reminderEndTimeStr\":\"22:00:00\",\"reminderTextConfig\":\"12313人啊是否\",\"serviceStage\":0},\"cfClewTaskDO\":{\"assginTime\":1740555250000,\"businessTags\":0,\"clewId\":626935,\"clewLayerConfigId\":0,\"clewLayerName\":\"\",\"closedType\":-1,\"createTime\":1740555237000,\"crowdPacketId\":0,\"delayHandle\":0,\"eagleHandle\":10,\"endReasonRemark\":\"\",\"expectLastAssginTime\":1740598437000,\"expectLastHandleTime\":1740573250000,\"id\":515299,\"isAward\":0,\"isClosed\":0,\"isDelete\":0,\"isRecycle\":0,\"lastAssignType\":0,\"lastHandleTime\":1740555237000,\"packetId\":49,\"priority\":0,\"score\":\"\",\"sourceType\":14,\"taskStatus\":1,\"taskType\":3,\"updateTime\":1740555250000,\"userId\":\"xuhongfeng\",\"userName\":\"许宏峰\",\"workContentType\":1000,\"workOrderId\":0,\"workbenchType\":2},\"dayKey\":\"2025-02-26\",\"delayTimeInSecond\":180,\"serviceStageEnum\":\"NEW\"}";
        CfClewRemindMQSendPayload payload = JSON.parseObject(json, CfClewRemindMQSendPayload.class);
        boolean res = reminderHandler.remindSend(payload);
        System.out.println(res);
    }

    @Test
    public void reminderLeaderTimeoutTest() {
        String json = "{\"cfClewReminderConfigDTO\":{\"consecutiveMissedFollowUpCount\":{\"count\":1,\"text\":\"123131312313吃啥\"},\"dailyReminderConfigs\":[{\"count\":2,\"intervalBetweenReminders\":[{\"duration\":3,\"timeUnit\":\"MINUTES\"},{\"duration\":4,\"timeUnit\":\"MINUTES\"}]}],\"followUpDuration\":{\"duration\":20,\"timeUnit\":\"MINUTES\"},\"followUpRequired\":true,\"followUpSkipConfigTypes\":[1],\"reminderDays\":1,\"reminderEndTimeStr\":\"22:00:00\",\"reminderTextConfig\":\"12313人啊是否\",\"serviceStage\":0},\"cfClewTaskDO\":{\"assginTime\":1740555250000,\"businessTags\":0,\"clewId\":626935,\"clewLayerConfigId\":0,\"clewLayerName\":\"\",\"closedType\":-1,\"createTime\":1740555237000,\"crowdPacketId\":0,\"delayHandle\":0,\"eagleHandle\":10,\"endReasonRemark\":\"\",\"expectLastAssginTime\":1740598437000,\"expectLastHandleTime\":1740573250000,\"id\":515299,\"isAward\":0,\"isClosed\":0,\"isDelete\":0,\"isRecycle\":0,\"lastAssignType\":0,\"lastHandleTime\":1740555237000,\"packetId\":49,\"priority\":0,\"score\":\"\",\"sourceType\":14,\"taskStatus\":1,\"taskType\":3,\"updateTime\":1740555250000,\"userId\":\"xuhongfeng\",\"userName\":\"许宏峰\",\"workContentType\":1000,\"workOrderId\":0,\"workbenchType\":2},\"dayKey\":\"2025-02-26\",\"serviceStageEnum\":\"NEW\"}";
        MyClewReminderTimeoutLeaderPayload payload = JSON.parseObject(json, MyClewReminderTimeoutLeaderPayload.class);
        boolean res = reminderHandler.reminderLeaderTimeout(payload);
        System.out.println(res);
    }
}
