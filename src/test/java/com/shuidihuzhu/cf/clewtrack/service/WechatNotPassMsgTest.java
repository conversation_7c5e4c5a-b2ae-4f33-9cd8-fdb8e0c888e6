package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.constants.EsSqlQueryCons;
import com.shuidihuzhu.cf.clewtrack.delegate.BiApiClientDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewBaseInfoEnums;
import com.shuidihuzhu.cf.clewtrack.facade.ICfClewBaseLayerConfigFacade;
import com.shuidihuzhu.cf.clewtrack.model.bdcrm.BdCrmVolunteerCountModel;
import com.shuidihuzhu.cf.clewtrack.mq.producer.IMqProducerService;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.cf.clewtrack.param.ClewInfoForFuwuSearchParam;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.clewtrack.service.impl.CfClewBaseInfoEsServiceImpl;
import com.shuidihuzhu.cf.clewtrack.utils.EsSqlUtils;
import com.shuidihuzhu.cf.dao.clewtrack.es.CfClewBaseInfoEs;
import com.shuidihuzhu.cf.dao.clewtrack.es.CfClewTaskAttachIndexNewEs;
import com.shuidihuzhu.cf.dao.clewtrack.es.CfClewTaskIndexNewEs;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewtrackHistoryMarkModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-08-14
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class) // 指定我们SpringBoot工程的Application启动类
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class
})
@WebAppConfiguration
@Slf4j
public class WechatNotPassMsgTest {

    @Autowired
    private IMqProducerService mqProducerService;
    @Autowired
    private ICfClewTaskReadService cfClewTaskReadService;
    @Autowired
    private ICfClewBaseLayerConfigFacade configFacade;
    @Autowired
    private CfClewTaskAttachIndexNewEs cfClewTaskAttachIndexNewEs;
    @Autowired
    private CfClewTaskIndexNewEs cfClewTaskIndexNewEs;
    @Autowired
    private CfClewBaseInfoEs cfClewBaseInfoEs;
    @Autowired
    private CfClewBaseInfoEsServiceImpl cfClewBaseInfoEsServiceImpl;
    @Autowired
    private BiApiClientDelegate biApiClientDelegateImpl;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void sendMsg(){
        CfClewTaskDO taskDOFromBD = cfClewTaskReadService.getTaskInfoByTaskId(16872L);
        mqProducerService.sendDelayWechatNotPassRecallMsg(taskDOFromBD);
    }

    @Test
    public void getConfig(){
        CfClewBaseInfoDO cfClewBaseInfoDO = new CfClewBaseInfoDO();
        cfClewBaseInfoDO.setPrimaryChannel(CfClewBaseInfoEnums.PrimaryChannelEnum.APP.getPrimaryChannel());
        cfClewBaseInfoDO.setClewType(CfClewBaseInfoEnums.ClewTypeEnum.CLEW_TYPE_0.getType());
        cfClewBaseInfoDO.setCreateTime(DateUtil.parseDateTime("2020-07-20 05:30:00"));
        configFacade.getLayerConfig(cfClewBaseInfoDO);
    }

    @Test
    public void testEs(){
        Date registeStartTime = DateUtils.addMinutes(DateUtils.addHours(DateUtil.addDay(DateUtil.getCurrentDate(),-29),-4),30);
        Date registeEndTime = DateUtils.addMinutes(DateUtils.addHours(DateUtil.getCurrentDate(),20),30);
        Date manualStartTime = DateUtils.addMinutes(DateUtils.addHours(DateUtil.getCurrentDate(),-3),30);
        Date manualEndTime = DateUtils.addMinutes(DateUtils.addHours(DateUtil.getCurrentDate(),21),30);
        Integer list = cfClewTaskIndexNewEs.getSurplusClew(0,registeStartTime,manualEndTime);
        log.info("list:{}", JSONObject.toJSONString(list));
        Integer count1 = cfClewTaskIndexNewEs.getHandleClew(0,registeStartTime);
        log.info("count1:{}", JSONObject.toJSONString(count1));
        ClewInfoForFuwuSearchParam searchParam = ClewInfoForFuwuSearchParam.builder()
                .userid("lichengjin")
                .cfStartTime(DateUtil.getDateFromLongString("2020-07-21 00:00:00"))
                .cfEndTime(DateUtil.getDateFromLongString("2020-07-21 23:59:59"))
                .cfStatus(0)
                .wechatPass(1)
                .userFlag(null)
                .pageNo(0)
                .pageSize(100)
                .taskTypes(Lists.newArrayList(1,2))
                .clewTypes(Lists.newArrayList(0,1,2))
                .phone(null)
                .serviceUserId(null)
                .primaryChannel(null)
                .packetIdList(Lists.newArrayList())
                .listTypeEnum(null)
                .serviceStatus(null)
                .amountStart(null)
                .amountEnd(null)
                .assignStartTime(DateUtil.getDateFromLongString("2020-06-21 00:00:00"))
                .assignEndTime((DateUtil.getDateFromLongString("2020-07-21 23:59:59")))
                .isRecycle(null)
                .userFirstTag(null)
                .userSecondTag(null)
                .isExchangePhone(null)
                .firstApproveStatus(null)
                .infoId(null)
                .isSubmitReport(null)
                .approveStatus(null)
                .build();
        searchParam.convertStrList();
        Integer count2 = cfClewTaskAttachIndexNewEs.getFuwuListCounts(searchParam);
        log.info("count2:{}", JSONObject.toJSONString(count2));
    }

    @Test
    public void getModel(){
        String uuid = null;String personId = "person_id_2c2318d1-6b90-4c48-aa0d-732a22abca59";Integer clewType = 0;
        List<CfClewBaseInfoDO> list = cfClewBaseInfoEs.listClewIdByUuid(uuid,personId,clewType);
        log.info("list:{}", JSONObject.toJSONString(list));
        String dateTimeStart = "2019-01-01 00:00:00",dateTimeEnd = "2020-07-21 00:00:00";
        StringBuilder whereSqlOfSimpleClew = buildWhereSqlOfSimpleClew(uuid, personId, dateTimeStart, dateTimeEnd);
        String sql = String.format(EsSqlQueryCons.ES_SIMPLE_CLEW_QUERY_BY_UUID_PERSONID, whereSqlOfSimpleClew.toString());

        List<CfClewtrackHistoryMarkModel.SimplyClewRecord> records = cfClewBaseInfoEsServiceImpl.listSimpleClewByUuid(uuid,personId,dateTimeStart,dateTimeEnd,true);
        OpResult<String> opResult = biApiClientDelegateImpl.esQueryCustom(sql);
        List<CfClewtrackHistoryMarkModel.SimplyClewRecord> recordBi = EsSqlUtils.getModelFromEs(opResult.getData(), CfClewtrackHistoryMarkModel.SimplyClewRecord.class);
        log.info("records:{},recordBi:{}",JSONObject.toJSONString(records),JSONObject.toJSONString(recordBi));
    }

    @Test
    public void testBdCrm(){
        Set<String> uniqueCodes = Sets.newHashSet("7s6qpu850","g7pcy6656","brkwc5702","2a8rks202","zqk3h4398","k87lfs618","6n5sfa773","5fzr8a290","t2ndqm864","nzyfgp406","nuvlqa631","pgw8yz801","xbwyk2402","7ehg3z516","fp3nzr900","gvcmsr534","g98rl5119","ulpvg5911","64numl137","msfegl106","2rtdec990","uh4wbx470","6e2vqh849","lk5pm8328","2amyqe416","g8unkb903","92bt3w763","ugxys4775","dxhbrz640","3a8b5z184","zags5q818","kew2c7956","w3sgpu665","vlke2h743","mgefw5845","2vfz7e479","pdmlue902","2s93h6178","s92g3x872","utr37e797","nsl3tr803","wr4q3p299","bzvd5p167","bsvgpx108","gv4l5y363","knta4z646","rgv9nd783","wkpqdv651","6glf59998","n5pste159","95g4wa887","kz9gec596","3fs62y121","g9xzwn711","gl45yb947","tswfqn321","mxrswy191","qycxvg671","fg64za502","uglxqb828","tsgy6m177","dnq8yp593","nhbs6p200","xes9gl842","8af3qd620","3r9mcq174","rwqla9471");
        int registerMode = 0,clewStatus = 0;String dateTimeStart = "2019-01-01 00:00:00",dateTimeEnd = "2020-07-21 00:00:00";
        StringBuilder whereSql = this.builderWhereSqlOfBdCrmVolunteerCountModel(registerMode, clewStatus, dateTimeStart, dateTimeEnd, uniqueCodes);
        String sql = String.format(EsSqlQueryCons.ES_BDCRMVOLUNTEERCOUNT_SQL,whereSql.toString());

        List<BdCrmVolunteerCountModel> list = cfClewBaseInfoEsServiceImpl.getBdCrmVolunteerCountModelList(registerMode,clewStatus,dateTimeStart,dateTimeEnd,uniqueCodes,true);

        OpResult<String> opResult = biApiClientDelegateImpl.esQueryCustom(sql);
        List<BdCrmVolunteerCountModel> list2 = EsSqlUtils.getModelFromEs(opResult.getData(), BdCrmVolunteerCountModel.class);
        log.info("list:{}",JSONObject.toJSONString(list));
        log.info("listbi:{}",JSONObject.toJSONString(list2));

    }

    @Test
    public void testBdCrmOne(){
        Set<String> uniqueCodes = Sets.newHashSet("7s6qpu850","g7pcy6656","brkwc5702","2a8rks202","zqk3h4398","k87lfs618","6n5sfa773","5fzr8a290","t2ndqm864","nzyfgp406","nuvlqa631","pgw8yz801","xbwyk2402","7ehg3z516","fp3nzr900","gvcmsr534","g98rl5119","ulpvg5911","64numl137","msfegl106","2rtdec990","uh4wbx470","6e2vqh849","lk5pm8328","2amyqe416","g8unkb903","92bt3w763","ugxys4775","dxhbrz640","3a8b5z184","zags5q818","kew2c7956","w3sgpu665","vlke2h743","mgefw5845","2vfz7e479","pdmlue902","2s93h6178","s92g3x872","utr37e797","nsl3tr803","wr4q3p299","bzvd5p167","bsvgpx108","gv4l5y363","knta4z646","rgv9nd783","wkpqdv651","6glf59998","n5pste159","95g4wa887","kz9gec596","3fs62y121","g9xzwn711","gl45yb947","tswfqn321","mxrswy191","qycxvg671","fg64za502","uglxqb828","tsgy6m177","dnq8yp593","nhbs6p200","xes9gl842","8af3qd620","3r9mcq174","rwqla9471");
        int registerMode = 0,clewStatus = 0;String dateTimeStart = "2019-01-01 00:00:00",dateTimeEnd = "2020-07-21 00:00:00";


        BdCrmVolunteerCountModel list = cfClewBaseInfoEsServiceImpl.getBdCfClewBaseInfoOrgCount(registerMode,clewStatus,dateTimeStart,dateTimeEnd,uniqueCodes,true);

        StringBuilder whereSql = this.builderWhereSqlOfBdCrmVolunteerCountModel(registerMode, clewStatus, dateTimeStart, dateTimeEnd, uniqueCodes);
        String sql = String.format(EsSqlQueryCons.ES_CFCLEWBASEINFO_ORG_COUNT_SQL,whereSql.toString());

        OpResult<String> opResult = biApiClientDelegateImpl.esQueryCustom(sql);

        BdCrmVolunteerCountModel list2= EsSqlUtils.getModelFromEs(opResult.getData(), BdCrmVolunteerCountModel.class).get(0);

        log.info("list:{}",JSONObject.toJSONString(list));
        log.info("listbi:{}",JSONObject.toJSONString(list2));

    }

    private StringBuilder buildWhereSqlOfSimpleClew(String uuid, String personId, String dateTimeStart, String dateTimeEnd) {
        StringBuilder whereSql = EsSqlUtils.builderWhereSqlOfTime(dateTimeStart, dateTimeEnd,"create_time",true);
        whereSql.append(String.format(" clew_type = %d", CfClewBaseInfoEnums.ClewTypeEnum.CLEW_TYPE_0.getType()));
        if (StringUtils.isNotBlank(uuid)) {
            whereSql.append(String.format(" and uuid = '%s'", uuid));
        } else if (StringUtils.isNotBlank(personId)) {
            whereSql.append(String.format(" and person_id = '%s'", personId));
        }
        whereSql.append(" order by id desc limit 100");
        return whereSql;
    }

    private StringBuilder builderWhereSqlOfBdCrmVolunteerCountModel(int registerMode, int clewStatus,String dateTimeStart, String dateTimeEnd, Set<String> uniqueCodes){
        StringBuilder whereSql = EsSqlUtils.builderWhereSqlOfTime(dateTimeStart,dateTimeEnd,"create_time",true);
        if (registerMode!=0){
            whereSql.append(String.format(" register_mode = %d and ",registerMode));
        }
        if (clewStatus!=-1){
            whereSql.append(String.format(" clew_status = %d and ",clewStatus));
        }
        whereSql.append(" clew_type <> 4 and ")
                .append(String.format(" unique_code in %s",
                        "('"+uniqueCodes.stream().collect(Collectors.joining("','")).concat("')")));
        return whereSql;
    }
}
