package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.VolunteerSearchModel;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * @author: wanghui
 * @time: 2019/9/26 3:20 PM
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class IVolunteerDelegateTest {
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;
    @Autowired
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;
    static {
        System.setProperty("spring.cloud.consul.host", "127.0.0.1");
    }

    @Test
    public void test(){
//        log.error(this.getClass().getSimpleName()+"  getCfVolunteerDOByUniqueCode  result:{}", JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCode("g98rl5119")));
//        log.error(this.getClass().getSimpleName()+"  getOnWorkCfVolunteerDOByLimit  result:{}", JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getOnWorkCfVolunteerDOByLimit(0L,5)));
//        log.error(this.getClass().getSimpleName()+"  getCfVolunteerDOByUniqueCodes  result:{}", JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCodes(Lists.newArrayList("lk5pm8328","6e2vqh849","uh4wbx470","2rtdec990","x95qlv616","msfegl106","tral7w917","64numl137","ulpvg5911","gxw9vk271"))));
//        log.error(this.getClass().getSimpleName()+"  addVolunteer  result:{}",cfGrowthtoolVolunteerFeignClient.addVolunteer());
//        VolunteerSearchModel volunteerSearchModel = new VolunteerSearchModel();
//        volunteerSearchModel.setVolunteerType(1);
//        volunteerSearchModel.setWorkStatus(2);
//        log.error(this.getClass().getSimpleName()+"  getVolunteer  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getVolunteer(volunteerSearchModel,true,1,10)));
//        log.error(this.getClass().getSimpleName()+"  getCrowdfundingVolunteerVoById  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getCrowdfundingVolunteerVoById(732L)));
//        log.error(this.getClass().getSimpleName()+"  updateVolunteerInfoById  result:{}",cfGrowthtoolVolunteerFeignClient.updateVolunteerInfoById());
//        log.error(this.getClass().getSimpleName()+"  updateQrCode  result:{}",cfGrowthtoolVolunteerFeignClient.updateQrCode());
//        log.error(this.getClass().getSimpleName()+"  checkIsExistByTypeOrIdentity  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.checkIsExistByTypeOrIdentity(1,"mzPOvL1APGYdTqG1o1Lp/cbPVlULAgMaH6xxeGq3EIQ=",732L)));
//        log.error(this.getClass().getSimpleName()+"  checkIsExistByTypeOrEmail  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.checkIsExistByTypeOrEmail(1,"<EMAIL>",732L)));
        log.error(this.getClass().getSimpleName()+"  updateApplyStatusById  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.updateApplyStatusById(732L,2,"wanghui",258,"","","")));
//        log.error(this.getClass().getSimpleName()+"  getVolunteerUniqueByMobileAndWorkStatus  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getVolunteerUniqueByMobileAndWorkStatus(2,"jyIN3U0Dts1G0unf6OEA6g==")));
//        log.error(this.getClass().getSimpleName()+"  getUniqueCodeListByVolunteerType  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getUniqueCodeListByVolunteerType(1)));
//        log.error(this.getClass().getSimpleName()+"  getVolunteerListByMisList  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getVolunteerListByMisList(Lists.newArrayList("yanghuijie","zhangqian","wangyong01"))));
//        log.error(this.getClass().getSimpleName()+"  getXianXiaVolunteerListByVolunteerType  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getXianXiaVolunteerListByVolunteerType(2)));
//        log.error(this.getClass().getSimpleName()+"  getCrowdfundingVolunteerByUniqueCode  result:{}",JSON.toJSONString(cfGrowthtoolVolunteerFeignClient.getCrowdfundingVolunteerByUniqueCode("lk5pm8328")));
    }
    @Test
    public void test1(){
        log.error(this.getClass().getSimpleName()+"  getVolunteerByMobile  result:{}",JSON.toJSONString(cfGrowthtoolFeginClient.getVolunteerByMobile("jyIN3U0Dts1G0unf6OEA6g==")));
        log.error(this.getClass().getSimpleName()+"  getVolunteerByEmail  result:{}",JSON.toJSONString(cfGrowthtoolFeginClient.getVolunteerByEmail("<EMAIL>")));
        log.error(this.getClass().getSimpleName()+"  getVolunteerByUniqueCode  result:{}",JSON.toJSONString(cfGrowthtoolFeginClient.getVolunteerByUniqueCode("lk5pm8328")));
    }
}
