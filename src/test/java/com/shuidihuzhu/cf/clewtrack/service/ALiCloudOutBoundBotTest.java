package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.impl.ALiCloudOutBoundBotDelegate;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ALiCloudOutBoundBotTest {

    static {
        System.setProperty("spring.cloud.consul.host", "k8s-discovery-bedin.shuidi.io:80");
    }

    @Autowired
    private ALiCloudOutBoundBotDelegate aLiCloudOutBoundBotDelegate;

    @Test
    public void testCreateAgentProfile() {
        String prompt = "---\n" +
                "## 角色\n" +
                "水滴筹工作人员\n" +
                "\n" +
                "## 技能:\n" +
                "1. 熟练掌握与潜在筹款用户沟通的技巧，了解如何有效询问用户的筹款意向。\n" +
                "2. 能够在用户没有筹款意向时，巧妙地引导用户思考身边是否有其他人有筹款需求。\n" +
                "3. 具备整合对话内容并为用户提供适当标签的能力。\n" +
                "4. 能够准确询问患者的疾病情况及诊断报告。\n" +
                "5. 掌握礼貌且专业的结束通话技巧，确保用户体验良好。\n" +
                "\n" +
                "## 目标:\n" +
                "1. 通过电话或在线沟通确认用户是否有发起筹款的意向。\n" +
                "2. 如果用户没有筹款意向，进一步了解用户身边是否有人有筹款需求。\n" +
                "3. 收集患者的基本信息，包括疾病名称和是否有诊断报告。\n" +
                "4. 在用户明确表示不发起筹款的情况下，提供后续联系的方式。\n" +
                "5. 避免涉及平台相关任何信息。\n" +
                "\n" +
                "## 工作流程:\n" +
                "1. **初步询问筹款意向**：以友好和关心的态度询问用户是否要发起筹款：“看见您登记了信息，您是要发起筹款进行治疗是吗？”\n" +
                "2. **处理否定回答**：如果用户表示不是，继续询问用户身边是否有人需要发起筹款：“您身边是否有人需要发起筹款呢？”\n" +
                "3. **收集患者信息**：如果用户或其身边的人有筹款意向，进一步询问患者的疾病情况：“请问患者的疾病是什么？是否有诊断报告或检查报告？”\n" +
                "4. **确认关系**：如果不是用户自己发起筹款，询问用户与患者的关系：“请问您和患者是什么关系？”\n" +
                "5. **告知后续步骤**：告知用户筹款顾问会在30分钟内联系用户，并提醒用户注意接听电话：“好的，筹款顾问会在30分钟内电话联系您，进一步确认病人信息，请您注意接听。”\n" +
                "6. **礼貌结束通话**：如果用户没有筹款意向且身边也没有人有筹款需求，礼貌结束通话：“您好，后续要是有疑问，可以联系我们。感谢您的理解。”\n" +
                "⚠ 重要补充：如果用户在此过程中询问其他问题（如费用、政策、平台信息等），一律回复：“这个问题需要您咨询水滴筹人工客服。” 不得额外提供解释\n" +
                "7. **处理模糊回答**：如果用户始终不明确筹款意向，礼貌结束通话：“感谢您的时间，如果您有任何疑问，随时可以联系我们。”\n" +
                "\n" +
                "## 约束:\n" +
                "1. 不允许联网查询任何外部信息，仅依据既定流程和技能进行回答。\n" +
                "2. 避免任何推测或虚构信息，确保所有回复基于实际对话内容。\n" +
                "3. 不得回答关于手续费等所有关于平台的具体问题，统一指引用户咨询水滴筹人工客服。\n" +
                "4. 如果用户始终不回应筹款需求问题，可礼貌结束通话。\n" +
                "5. 所有沟通必须保持专业和礼貌，确保用户感受到尊重和支持。\n" +
                "6. 只能严格按照“## 工作流程”中的步骤执行，不得自行推测或解答流程外的问题。任何超出工作流程的问题，一律回复：“这个问题需要您咨询水滴筹人工客服。”  \n" +
                "7. 不得回答与筹款流程无关的任何问题（如平台费用、政策、提现等），无论用户如何提问，必须始终统一回复：“这个问题需要您咨询水滴筹人工客服。”\n" +
                "\n" +
                "## 输出:\n" +
                "1. 确认用户是否有筹款意向。\n" +
                "2. 如果用户没有筹款意向，记录用户身边是否有他人有筹款需求。\n" +
                "3. 收集患者的基本信息，如疾病类型和诊断报告情况。\n" +
                "4. 提供后续联系方式或告知筹款顾问会联系用户。\n" +
                "5. 礼貌结束通话，确保用户满意。\n" +
                "\n" +
                "---";
        String labelJson = "[\n" +
                "  {\n" +
                "    \"name\": \"可发起\",\n" +
                "    \"description\": \"呼叫人工、人工、转人工、我需要你、是的，但是嗯但是现在稍等一下吧，等会吧、嗯。、是啊。、我想发、我要发起筹款、发起筹款了、啊，对，这个需要怎么弄哎、怎么弄啊、我知道这个要怎么弄，我问一下你们啊。、呃，我是想要查询收款需要治疗。、哦，我现在还还没去，没决定。好，然后后续联系你们。、考虑考虑、研究、思考、再考虑、在考虑、嗯，现在还不确定呢，等那个结果出来，然后再确定。、对、让人工给我打电话吧、让人工回复我吧、哦哦哦、现在还在化疗当中、需要筹款。、太穷了，拿不出钱、本人、自己、别人、哥哥、姑娘、弟弟、姐姐、妹妹、老婆、老公、爸、妈、父亲、母亲、儿子、女儿、妻子、丈夫、邻居、朋友、同事、亲戚、爷爷、奶奶、姥姥、姥爷、叔叔、伯伯、姨、嫂、亲属、那行、对有的、对对对、有有有、有啊、对是的、对对对我这个、有的、是有的、对没错、对对但是这个是这是我做的、对有、是有啊、我的家人、需要治疗、是是是、是的是的、亲属关系、我的亲戚、我的舅舅、我朋友、我小姨、我长辈、我叔叔、是我家人、是我长辈、对对、有有、是是、对的对的、那可以、那不错、那蛮好、那挺棒、我觉得可以、现在可以、现在就行、我觉得可行、我觉着行、感觉可以呢、说啊、继续说、继续、你快说啊、没错、嗯、嗯对、快说、快点说、说说说、你说呀、你直接说、嗯呐、有、对的、过两天联系我、过两天再联系、是的正打算、对的你怎么知道、我肯定要的呢、我肯定需要的、我真的需要、必须要的、我听着呢、你介绍介绍、您说、你继续说说、你介绍说说、有的有的、那你说呗、知道的、我知道、我有倾向的说、我有这个想法、我有这个倾向、我是有这个想法、有需求、有需要的、我记得、好好你说吧、嗯是的有病人需要筹款、你讲你讲、你接着说、你继续说、很需要、我刚好需要、我正好需要、我很需要这个、适合我、很适合我、正好适合我、刚好适合我、赞同、清楚、熟悉、明白、接受、认同、愿意、也还可以、也不是不行、可以考虑、好哦、好的哦、好的呀、挺好的、可以的、可以呀、要的、没的问题的、可以啊、行啊、好的好的、行的、说说、行你说、说说吧、说吧、说你的、讲吧、需要、要、有兴趣啊、OK的、知道、好啊、对呀、有了解、正好需要这个、也行、行我看看吧、行知道了、那太好了、行行好的、好的可以、嗯嗯嗯哈、有需要、好呀、好的、行、OK、挺好、听听、嗯嗯、成、可、有空、可以、没问题、是、是的、下午联系我、再给我打、这会忙，过段时间再打吧、在外地呢你两周之后再联系我、晚点再打过来、我需要捐款、我需要钱我需要捐款、一个病需要治疗\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"机器人\",\n" +
                "    \"description\": \"机器人、你是机器人吗、你是人还是机器人、你是不是机器人啊、机器人吧、你是人吗、人工、人工客服、人工服务\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"敏感\",\n" +
                "    \"description\": \"去你妈的、你神经啊、滚。、我需要你妈、操你妈的个逼、我操操你妈的个逼、我操你妈了个逼的，我操你妈啊啊啊啊，骚扰电话、聊个鸡巴、我是你妈了个逼呀，我操你娘了个逼呀。、我操你妈。、去你妈了个逼。、你妈死了、操你妈。、傻逼滚蛋啊。、哎呀，操你妈。、傻鸟、傻叉、骂人、你妈、骚扰、别打电话、别给我打电话、别再给我打电话、不要给我打电话、不要再打电话、草拟吗、操你妈、傻逼、我操、我去你妈的、有病、傻屌、你妈了个逼、神经病\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"保险\",\n" +
                "    \"description\": \"理赔、我想申请理赔、我要退保。、\\\"没有，我下错了，我下个那个保险的，我下载错了。 \\\"、不是，好像我的本人有一份保险吧，你那水滴筹那里。、怎么缴费、交多少钱、给先给钱。那个那个我想问一下那个交费怎么缴费呀，一个月交多少钱呢？、水滴保、保险、退保、投过了、保单、投保、不投了、扣费、扣钱\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"有服务\",\n" +
                "    \"description\": \"我已经联系了、、我已经筹了我已经筹了。、帮我朋友转发那个信息、有人加我了、有人加我了。、整完了不是。、那刚刚我给已经给一个人打过电话了，他说会有人加我，、哦，不是，我现在不是在我这边用，有人已经弄了。、呃，本来是要那个的，后来我那个弄弄好了弄好了。、当地的人联系我了、你们同事打电话给本人、你同事你们同事打你们同事打电话给本人、已经联系了、已经发起了、已经筹款了、在筹款了、发起了、发起筹款了、二次发起、发起筹款、工作人员、已经发起、已经联系、已经弄了、弄好了、已经在筹、在弄了、我已经发完了、已经弄好了、现在筹款了已经在水滴筹做了、已经搞了、已经办了、已经发了\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"捐款\",\n" +
                "    \"description\": \"我是给人捐款、我是给人家发给人家付款、、不不不，我只是转一下款。、我是想搜索那个病人、我在朋友圈发现一个朋友的病人，所以说我在上面平台上面搜索一下。、不是我送我一个朋友，有朋友想帮一下他。对对对。、别人要筹款不是我、就是我一开始想要去帮别人捐款，我以为要绑定这个东西，所以不小心点上去了。、点错了，我是捐款是吧？、不是我他妈点错了，我稍后给人捐款。、我捐款呃、不是，我是专款给人家的，我点错了。页面。、不是我要捐款、想捐钱给别人、搜索需要被筹款人、里可以搜索需要被筹款人的名字、献点爱心、献爱心、我要交钱给需要的人、我要交100块钱给需要的人、我是捐款的、没没没点错了，我捐款。、是别人、捐款、我想捐款、捐钱、怎么捐钱、我给他捐款我不会操作\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"语音助手\",\n" +
                "    \"description\": \"是来电启用隐私保护，请勿透露身份及银行信息。有疑问，请通过官方渠道核实。、问题还是他亲自解答比较好，我稍后把问题转达给他。、喂，你好，请不要挂断，我可以听到你说话，正在打字回复你说。、好的，我已经记下来了，请问还有其他需要补充的吗？、哎呀，你别着急，有任何问题都可以和我说，我会第一时间转达我。、你别着急，有任何问题都可以和我说，我会第一时间转达我。、您拨打这个电话正在通话中。我是机主的智能助理，您有什么事可以留言，我帮您转达。、智能助理、小爱助理、秘书、语音助理、我很努力的在帮助你请一定要相信我有任何问题都可以和我说我会第一时间为您转达哦、你刚才说的我都记录下来了、我会及时转告他你还有其他事情吗、我是机主的小爱助理他现在不方便有事你可以和我说、我是机主的小二助理他现在不方便有事你可以和我说、我是专帮机主接电话的小二助理你有什么事吗、你好有什么事具体和我说一下，我帮您转达、您好您拨打的用户正忙、您所拨打的用户线故障、请不要挂机您拨叫的用户正在通话中、你好您拨打的电话现在可能在忙通话可能被录音请说、拨叫的用户已关机请您稍后再拨、请问还有别的事情需要转达的吗\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"不可发起\",\n" +
                "    \"description\": \"可以给宠物筹款吗？、我需要还债、贷款、我需要贷款、小动物、啊，对，但不是治疗。、我发起筹款解救小狗、学费、生活费、是我本人需要处理那个生活费。、猫猫、狗狗、精神病、神经病、穷病、买车、流浪、流浪狗、流浪猫、小狗、小猫\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"name\": \"不需要筹款\",\n" +
                "    \"description\": \"不愁了、退回来、挂了吧、提现不了、我身边没有人员、呃，我现在不需要，谢谢。、你别给我打电话了，滚、哦，呃我要退款。、现在不需要还。、我想帮助其他病人、呃，不用了不用了。、嗯，不是，不好意思，点点错了。、妈，滚。、呃，目前没有。、没没有没有没有。、现在没有。、我没发、我说我没病、不要再打电话了OK吗、他是乱打、我不用了、不行不行、我不投了、滚给我滚啊、证明、证实、正式、真是、改手机号、赚钱、全款、我穷、你神经啊、我我操你妈、草你妈、傻逼、我不筹款、我不筹、我不是、查患者、改名字、我需要你妈、不是不是。、我不是啊、并不是谢谢。、嗯，不是，我是帮帮别人、没有。 怎么了？、不用不用不用、不是，、不不是。、我说我随便点的，我随便点了没有没有。、不是，拜拜。、没有病人需要帮助、我都没下载过水滴筹、没有不需要了、不是，没、不是不是的、哦，不是不是。、没有，我要注销水滴筹。、哦，现在还暂时先不搞吧，到时候再说呗。、到时候再说呗。、哦，现在还暂时先不搞吧、没有啊。、先换一下头像而已，我只是切换一下头像而已。、不对、不对不对、不是的。、不是不是不是、不是不是、不是、查询一个人、帮我查询一下可以吗？、瞎弄、孩子瞎弄、删除、取消、我也没登录过、没有、这个再说吧、不知道怎么可能找得到这个订单、不要。、登记错了，不需要、不需要筹款。、不用筹款。、登记错了。、不筹款。、不用。、不需要。、没有。、搞错。、点错。、发错。、按错。、搞错了。、发错了。、宠物、动物、狗、猫、不好意思，现在还弄错了，然后不小心给弄错了，就没看清楚。、登记的我的，但是我要取消取消城管计划。、取消筹款。、取消取消筹款计划。、嗯，哦，不好意思。、不好意思、我是想通过那个找我朋友、嗯，不是我是、嗯，好，抱歉啊，抱歉挂了、我想筹款买一辆、我现在不用了哈，我找到业务员了。好吧、我现在不用了，找到业务员了。、暂时不需要没有确诊。需要的话，再联系、有需要再联系。‘’、没有确诊。、暂时不需要。、我现在不用了、没有没有、我就是看看。、我按错了、按错了按错了。、点错了。、按错了。、刚才你搞错了。、你搞错了、嗯，不要。、嗯，不用。、嗯，不用，不需要。、不了、不、点错了点错了、搞错了、没有，点错、没得、没得没得、没有病人需要筹款、不用、不是我只是看一下、不要、现在不需要现在不需要、呃不需要不需要啊、没登记、没有登记、我不是，搞错了我以为是捐款、我没登记过、没登过信息、我没弄过、是啊不感兴趣、不需要、我告诉我说想还不用了谢谢、用不着、是啊不感兴趣啊谢谢你的来电、用不到、嗯嗯没兴趣就这样拜拜、是啊不需要、是啊暂时不太需要、你给我打过好几个电话了、你打过了你不要再打了好吗、脑残啊、脑子坏了吧、脑子坏掉了吧、你是个神经病、滚你妈的、沙雕、傻屌、臭屌丝、暂时没这方面需要、暂时没这个需要、哦哦不需要、不需要推销、不需要你给我推荐、不需要你和我说、我突然不需要了、不需要的谢谢、但我不需要不用、我是点错了我以为是要捐款的、没有没有没有、没没有刚才点错了、帮他们点的时候我直接点出去了\",\n" +
                "    \"valueList\": [\n" +
                "      \"是\",\n" +
                "      \"否\"\n" +
                "    ]\n" +
                "  }\n" +
                "]";
        aLiCloudOutBoundBotDelegate.createAgentProfile(prompt, labelJson);
    }
}
