package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.biz.CfClewTrackUserInfoBiz;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO;
import com.shuidihuzhu.cf.clewtrack.service.runner.AbstractTest;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * CfClewTrackUserInfoBiz 测试类
 *
 * <AUTHOR>
 * @since 2024/12/11
 */
public class CfClewTrackUserInfoBizTest extends AbstractTest {

    @Resource
    private CfClewTrackUserInfoBiz cfClewTrackUserInfoBiz;

    @Test
    public void testInsert() {
        // 准备测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_user_id");
        userInfo.setUserName("测试用户");
        userInfo.setQyWechatUserId("test_qy_wechat_id");
        userInfo.setTrackerType(1);

        // 执行测试
        int result = cfClewTrackUserInfoBiz.insert(userInfo);

        // 验证结果
        Assert.assertEquals(1, result);
    }

    @Test
    public void testUpdateClewUserInfoById() {
        // 先插入测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_update_id");
        userInfo.setUserName("原始用户名");
        userInfo.setQyWechatUserId("original_qy_id");
        userInfo.setTrackerType(1);
        cfClewTrackUserInfoBiz.insert(userInfo);

        // 准备更新数据
        userInfo.setUserName("更新后用户名");
        userInfo.setQyWechatUserId("updated_qy_id");

        // 执行测试
        int result = cfClewTrackUserInfoBiz.updateClewUserInfoById(userInfo);

        // 验证结果
        Assert.assertEquals(1, result);
    }

    @Test
    public void testGetClewUserInfoDetailById() {
        // 先插入测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_detail_id");
        userInfo.setUserName("详情测试用户");
        userInfo.setQyWechatUserId("detail_qy_id");
        userInfo.setTrackerType(1);
        cfClewTrackUserInfoBiz.insert(userInfo);

        // 获取插入的记录ID
        List<CfClewTrackUserInfoDO> insertedList = cfClewTrackUserInfoBiz.getByUserIdAndType(
                Arrays.asList("test_detail_id"), 1);
        Assert.assertFalse(insertedList.isEmpty());
        Integer id = insertedList.get(0).getId();

        // 执行测试
        CfClewTrackUserInfoDO result = cfClewTrackUserInfoBiz.getClewUserInfoDetailById(id);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals("test_detail_id", result.getUserId());
        Assert.assertEquals("详情测试用户", result.getUserName());
    }

    @Test
    public void testUpdateUserOnlineStatus() {
        // 先插入测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_online_id");
        userInfo.setUserName("在线状态测试用户");
        userInfo.setQyWechatUserId("online_qy_id");
        userInfo.setTrackerType(1);
        cfClewTrackUserInfoBiz.insert(userInfo);

        // 获取插入的记录ID
        List<CfClewTrackUserInfoDO> insertedList = cfClewTrackUserInfoBiz.getByUserIdAndType(
                Arrays.asList("test_online_id"), 1);
        Assert.assertFalse(insertedList.isEmpty());
        Integer id = insertedList.get(0).getId();

        // 执行测试
        Date now = new Date();
        cfClewTrackUserInfoBiz.updateUserOnlineStatusById(id, now, 0, 1, now);

        // 验证结果
        CfClewTrackUserInfoDO updated = cfClewTrackUserInfoBiz.getClewUserInfoDetailById(id);
        Assert.assertNotNull(updated);
        Assert.assertEquals(Integer.valueOf(1), updated.getOnlineStatus());
        Assert.assertEquals(Integer.valueOf(0), updated.getIsLock());
    }

    @Test
    public void testUpdateUserIsTop() {
        // 先插入测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_top_id");
        userInfo.setUserName("置顶测试用户");
        userInfo.setQyWechatUserId("top_qy_id");
        userInfo.setTrackerType(1);
        cfClewTrackUserInfoBiz.insert(userInfo);

        // 执行测试
        int result = cfClewTrackUserInfoBiz.updateUserIsTop(Arrays.asList("test_top_id"), 1, 1);

        // 验证结果
        Assert.assertEquals(1, result);
    }

    @Test
    public void testClearQywechatId() {
        // 先插入测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_clear_id");
        userInfo.setUserName("清除企业微信ID测试用户");
        userInfo.setQyWechatUserId("clear_qy_id");
        userInfo.setTrackerType(1);
        cfClewTrackUserInfoBiz.insert(userInfo);

        // 执行测试
        int result = cfClewTrackUserInfoBiz.clearQywechatIdByUserIdAndTrackerType("test_clear_id", 1);

        // 验证结果
        Assert.assertEquals(1, result);
    }

    @Test
    public void testModifyOnWorkTime() {
        // 先插入测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_worktime_id");
        userInfo.setUserName("工作时间测试用户");
        userInfo.setQyWechatUserId("worktime_qy_id");
        userInfo.setTrackerType(1);
        cfClewTrackUserInfoBiz.insert(userInfo);

        // 执行测试
        cfClewTrackUserInfoBiz.modifyOnWorkTime("09:00-18:00", "test_worktime_id");

        // 验证结果
        List<CfClewTrackUserInfoDO> result = cfClewTrackUserInfoBiz.getByUserIdAndType(
                Arrays.asList("test_worktime_id"), 1);
        Assert.assertFalse(result.isEmpty());
        Assert.assertEquals("09:00-18:00", result.get(0).getOnWorkTime());
    }

    @Test
    public void testGetUserInfoListByTrackerType() {
        // 先插入测试数据
        CfClewTrackUserInfoDO userInfo = new CfClewTrackUserInfoDO();
        userInfo.setUserId("test_list_id");
        userInfo.setUserName("列表测试用户");
        userInfo.setQyWechatUserId("list_qy_id");
        userInfo.setTrackerType(1);
        cfClewTrackUserInfoBiz.insert(userInfo);

        // 执行测试
        List<CfClewTrackUserInfoDO> result = cfClewTrackUserInfoBiz.getUserInfoListByTrackerType(1, 0);

        // 验证结果
        Assert.assertFalse(result.isEmpty());
        boolean found = result.stream()
                .anyMatch(user -> "test_list_id".equals(user.getUserId()));
        Assert.assertTrue(found);
    }
}
