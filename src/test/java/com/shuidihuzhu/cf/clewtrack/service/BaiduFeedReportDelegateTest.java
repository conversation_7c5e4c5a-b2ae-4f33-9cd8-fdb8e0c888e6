package com.shuidihuzhu.cf.clewtrack.service;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.executor.ShardingContexts;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewBaseInfoEnums;
import com.shuidihuzhu.cf.clewtrack.facade.baidufeed.IBaiduFeedConvertInfoFacade;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.fromcfapi.CfInitialAuditApproveMsgConsumer;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.fromcfapi.NoticeClewTrackAddBdCrmClewConsumer;
import com.shuidihuzhu.cf.clewtrack.runner.assigntask.QianZhiTaskScheduleJob;
import com.shuidihuzhu.cf.model.clewtrack.ScanQRCodeNoticeEnum;
import com.shuidihuzhu.cf.model.clewtrack.ScanQRCodeNoticeModel;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @date 2019-09-26
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class BaiduFeedReportDelegateTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private CfInitialAuditApproveMsgConsumer cfInitialAuditApproveMsgConsumer;
    @Resource
    private IBaiduFeedConvertInfoFacade baiduFeedConvertInfoFacadeImpl;
    @Autowired
    private NoticeClewTrackAddBdCrmClewConsumer noticeClewTrackAddBdCrmClewConsumer;
    @Autowired
    private QianZhiTaskScheduleJob qianZhiTaskScheduleService;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Test
    public void reportBaidu(){
        Date end = new Date();
        Date start = DateUtils.addHours(end,-2);
//        UserConvertInfo userConvertInfo = new UserConvertInfo();
//        ConversionType conversionType = new ConversionType();
//        conversionType.setUid(29027423L);
//        conversionType.setConvertType(3);
//        conversionType.setIsConvert(1);
//        conversionType.setLogidUrl("https://www.123.com/");
//        List<ConversionType> conversionTypes = Lists.newArrayList(conversionType);
//        userConvertInfo.setToken("w42iZMX0WSLhUtrr9Nhu8Em7Iskzv9Mx@6M6W6Kbb8qG6IAMmNIVYDYdqGCk28fUE");
//        userConvertInfo.setConversionTypes(conversionTypes);
//        BaiduFeedResponse response = baiduFeedReportDelegateImpl.reportBaiduConvertinfo(userConvertInfo);
        String str = "爱福家迪卡龙减肥的拉开金额我让你爱福家迪卡龙减肥的拉开金额我让你爱福家迪卡龙减肥的拉开金额我让你爱福家迪卡龙减肥的拉开金额我让你爱福家迪卡龙减肥的拉开金额我让你爱福家迪卡龙减肥的拉开金额我让你爱福家";
        String s2 = oldShuidiCipher.aesEncrypt(str);
        log.info("-------长度：{} s2密文:{}",s2.length(),s2);
    }

    @Test
    public void reportDeepConvert(){
        baiduFeedConvertInfoFacadeImpl.reportDeepConvertInfoByPhoneAndPrimaryChannel("***********",
                CfClewBaseInfoEnums.PrimaryChannelEnum.FEED_BAIDU.getPrimaryChannel());
    }
    @Test
    public void noticeClewTrackAddBdCrmClewConsumer(){
        ConsumerMessage<ScanQRCodeNoticeModel> mqMessage = new ConsumerMessage();
        ScanQRCodeNoticeModel scanQRCodeNoticeModel = new ScanQRCodeNoticeModel();
        scanQRCodeNoticeModel.setType(ScanQRCodeNoticeEnum.FOLLOW_OFFICIAL_ACCOUNT);
        Map<String,String> map = Maps.newHashMap();
        map.put("userId",String.valueOf(0));
        map.put("volunteerUniqueCode","gvcmsr534");
        map.put("phone","***********");
        map.put("channel","BD_sale_user_introduction");
        map.put("clientIp","127.0.0.1");
        map.put("otherInfo", "{\"introducerName\":\"张三\",\"introducerEncryMobile\":\"8ovYyJBP29r8KM1MU0S4ew==\"}");
        scanQRCodeNoticeModel.setExtData(map);
        mqMessage.setPayload(scanQRCodeNoticeModel);
        noticeClewTrackAddBdCrmClewConsumer.consumeMessage(mqMessage);
    }

    @Test
    public void cfInitialAuditApproveMsg(){
        ConsumerMessage<InitialAuditItem.InitialAuditOperation> mqMessage = new ConsumerMessage();
        InitialAuditItem.InitialAuditOperation initialAuditOperation = new InitialAuditItem.InitialAuditOperation();
        mqMessage.setPayload(initialAuditOperation);
        initialAuditOperation.setCaseId(2293953);
        cfInitialAuditApproveMsgConsumer.consumeMessage(mqMessage);
    }

    @Test
    public void testqianZhiTaskScheduleService(){
        ShardingContext shardingContext = new ShardingContext(new ShardingContexts("1","qianZhiTaskScheduleJob",1,null,Maps.newHashMap(),1, false, null),1);
        qianZhiTaskScheduleService.doRealExecute(shardingContext);
    }

    public long startTaskAllInOnce(int threadNums, final Runnable task) throws InterruptedException {
        final CountDownLatch startGate = new CountDownLatch(1);
        final CountDownLatch endGate = new CountDownLatch(threadNums);
        for(int i = 0; i < threadNums; i++) {
            Thread t = new Thread() {
                public void run() {
                    try {
                        // 使线程在此等待，当开始门打开时，一起涌入门中
                        startGate.await();
                        try {
                            task.run();
                        } finally {
                            // 将结束门减1，减到0时，就可以开启结束门了
                            endGate.countDown();
                        }
                    } catch (InterruptedException ie) {
                        ie.printStackTrace();
                    }
                }
            };
            t.start();
        }
        long startTime = System.nanoTime();
        System.out.println(startTime + " [" + Thread.currentThread() + "] All thread is ready, concurrent going...");
        // 因开启门只需一个开关，所以立马就开启开始门
        startGate.countDown();
        // 等等结束门开启
        endGate.await();
        long endTime = System.nanoTime();
        System.out.println(endTime + " [" + Thread.currentThread() + "] All thread is completed.");
        return endTime - startTime;
    }
}
