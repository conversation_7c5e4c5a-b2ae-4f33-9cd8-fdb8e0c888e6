package com.shuidihuzhu.cf.clewtrack.service;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserRpcResponse;
import com.shuidihuzhu.account.model.blacklist.MobileBlacklistBatchQueryDto;
import com.shuidihuzhu.account.model.blacklist.UserBlacklistDto;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.ShortUrlDelegate;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.feign.snake.UserBlacklistFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j

public class BlacklistBatchTest {


    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private UserBlacklistFeignClient userBlacklistFeignClient;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private ShortUrlDelegate shortUrlDelegate;

    @Test
    public void BlacklistTest() {
        //List<TaskPhoneModel> checkNoRepeatPhones = Lists.newArrayList( );//待测手机号
        List<String> predictiveCallPhones = Lists.newArrayList("18523587313", "17300983908", "18900867451");//原始手机号列表
        //根据原始手机号列表获得加密手机号列表
        List<String> cipherCallPhoneList = new ArrayList<>();//加密手机号列表
        for (String callPhone : predictiveCallPhones) {
            cipherCallPhoneList.add(oldShuidiCipher.aesEncrypt(callPhone));
        }
        log.info("cipherCallPhoneList:{}", JSONObject.toJSONString(cipherCallPhoneList));

        //
        //根据加密手机号列表获取黑名单列表
        MobileBlacklistBatchQueryDto batchQueryDto = new MobileBlacklistBatchQueryDto();
        List<Integer> blackListBizTypes = Lists.newArrayList(0, 2);
        batchQueryDto.setBizTypes(blackListBizTypes);//0,2
        batchQueryDto.setCryptoMobiles(cipherCallPhoneList);
        UserRpcResponse<List<UserBlacklistDto>> blacklistBatchResponse = userBlacklistFeignClient.batchGetBlacklistByMobile(batchQueryDto);
        log.info("blacklistBatchResponse:{}", JSONObject.toJSONString(blacklistBatchResponse));

        //去除手机号列表中黑名单列表中禁止拨打的部分手机号
        for (UserBlacklistDto blacklistDto : blacklistBatchResponse.getData()) {
            if (cipherCallPhoneList.contains(blacklistDto.getCryptoMobile())) {//在黑名单中，再判断是哪种禁止类型
                //对cipherCallPhoneList进行解密
                if (blacklistDto.getForbidVoiceCall() || blacklistDto.getForbidMarketVoiceCall()) {
                    String cipherCall = shuidiCipher.decrypt(blacklistDto.getCryptoMobile());
                    predictiveCallPhones.remove(cipherCall);
                }
            }
        }
        log.info("predictiveCallPhones:{}", JSONObject.toJSONString(predictiveCallPhones));
    }

    @Test
    public void testShortUrl() {
        String process = shortUrlDelegate.process("https://www.shuidichou.com/raise/new-launch/online-instead-fill?channel=wx_null_111_launch&primeservice=wx_1&info=10ctWE9C9kddhtVLz1H1GEbO7b2S7fonUy%2BDcZqYNt0%3D");
        System.out.println(process);
    }
}

