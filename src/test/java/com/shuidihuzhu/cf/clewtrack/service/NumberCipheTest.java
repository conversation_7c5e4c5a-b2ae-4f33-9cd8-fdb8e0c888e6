package com.shuidihuzhu.cf.clewtrack.service;


import com.shuidihuzhu.account.model.UserRpcResponse;
import com.shuidihuzhu.account.model.blacklist.MobileBlacklistQueryDto;
import com.shuidihuzhu.account.model.blacklist.UserBlacklistDto;
import com.shuidihuzhu.account.model.blacklist.UserBlacklistUpdateDto;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO;
import com.shuidihuzhu.cf.clewtrack.enums.TianRunEnums;
import com.shuidihuzhu.cf.clewtrack.result.tianrun.PreviewOutCallResult;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.feign.snake.UserBlacklistFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j


public class NumberCipheTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
    @Autowired
    private UserBlacklistFeignClient userBlacklistFeignClient;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private ICfClewCallRecordsService cfClewCallRecordsService;

    @Test
    public void testSaveCallRecords() {
        CfClewCallRecordsDO cfClewCallRecordsDO = CfClewCallRecordsDO.builder()
                .userId("111111")
                .uniqueId("1111111")
                .customerNumber("13233323206")
                .status(TianRunEnums.TianRunCnoStatusEnum.RINGING.getStatus())
                .pageName("测试")
                .caseId(123456L)
                .callType(TianRunEnums.TianRunManualCallTypeEnum.ManualCallType_0.getType())
                .clientName("测试")
                .operatorType("测试")
                .orgName("测试")
                .workOrderId(123456L)
                .build();
        cfClewCallRecordsService.saveCallRecords(cfClewCallRecordsDO);
    }


    @Test
    public void numberCipheTesx(){
        MobileBlacklistQueryDto mbq = new MobileBlacklistQueryDto();
        List<Integer> list = new ArrayList<>();
        list.add(0);
        list.add(2);
        mbq.setBizTypes(list);//0,2
        //给手机号加密
        String numberCipher = oldShuidiCipher.aesEncrypt("18523587313");
        //String numberCipherNew = shuidiCipher.encrypt("110");
        mbq.setCryptoMobile(numberCipher);
        //获取黑名单
        UserRpcResponse<List<UserBlacklistDto>> blacklist = userBlacklistFeignClient.getBlacklistByMobile(mbq);
        //判断电话是否在黑名单中
        for ( UserBlacklistDto result: blacklist.getData()) {
            if (result.getCryptoMobile().equals(numberCipher)){
                PreviewOutCallResult previewOutCallResult = new PreviewOutCallResult();
                //判断禁止类型
                if (result.getForbidVoiceCall()){//禁止语音外呼
                    previewOutCallResult.setRes("100");
                    System.out.println(previewOutCallResult.getRes());
                }
                if (result.getForbidMarketVoiceCall()){//禁止营销语音外呼
                    previewOutCallResult.setRes("101");
                    System.out.println(previewOutCallResult.getRes());
                }
                break;
            }
            System.out.println("可正常拨打电话");
        }



    }

    @Test
    public void updateBlackList(){
        UserBlacklistUpdateDto updateDto = new UserBlacklistUpdateDto();
        updateDto.setBizType(0);
        String numberCipher = oldShuidiCipher.aesEncrypt("18523587313");
        updateDto.setCryptoMobile(numberCipher);
        updateDto.setForbidNormalSMS(false);
        updateDto.setForbidMarketSMS(false);
        updateDto.setForbidVerificationCode(false);
        updateDto.setForbidVoiceCall(true);//禁止普通语音外呼
        updateDto.setForbidMarketVoiceCall(true);//禁止营销语音外呼
        updateDto.setForbidWXPush(false);
        updateDto.setForbidAPPPush(false);
        updateDto.setForbidWeComAdd(false);
        updateDto.setWxComplaint(false);
        updateDto.setRemark("手机号");
        updateDto.setChannelId(99);
        updateDto.setOperatorId(92424);
        updateDto.setOperator("baichenxi");
        //更新黑名单数据
         userBlacklistFeignClient.update(updateDto);

    }

}
