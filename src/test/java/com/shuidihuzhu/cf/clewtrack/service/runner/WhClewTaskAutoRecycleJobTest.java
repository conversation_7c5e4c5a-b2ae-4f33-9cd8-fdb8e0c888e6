package com.shuidihuzhu.cf.clewtrack.service.runner;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.runner.WhClewTaskAutoRecycleJob;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: wanghui
 * @time: 2019/3/22 4:54 PM
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class WhClewTaskAutoRecycleJobTest extends AbstractTest {

    @Autowired
    WhClewTaskAutoRecycleJob whClewTaskAutoRecycleJob;

    @Test
    public void doExecute1() throws Exception {
        whClewTaskAutoRecycleJob.execute(shardingContext(0,1));
    }
}
