package com.shuidihuzhu.cf.clewtrack.service.runner;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.runner.Cf1V1FollowUpTaskJob;
import com.shuidihuzhu.cf.clewtrack.runner.FollowUpTaskJob;
import com.shuidihuzhu.cf.clewtrack.runner.WhClewTaskAutoRecycleJob;
import com.shuidihuzhu.cf.clewtrack.runner.assigntask.DonateServiceTaskScheduleJob;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @author: wanghui
 * @time: 2019/3/22 4:54 PM
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ClewTaskAutoAssignJobTest extends AbstractTest {

    @Autowired
    WhClewTaskAutoRecycleJob whClewTaskAutoRecycleJob;
    @Autowired
    private FollowUpTaskJob followUpTaskJob;

    @Autowired
    private DonateServiceTaskScheduleJob donateServiceTaskScheduleJob;

    @Autowired
    private Cf1V1FollowUpTaskJob cf1V1FollowUpTaskJob;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void doExecuteTaskAutoAssign() {
        whClewTaskAutoRecycleJob.doRealExecute(shardingContext(0,1));
    }


    @Test
    public void doFollowUpTaskJob(){
        followUpTaskJob.doRealExecute(shardingContext(0,1));
    }

    @Test
    public void doDonateServiceTaskScheduleJob(){
        donateServiceTaskScheduleJob.doRealExecute(shardingContext(0,1));
    }

    @Test
    public void doCf1V1FollowUpTaskJob(){
        cf1V1FollowUpTaskJob.doRealExecute(shardingContext(0,1));
    }
}
