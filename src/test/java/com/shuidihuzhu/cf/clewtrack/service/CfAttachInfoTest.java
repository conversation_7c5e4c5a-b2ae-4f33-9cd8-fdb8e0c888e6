package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.facade.ICfQyWxBindClewFacade;
import com.shuidihuzhu.cf.clewtrack.model.vo.FuwuBaseInfoVO;
import com.shuidihuzhu.cf.clewtrack.model.vo.InfoVO;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.clewtrack.service.clewconfig.CfClewFuwuUserUnderTakeInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019-09-04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class CfAttachInfoTest {

    @Autowired
    private ICfClewAttachInfoService cfClewAttachInfoServiceImpl;

    @Autowired
    private ICfQyWxBindClewFacade cfQyWxBindClewFacade;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void update(){
        Long id = 5331L; Integer transWorkContentType = 0;
        cfClewAttachInfoServiceImpl.updateClewAttachInfoWithTransWorkContentType(id,transWorkContentType);
    }

    @Test
    public void test(){
        OpResult<FuwuBaseInfoVO> o = cfQyWxBindClewFacade.getFuwuBaseInfoVO("houyufei","wmPLO1EQAAZeAXq5gycfwkK2-_RG_hrQ");
        System.out.println("123123"+JSON.toJSONString(o));

        OpResult<InfoVO>  p = cfQyWxBindClewFacade.getInfoVO("houyufei","wmPLO1EQAAZeAXq5gycfwkK2-_RG_hrQ");

        System.out.println("123123"+JSON.toJSONString(p));

    }
}
