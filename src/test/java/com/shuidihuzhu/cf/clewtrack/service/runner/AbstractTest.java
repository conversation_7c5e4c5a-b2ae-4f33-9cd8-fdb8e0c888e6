package com.shuidihuzhu.cf.clewtrack.service.runner;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.executor.ShardingContexts;
import com.shuidihuzhu.cf.clewtrack.Application;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: <PERSON>
 * @date: 2018/1/12 15:44
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class AbstractTest {

	static {
		System.setProperty("spring.cloud.consul.host", "k8s-discovery-bedin.shuidi.io:80");
	}

	public ShardingContext shardingContext(int shardingItem, int shardingTotalCount, String jobParameter) {
		Map<Integer, String> objectMap = new HashMap<>();
		for (int i = 0; i <= shardingTotalCount; i++) {
			objectMap.put(i, null);
		}
		ShardingContexts contexts =
				new ShardingContexts(getClass().getSimpleName(), getClass().getSimpleName(), shardingItem, jobParameter, objectMap, false, null);
		return new ShardingContext(contexts, shardingItem);

	}

	public ShardingContext shardingContext(int shardingItem, int shardingTotalCount) {
		return shardingContext(shardingItem, shardingTotalCount, "");
	}
}
