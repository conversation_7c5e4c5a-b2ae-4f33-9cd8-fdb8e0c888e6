package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.google.gson.reflect.TypeToken;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.IALiCloudOutBoundBotDelegate;
import com.shuidihuzhu.cf.clewtrack.delegate.ITianRunBlacklistDelegate;
import com.shuidihuzhu.cf.clewtrack.delegate.impl.TianRunBlacklistDelegate;
import com.shuidihuzhu.cf.clewtrack.model.tianrun.predictivecall.TaskTelList;
import com.shuidihuzhu.cf.clewtrack.model.tianrun.predictivecall.TianRunPredictiveCallResultModel;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.clewtrack.tianrun.client.TianRunPredictiveCallFeignClient;
import com.shuidihuzhu.common.web.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 天润黑名单代理接口测试类
 * @Author: xuhongfeng
 * @Date: 2024/07/02 16:30
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TianRunBlacklistDelegateTest {
    
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private ITianRunBlacklistDelegate tianRunBlacklistDelegate;
    
    @Autowired
    private IALiCloudOutBoundBotDelegate aLiCloudOutBoundBotDelegate;
    
    /**
     * 测试方法1：直接使用实际的服务来测试，测试从天润系统获取黑名单
     * 参考TianRunPredictiveCallDelegate.listTaskTel方法实现
     */
    @Test
    public void testGetBlacklistPhones() {
        // 获取黑名单的第一页，每页10条
        int limit = 50;
        int offset = 0;
        
        try {
            List<String> blacklistPhones = tianRunBlacklistDelegate.getBlacklistPhones(limit, offset);
            log.info("提取的黑名单手机号列表: {}", JSON.toJSONString(blacklistPhones));
        } catch (Exception e) {
            log.error("测试获取黑名单手机号列表异常", e);
        }
    }
    
    /**
     * 测试方法2：使用模拟的方式测试同步单条黑名单数据到阿里云
     */
    @Test
    public void testSyncSinglePhoneToAliCloud() {
        tianRunBlacklistDelegate.syncBlacklistToAliCloud();
    }
    
    /**
     * 测试方法3：直接同步一个指定手机号到阿里云（产生实际影响）
     */
    @Test
    public void testSyncSpecificPhoneDirectly() {
        String testPhoneNumber = "18147668612"; // 替换为您要测试的实际手机号
        
        try {
            // 创建仅包含一个测试手机号的列表
            List<String> testPhones = Collections.singletonList(testPhoneNumber);
            
            // 直接调用阿里云接口将这个手机号添加到黑名单
            OpResult<Integer> result = aLiCloudOutBoundBotDelegate.saveContactBlockList(testPhones);
            
            // 验证结果
            log.info("同步单个黑名单结果: {}", JSON.toJSONString(result));
            if (result.isSuccess()) {
                log.info("成功将手机号{}添加到阿里云黑名单", testPhoneNumber);
            } else {
                log.error("添加手机号{}到阿里云黑名单失败: {}", testPhoneNumber, result.getMessage());
            }
        } catch (Exception e) {
            log.error("测试直接同步黑名单到阿里云异常", e);
        }
    }
} 