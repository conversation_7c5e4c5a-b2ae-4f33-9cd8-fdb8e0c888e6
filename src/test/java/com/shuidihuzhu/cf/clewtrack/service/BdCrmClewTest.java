package com.shuidihuzhu.cf.clewtrack.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.constants.CfClewCommonCons;
import com.shuidihuzhu.cf.clewtrack.delegate.*;
import com.shuidihuzhu.cf.clewtrack.facade.ICfClewTaskBdFacade;
import com.shuidihuzhu.cf.clewtrack.utils.CrowdfundingUtil;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfLocationVo;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.growthtool.model.OrgInfoModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-07-17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class BdCrmClewTest {

    @Resource
    private ICfClewInfoService clewInfoService;

    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
    @Autowired
    private IVolunteerDelegate volunteerDelegate;
    @Autowired
    private ICfGrowthtoolApiDelegate cfGrowthtoolApiDelegate;
    @Autowired
    private ICfClewTaskBdFacade cfClewTaskBdFacade;

    @Autowired
    private ICfClewTaskTransferService taskTransferService;

    @Test
    public void tes11(){
        taskTransferService.callRobot("JkEJ+QXlL+itJORUG51LdQ==", "11232", 1L);
    }

    private boolean isCirculatedBd(List<String> uniqueCodes){
        //unqiueCode列表为空
        if(CollectionUtils.isEmpty(uniqueCodes)){
            return false;
        }
        //根据uniqueCode查询是否是BD
        List<Integer> volunteerTypes = volunteerDelegate.getVolunteerTypeListByUniqueCodes(uniqueCodes);
        List<Integer> bdvolunteerTypes = volunteerTypes.stream().filter(val -> val.equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bdvolunteerTypes)){
            //流转给BD
            return true;
        }
        //没有流转给bd
        return false;
    }

    @Test
    public void testgetCrowdfundingVolunteerByCityName(){
        CrowdfundingVolunteer crowdfundingVolunteerByCityName = this.getCrowdfundingVolunteerByCityName("济南", "geren");
        log.error(this.getClass().getSimpleName()+"  getCrowdfundingVolunteerByCityName :{}",crowdfundingVolunteerByCityName);
    }
    private CrowdfundingVolunteer getCrowdfundingVolunteerByCityName(String cityName, String desc){
        if (org.apache.commons.lang.StringUtils.isBlank(cityName)){
            return null;
        }
        String cityNameR = cityName.replace("市","");

        // 查询 增长-渠道  下所有组织
        List<OrgInfoModel> orgInfoModels = cfGrowthtoolApiDelegate.getOfflineAllOrg();
        // 根据 ip解析出来的 城市 和 上面的组织名进行 模糊匹配
        List<OrgInfoModel> resultOrg = orgInfoModels.stream().filter(orgInfoModel -> orgInfoModel.getOrgName().indexOf(cityNameR) >= 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultOrg)){
            log.warn(this.getClass().getSimpleName()+" allOrgMembersResult filter result is null  ");
            return null;
        }

        resultOrg = resultOrg.stream().sorted((o1, o2) -> Integer.compare(o1.getOrgId(),o2.getOrgId())).collect(Collectors.toList());
        // 如果查询多个选最后一个 因为会存在  重庆-重庆0   其实重庆0 才是城市那一层级
        OrgInfoModel orgInfoModel = resultOrg.get(resultOrg.size()-1);
        List<OrgInfoModel> provinceOrgList = Lists.newArrayList();
        try {
            provinceOrgList = orgInfoModels.stream().filter(infoModel -> {
                if (infoModel.getOrgName().equals(orgInfoModel.getOrgName().substring(0,orgInfoModel.getOrgName().lastIndexOf(CfClewCommonCons.ORG_SPLIT)))) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());

        }catch (Exception e){
            log.warn(this.getClass().getSimpleName()+" 组织名 是顶级组织  ");
            return null;
        }
        if (CollectionUtils.isEmpty(provinceOrgList)){
            log.warn(this.getClass().getSimpleName()+" allOrgMembersResult filter result is null  ");
            return null;
        }
        // 根据parentOrgId  查询组织下的人员  （省级经理）
        //OrgMembersResult orgMebmbers = seaAccountServiceDelegate.getOrgMembersFromGrowthtoolForBdCrm(provinceOrgList.get(0).getOrgId());
        //if (orgMebmbers==null || CollectionUtils.isEmpty(orgMebmbers.getMembers())){
        //    log.warn(this.getClass().getSimpleName()+" getOrgMebmbers result is null  ");
        //    return null;
        //}
        //List<String> misList = orgMebmbers.getMembers().stream().map(SimpleUserVo::getMis).collect(Collectors.toList());
        //List<CrowdfundingVolunteer> volunteerListByMisList = volunteerDelegate.getVolunteerListByMisList(misList).stream()
        //        .filter(crowdfundingVolunteer -> VolunteerEnums.WorkStatusEnum.ON_THE_JOB.getValue()==crowdfundingVolunteer.getWorkStatus()).collect(Collectors.toList());
        //if (CollectionUtils.isEmpty(volunteerListByMisList)){
        //    log.warn(this.getClass().getSimpleName()+" getVolunteerListByMisList result is null  ");
        //    return null;
        //}
        //return volunteerListByMisList.get(RandomUtils.nextInt(0,volunteerListByMisList.size()));
        return null;
    }
    @Test
    public void testgetIp(){
        FeignResponse<CfLocationVo> provinceAndCityResult = crowdfundingFeignClient.getProvinceAndCityByIP(CrowdfundingUtil.getIp(""));
        FeignResponse<CfLocationVo> provinceAndCityResult1 = crowdfundingFeignClient.getProvinceAndCityByIP(CrowdfundingUtil.getIp(null));
        FeignResponse<CfLocationVo> provinceAndCityResult2 = crowdfundingFeignClient.getProvinceAndCityByIP(CrowdfundingUtil.getIp(0));
        log.error("provinceAndCityResult  :{}",provinceAndCityResult );
        log.error("provinceAndCityResult1 :{}",provinceAndCityResult1);
        log.error("provinceAndCityResult2 :{}",provinceAndCityResult2);
    }

    @Test
    public void test11(){
//        List<String> allMis = cfClewTaskBdServiceImpl.getAllMis();
//        if (CollectionUtils.isEmpty(allMis)){
//            return;
//        }
//        String dateTime = DateUtil.getCurrentDateStr();
//        List<String> existMisList = cfClewTaskBdServiceImpl.getExistMisList(dateTime);
//        allMis.removeAll(existMisList);
//        List<List<String>> listList = Lists.partition(allMis, CfClewCommonCons.SEARCH_SIZE);
//        listList.parallelStream().forEach(list -> cfClewTaskBdServiceImpl.batchInsertCfCrmAssignCount(list,dateTime));
//        bdCrmClewFacade.assignBdCrmClew(28323L+"",null);
    }
}
