package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.utils.ShuidiCipherUtils;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-02-11
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class ShuidiCipherUtilTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Test
    public void testDecrypt(){
        String encryptPhone = "DdwJ7SlxAEAnLuz3Rj/4lQ==";
        String result = ShuidiCipherUtils.decrypt(encryptPhone);
        System.out.println(result);
    }
}
