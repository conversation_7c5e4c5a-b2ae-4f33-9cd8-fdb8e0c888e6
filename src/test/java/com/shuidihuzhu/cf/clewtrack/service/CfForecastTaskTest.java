package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.ICfForecastTaskDelegate;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-09-03
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class CfForecastTaskTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Autowired
    private ICfForecastTaskDelegate cfForecastTaskDelegateImpl;
    @Autowired
    private ICfForecastCallTaskService cfForecastCallTaskServiceImpl;

    @Test
    public void get(){
        cfForecastTaskDelegateImpl.checkIsHaveNotEndForecastTaskWithToday(0);
    }

    @Test
    public void query(){
        log.info("6555:{}",cfForecastCallTaskServiceImpl.getCfForecastCallTaskByTaskId(6555));
        log.info("6095:{}",cfForecastCallTaskServiceImpl.getCfForecastCallTaskByTaskId(6095));
    }

    @Test
    public void update(){
        int result1 = cfForecastCallTaskServiceImpl.updateStatus(6555,0);
        log.info("result:{}",result1);
        int result2 = cfForecastCallTaskServiceImpl.updateStatus(6555,3);
        log.info("result2:{}",result2);
    }
}
