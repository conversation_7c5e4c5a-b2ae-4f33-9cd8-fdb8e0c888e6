package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.tianrunclink.ICfTianRunSdkClientDelegate;
import com.shuidihuzhu.cf.clewtrack.delegate.tianrunclink.ITianrunClinkFactory;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewCallRecordsDO;
import com.shuidihuzhu.cf.clewtrack.facade.ITianRunFacade;
import com.shuidihuzhu.cf.clewtrack.model.vo.ClientAgentInfo;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.clewtrack.result.tianrun.HangUpResult;
import com.shuidihuzhu.cf.clewtrack.result.tianrun.PreviewOutCallResult;
import com.tinet.clink.openapi.model.CdrObRecordModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-10-10
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class TianRunSdkClientDelegateTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private ITianRunFacade tianRunFacade;

    @Resource(name = "TianrunClinkV2Delegate")
    private ICfTianRunSdkClientDelegate tianrunClinkV2Delegate;

    @Autowired
    private ITianrunClinkFactory tianrunClinkFactory;

    @Test
    public void call(){
        PreviewOutCallResult previewOutCallResult = tianrunClinkV2Delegate.previewOutcall("3003","13552080120","1", true);
        log.info("previewOutCallResult:{}",previewOutCallResult);
    }

    @Test
    public void getClientInfo(){
        OpResult<ClientAgentInfo> result = tianRunFacade.getClientInfo("3002");
        log.info("result:{}",result.getData());
    }

    @Test
    public void clientOffline(){
        HangUpResult result = tianrunClinkFactory.clientOffline("3003");
        OpResult<ClientAgentInfo> opResult = tianRunFacade.getClientInfo("3003");
        if (opResult.isSuccessWithNotNullData() && StringUtils.isNotEmpty(opResult.getData().getBindTel())) {
            tianrunClinkFactory.unBindTel("3003",opResult.getData().getBindTel());
        }
        log.info("result:{}",result);
    }

    @Test
    public void clientOnine(){
        HangUpResult result = tianrunClinkFactory.clientOnline("3003","18310535756",1);
        log.info("result:{}",result);
    }

    @Test
    public void getCfClewCallRecordsDO(){
        // medias_1-1606368723.49445 坐席未接听
        // medias_2-1606359439.27217 双方接听
        // medias_3-1606375728.77474 客户未接听
        OpResult<CfClewCallRecordsDO> opResult = tianrunClinkFactory.getCfClewCallRecordsDO("medias_3-1606375728.77474");
        log.info("data:{}",opResult.getData());
    }

    @Test
    public void handleIncommigCallRecord(){
        // medias_5-1606376319.80031 人工接听
        // medias_5-1606376262.79788
        OpResult<CfClewCallRecordsDO> opResult = tianrunClinkFactory.handleIncommigCallRecord("medias_5-1606376262.79788");
        log.info("data:{}",opResult.getData());
    }

    @Test
    public void listCdrObs(){
        String cno = "6205"; String phone = "13842490759";
        OpResult<CdrObRecordModel> opResult = tianrunClinkV2Delegate.listCdrObs(cno,phone);
        log.info("data:{}", JSON.toJSONString(opResult.getData()));
    }
}
