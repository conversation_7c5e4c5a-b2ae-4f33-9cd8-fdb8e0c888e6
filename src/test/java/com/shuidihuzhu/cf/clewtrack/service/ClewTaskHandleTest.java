package com.shuidihuzhu.cf.clewtrack.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.IMsgCallRobotClientV2Delegate;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.facade.IClewTaskFacade;
import com.shuidihuzhu.cf.clewtrack.model.ClewTaskHandleTestModel;
import com.shuidihuzhu.cf.clewtrack.param.CfTaskTypeTestQueryParam;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.clewtrack.service.taskassign.creator.ITaskCreator;
import com.shuidihuzhu.cf.clewtrack.service.taskassign.creator.TaskFactory;
import com.shuidihuzhu.msg.request.CallRobotRecordSendRequest;
import com.shuidihuzhu.msg.request.TemplateInfoItem;
import com.shuidihuzhu.msg.response.CallRobotRecordSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-02-13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class ClewTaskHandleTest {

    @Autowired
    private IClewTaskFacade clewTaskFacade;

    @Autowired
    private ICfClewTaskHandleTestService cfClewTaskHandleTestService;

    @Autowired
    private TaskFactory taskFactory;

    @Autowired
    private ICfClewInfoService clewInfoService;

    @Autowired
    private IMsgCallRobotClientV2Delegate msgCallRobotClientV2Delegate;


    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testCall() {
        CallRobotRecordSendRequest callRobotRecordSendRequest = new CallRobotRecordSendRequest();
        TemplateInfoItem templateInfoItem = new TemplateInfoItem();
        templateInfoItem.setMobile("JkEJ+QXlL+itJORUG51LdQ==");
        templateInfoItem.setCustomParam(Lists.newArrayList(Map.of("0", "12")));
        callRobotRecordSendRequest.setTemplateInfo(Lists.newArrayList(templateInfoItem));
        callRobotRecordSendRequest.setBizName("CHOU_CLEW_TRACK");
        callRobotRecordSendRequest.setTemplateNumber(1024L);
        //通知外呼平台打电话
        CallRobotRecordSendResponse sendResponse = msgCallRobotClientV2Delegate.callRobotRecordSend(callRobotRecordSendRequest);
        System.out.println(sendResponse);
    }

    @Test
    public void test(){
        String phone = "yepnsCU+im0Umm3K7cOgGg==";
        /*CfTaskTypeTestQueryParam params = new CfTaskTypeTestQueryParam();
        params.setPhone(phone);
        params.setRecordType(2);
        params.setRegType(0);
        params.setTimestamp(1581559162000L);
        if(params.checkParams()) return;
        OpResult opResult = clewTaskFacade.postTaskTypeTest(params);*/

        ClewTaskHandleTestModel modelHandle = cfClewTaskHandleTestService.getClewTaskHandleTest(new CfTaskTypeTestQueryParam(
                phone, 2));

        System.out.println();

    }

    @Test
    public void testChannel(){
        CfClewBaseInfoDO baseInfoDO = clewInfoService.getClewBaseInfoByClewId(179398L);
        OpResult<Boolean> initResult = taskFactory.initAndSaveTask(ITaskCreator.CreateSource.from_register, baseInfoDO);
    }
}
