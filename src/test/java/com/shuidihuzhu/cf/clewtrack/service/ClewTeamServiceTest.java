package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.clewtrack.model.admin.AdminUserAccountModel;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * <AUTHOR>
 * @date 2019-09-05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class) // 指定我们SpringBoot工程的Application启动类
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class
})
@WebAppConfiguration
@Slf4j
public class ClewTeamServiceTest {

    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;
    static {
        System.setProperty("spring.cloud.consul.host","consul.zelda.shuiditech.com:80");
    }

    @Test
    public void queryName(){
        AdminUserAccountModel accountModel = seaAccountServiceDelegate.getSeaAccountByMis("cairuifang");
        log.info("accountModel:{}",accountModel);
    }

    @Test
    public void queryOrg(){
        AdminUserAccountModel accountModel = seaAccountServiceDelegate.getSeaAccountByMis("cairuifang");
        String org = seaAccountServiceDelegate.getGroupNameByUserId(accountModel.getId());
        log.info("orgName:{}",org);
    }
}
