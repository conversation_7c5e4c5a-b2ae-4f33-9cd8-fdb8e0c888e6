package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CfDonateServiceInfoControllerTest {

    static {
        System.setProperty("spring.cloud.consul.host", "k8s-discovery-bedin.shuidi.io:80");
    }


} 