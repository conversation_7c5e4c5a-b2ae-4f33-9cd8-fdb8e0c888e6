package com.shuidihuzhu.cf.clewtrack.service;

/**
 * <AUTHOR>
 * @date 2019-07-24
 */

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.domain.clewindex.CfClewTaskAttachIndexDO;
import com.shuidihuzhu.cf.clewtrack.domain.clewindex.CfClewTaskIndexDO;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewErrorCode;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewTaskEnums;
import com.shuidihuzhu.cf.clewtrack.model.CFClewRegisterModel;
import com.shuidihuzhu.cf.clewtrack.model.bdcrm.BdCrmClewInfoBaseModel;
import com.shuidihuzhu.cf.clewtrack.param.CfDengjiClewForWhQueryParam;
import com.shuidihuzhu.cf.clewtrack.param.CfFuWuClewPreposeReportParam;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.clewtrack.service.clewindex.ICfClewTaskAttachIndexService;
import com.shuidihuzhu.cf.clewtrack.service.clewindex.ICfClewTaskIndexService;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewBaseInfoDao;
import com.shuidihuzhu.client.cf.admin.model.CfBasePreMsgStatusEnum;
import com.shuidihuzhu.client.cf.clewtrack.model.CfBdClewForPreposeMaterialModel;
import com.shuidihuzhu.client.cf.growthtool.client.ClewPreproseMaterialFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.ClewCasePreproseMaterialRelationModel;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialSaveOrUpdateModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-07-17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class ClewInfoServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Resource
    private ICfClewInfoService clewInfoService;
    @Autowired
    private ICfClewTaskWriteService cfClewTaskWriteService;
    @Autowired
    private ICfClewTaskReadService cfClewTaskReadService;
    @Autowired
    private ICfClewTaskIndexService taskIndexService;
    @Autowired
    private ICfDengjiWhInfoService whInfoService;
    @Autowired
    private CfClewBaseInfoDao cfClewBaseInfoDao;

    @Autowired
    private ClewPreproseMaterialFeignClient clewPreproseMaterialFeignClient;

    @Autowired
    private ICfClewInfoService cfClewInfoService;

    @Autowired
    private ICfClewTaskAttachIndexService cfClewTaskAttachIndexService;

    @Autowired
    private ICfClewTaskReadService clewTaskReadService;


    @Test
    public void testDistinct(){
        CfDengjiClewForWhQueryParam param = new CfDengjiClewForWhQueryParam();
        param.setPhone("13021987359");
        param.setOffset(0);
        param.setPageSize(10);
        //whInfoService.searchTaskInfoByPhone(param);
        List<CfClewTaskIndexDO> cfClewTaskIndexDOList = whInfoService.queryCfClewTaskIndex(param);
        log.info("cfClewTaskIndexDOList:{}",cfClewTaskIndexDOList);

    }


    @Test
    public void checkInfo(){
       List<CfClewBaseInfoDO> list = clewInfoService.getClewBaseInfoByEncryptPhoneAndClewType("13263222132");
       CfClewBaseInfoDO cfClewBaseInfoDO = list.get(0);
       BdCrmClewInfoBaseModel b = cfClewBaseInfoDO.bulidBdCrmClewInfoBaseModel();
       log.info("b:{}",b);
       log.info("list:{}", JSON.toJSONString(list));
    }

    private void compareCfBdClewForPreposeMaterialModel(CfBdClewForPreposeMaterialModel origin, CfBdClewForPreposeMaterialModel replace) {
        log.info("result:{}",origin.equals(replace));
    }

    private void compare(List<CfClewBaseInfoDO> originList , List<CfClewBaseInfoDO> replaceList){
        Map<Long,CfClewBaseInfoDO> originMap = originList.stream().collect(Collectors.toMap(CfClewBaseInfoDO::getId, Function.identity()));
        Map<Long,CfClewBaseInfoDO> replaceMap = replaceList.stream().collect(Collectors.toMap(CfClewBaseInfoDO::getId, Function.identity()));
        int count = 0;
        for (Long id : originMap.keySet()){
            if (replaceMap.getOrDefault(id,null) != null){
                count++;
            }
        }
        if (count == originList.size()){
            log.info("true");
        }else{
            log.info("false");
        }
    }

    private void compares(List<CFClewRegisterModel> origin, List<CFClewRegisterModel> replace){
        Map<String,CFClewRegisterModel> originMap = origin.stream().collect(Collectors.toMap(CFClewRegisterModel::getClewCreateTime, Function.identity()));
        Map<String,CFClewRegisterModel> replaceMap = replace.stream().collect(Collectors.toMap(CFClewRegisterModel::getClewCreateTime, Function.identity()));
        int count = 0;
        for (String id : originMap.keySet()){
            if (replaceMap.getOrDefault(id,null) != null){
                count++;
            }
        }
        if (count == origin.size()){
            log.info("true");
        }else{
            log.info("false");
        }
    }
    private void compareList(List<CFClewRegisterModel> origin, List<CFClewRegisterModel> replace){
        boolean res = true; int count = 0;
        for (CFClewRegisterModel clewRegisterModel : origin){
            res &= clewRegisterModel.equals(replace.get(count));
            count++;
        }
        log.info("res:{}",res);
    }

    private void compareCfClewBaseInfoDO(CfClewBaseInfoDO origin, CfClewBaseInfoDO replace) {
        log.info("result:{}",replace.equals(origin));
    }

    private void compareBdCrmClewInfoBaseModel(BdCrmClewInfoBaseModel origin, BdCrmClewInfoBaseModel replace) {
        log.info("result:{}",replace.equals(origin));
    }

    private void compareBdCrmClewList(List<BdCrmClewInfoBaseModel> origin, List<BdCrmClewInfoBaseModel> replace) {
        boolean res = true; int count = 0;
        for (BdCrmClewInfoBaseModel clewRegisterModel : origin){
            res &= clewRegisterModel.equals(replace.get(count));
            count++;
        }
        log.info("res:{}",res);
    }

    @Test
    public void testcopy(){


//        Response<Integer> res =  clewPreproseMaterialFeignClient.createLink(2542);
//        log.info("1111={}",JSON.toJSONString(res));

        Response<String> res = clewPreproseMaterialFeignClient.copyLink(2542);
        log.info("1111={}",JSON.toJSONString(res));

    }

    @Test
    public void save(){

        CfFuWuClewPreposeReportParam param = new CfFuWuClewPreposeReportParam();

        param.setTaskId(122018L);
        param.setClewUserId("xiaoqian");
        ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel c = new ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel();
        c.setDiseaseName("8923429348823748");
        c.setRaiseMobile("13111111111");
        c.setVersion(2);
        c.setPreposeType(1);
        c.setRelationVersion(6);
        c.setPatientIdCardType(1);
        param.setClewPreposeMaterialModel(c);
        saveOrUpdateMsg(param);

    }


    public Response<Void> saveOrUpdateMsg(CfFuWuClewPreposeReportParam reportParam){
        if(reportParam.getTaskId()<=0){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfClewTaskAttachIndexDO attachIndexDO = cfClewTaskAttachIndexService.getCfClewTaskAttachIndexDOByTaskId(reportParam.getTaskId());

        if (attachIndexDO.getPremsgStatus() == CfBasePreMsgStatusEnum.for_chuci.getCode()
                ||attachIndexDO.getPremsgStatus() == CfBasePreMsgStatusEnum.chuci_pass.getCode()
                ||attachIndexDO.getPremsgStatus() == CfBasePreMsgStatusEnum.chuci_reject.getCode()){
            return NewResponseUtil.makeError(CfClewErrorCode.OPRATION_ERRPR);
        }

        CfClewTaskDO cfClewTaskDO = clewTaskReadService.getTaskInfoByTaskId(reportParam.getTaskId());
        if(cfClewTaskDO == null || cfClewTaskDO.getUserId().compareToIgnoreCase(reportParam.getClewUserId().trim())!=0){
            return NewResponseUtil.makeError(CfClewErrorCode.SYSTEM_PARAM_ERROR);
        }
        ClewPreposeMaterialSaveOrUpdateModel saveOrUpdateModel = new ClewPreposeMaterialSaveOrUpdateModel();
        saveOrUpdateModel.setClewId(cfClewTaskDO.getClewId());
        saveOrUpdateModel.setTaskId(reportParam.getTaskId());
        saveOrUpdateModel.setClewPreposeMaterialModel(reportParam.getClewPreposeMaterialModel());
        saveOrUpdateModel.setInfoId(null);
        saveOrUpdateModel.setOperateType(ClewPreposeMaterialSaveOrUpdateModel.OperateTypeEnum.SAVE);
        saveOrUpdateModel.setMis(cfClewTaskDO.getUserId());
        saveOrUpdateModel.setName(cfClewTaskDO.getUserName());
        saveOrUpdateModel.setLeaderMis("yinshuo");
        saveOrUpdateModel.setLeaderName("尹硕");
        Response<com.shuidihuzhu.client.baseservice.notice.v1.OpResult<ClewCasePreproseMaterialRelationModel>> resultResponse =  clewPreproseMaterialFeignClient.saveOrUpdateMaterialInfoVo(saveOrUpdateModel);
        if(resultResponse.notOk()){
            return NewResponseUtil.makeFail(resultResponse.getMsg());
        }
        com.shuidihuzhu.client.baseservice.notice.v1.OpResult<ClewCasePreproseMaterialRelationModel> opResult = resultResponse.getData();
        if(opResult.isFail()){
            return NewResponseUtil.makeFail(resultResponse.getMsg());
        }
        cfClewInfoService.updatePreposeReportData(reportParam.getOperateType(),opResult.getData().getApproveStatus(),reportParam.getClewPreposeMaterialModel().getVersion(),cfClewTaskDO.getClewId());
        cfClewTaskAttachIndexService.savePremsgStatus(reportParam.getTaskId(),CfBasePreMsgStatusEnum.save);

        return NewResponseUtil.makeSuccess(null);

    }
}
