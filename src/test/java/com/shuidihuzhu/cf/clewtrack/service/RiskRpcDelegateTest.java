package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.RiskRpcDelegate;
import com.shuidihuzhu.cf.clewtrack.model.HighRiskTipModel;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.PreposeMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PreposeMaterialModel;
import com.shuidihuzhu.cf.risk.model.risk.highrisk.HighRiskJudgeResult;
import com.shuidihuzhu.client.cf.growthtool.model.ClewPreposeMaterialSaveOrUpdateModel;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/7/22 2:22 PM
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class RiskRpcDelegateTest {

    @Autowired
    private RiskRpcDelegate riskRpcDelegate;

    @Resource
    private PreposeMaterialClient preposeMaterialClient;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void getRiskTipTest() {
        ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel clewPreposeMaterialModel = new ClewPreposeMaterialSaveOrUpdateModel.ClewPreposeMaterialModel();
        RpcResult<PreposeMaterialModel.MaterialInfoVo> materialInfoVoRpcResult = preposeMaterialClient.selectMaterialsById(191606);
        PreposeMaterialModel.MaterialInfoVo data = materialInfoVoRpcResult.getData();
        BeanUtils.copyProperties(data, clewPreposeMaterialModel);
//        OpResult<HighRiskTipModel> riskTip = riskRpcDelegate.getRiskTip(clewPreposeMaterialModel);
//        log.info("result:{}", riskTip);
    }
}
