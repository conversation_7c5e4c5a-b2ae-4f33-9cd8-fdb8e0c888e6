package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.ICShowClueRecordDelegate;
import com.shuidihuzhu.cf.clewtrack.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseLayerDO;
import com.shuidihuzhu.cf.clewtrack.enums.WorkbenchEnums;
import com.shuidihuzhu.cf.clewtrack.facade.ICfClewBaseLayerConfigFacade;
import com.shuidihuzhu.cf.clewtrack.facade.ICfFuwuStatisticFacade;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.client.cf.clewtrack.model.Cf1v1OrgMemberInfo;
import com.shuidihuzhu.client.dataservice.bi.v1.BiApiClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.client.model.SearchDto;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-06-27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class QyWechatTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private BiApiClient biApiClient;
    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;
    @Autowired
    private IClewTrackUserInfoService clewTrackUserInfoServiceImpl;
    @Autowired
    private ICfFuwuStatisticFacade cfFuwuStatisticFacadeImpl;
    @Autowired
    private ICShowClueRecordDelegate CShowClueRecordDelegate;
    @Autowired
    private ICfClewBaseLayerConfigFacade cfClewBaseLayerConfigFacadeImpl;
    @Autowired
    private ICfClewInfoService cfClewInfoServiceImpl;
    @Autowired
    private IClewUserOrgService clewUserOrgServiceImpl;

    @Test
    public void test(){
        Date startTime = DateUtil.getCurrentDate();
        Date endTime = DateUtil.addDay(DateUtil.getCurrentDate(),1);
    }

    @Test
    public void testEs(){
        SearchDto searchDto = new SearchDto();
        String querySql = "select * from shuidi_clewtrack_cf_clew_task_index_new_alias where id in ('37038','37039')";
        searchDto.setQuerySql(querySql);
        Response response = biApiClient.esQueryCustom(searchDto);
        log.info("response:{}",response);
    }

    @Test
    public void testds(){
//        OpResult<List<CfClewTrackUserInfoDO>> userInfoOpResult = clewTrackUserInfoServiceImpl.getUserInfoListByTrackerType(CfClewTrackUserInfoEnums.TrackerUserTypeEnum.BD_FUWU.getType(),0);
//        if (userInfoOpResult.isFailOrNullData()){
//            return;
//        }
//        Map<String,CfClewTrackUserInfoDO> fuwuUserInfoMap = userInfoOpResult.getData().stream().collect(Collectors.toMap(CfClewTrackUserInfoDO::getUserId , Function.identity(), (oldVal, newVal) -> newVal));
//        List<String> misList = Lists.newArrayList(fuwuUserInfoMap.keySet());
        List<Integer> orgList = Lists.newArrayList(144,149);
        Map<String,List<String>> stringListMap = seaAccountServiceDelegate.getUserOrgsByOrgList(orgList);
        Map<String,String> userOrgMap = seaAccountServiceDelegate.getUserOrgsByMisList(stringListMap.get("fuwuStaticsUserList"));
        log.info("stringListMap:{}", JSONObject.toJSONString(stringListMap));
        log.info("userOrgMap:{}",JSONObject.toJSONString(userOrgMap));
    }

    @Test
    public void getFuwuStatisticData(){
        String time = com.shuidihuzhu.common.util.DateUtil.getDate2Str("yyyyMMddHHmm",DateUtil.nowDate());
        cfFuwuStatisticFacadeImpl.getFuwuStatisticData(time);
    }

    @Test
    public void doQuery(){
        Date startDate = DateUtil.getCurrentDate();
        Date endDate = DateUtil.addDay(startDate,1);
        CShowClueRecordDelegate.doCShowClueRecord(startDate, endDate);
    }

    @Test
    public void doU(){
        CfClewBaseInfoDO cfClewBaseInfoDO = cfClewInfoServiceImpl.getClewBaseInfoByClewId(39827L);
        OpResult<ImmutablePair<Long, CfClewBaseLayerDO>> opResult = cfClewBaseLayerConfigFacadeImpl.getLayerConfig(cfClewBaseInfoDO);
    }

    @Test
    public void testa(){
        List<Cf1v1OrgMemberInfo> cf1v1OrgMemberInfos = clewUserOrgServiceImpl.listAllOrgMemberInfoWithWorkbenchType(WorkbenchEnums.WorkbenchTypeEnum.FUWU_WH);
        log.info("cf1v1OrgMemberInfos:{}",cf1v1OrgMemberInfos);
    }
}
