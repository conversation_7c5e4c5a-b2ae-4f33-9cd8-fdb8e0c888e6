package com.shuidihuzhu.cf.clewtrack.service;


import com.github.rholder.retry.*;
import com.shuidihuzhu.cf.clewtrack.utils.GuavaRetryerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.base.Predicates;
import org.junit.Test;

import java.util.Random;
import java.util.concurrent.*;

/**
 * @author: fengxuan
 * @create 2022-02-10 19:23
 **/
@Slf4j
public class GuavaRetryTest {



    @Test
    public void testSimpleRetryer() {

        try {
            //var ref = new Object() {
            //    int i = 0;
            //};
            CompletableFuture<Void> future = CompletableFuture.allOf(CompletableFuture.runAsync(() -> {
                try {
                    GuavaRetryerUtil.retryer.call(() -> {
                        log.info("hello");
                        return false;
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }), CompletableFuture.runAsync(() -> {
                try {
                    GuavaRetryerUtil.retryer.call(() -> {
                        log.info("test");
                        return false;
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }));
            future.get();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private boolean test(int i) {
        Random random = new Random();
        int num = random.nextInt(10);
        log.info("i:{},random:{}", i, num);
        if (num < 5) {
            return true;
        }
        return false;
    }

    @Test
    public void simpleTest() throws InterruptedException {
        ExecutorService executorService = Executors.newFixedThreadPool(2);
        for (int i = 0; i < 10; i++) {
            int finalI = i;
            executorService.submit(() -> {
                try {
                    GuavaRetryerUtil.retryer.call(() -> test(finalI));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        TimeUnit.MINUTES.sleep(10);
    }

    static class SimpleRetryListener implements RetryListener {

        @Override
        public <V> void onRetry(Attempt<V> attempt) {
            log.info("attempt:{}", attempt.getAttemptNumber());
            //log.info("attempt get:{}", attempt.getResult());
        }
    }

}
