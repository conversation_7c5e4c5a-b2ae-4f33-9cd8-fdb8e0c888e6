package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.impl.SeaAccountServiceDelegate;
import com.shuidihuzhu.cf.clewtrack.delegate.impl.VolunteerDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.wisdomdonate.ClewCaseDonateTaskDO;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewBaseInfoEnums;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewTrackUserInfoEnums;
import com.shuidihuzhu.cf.clewtrack.model.admin.OrgMembersResult;
import com.shuidihuzhu.cf.clewtrack.model.bdcrm.BdClewAttachModel;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.fromcfapi.NoticeClewTrackAddBdCrmClewConsumer;
import com.shuidihuzhu.cf.clewtrack.param.CfCallRecordQueryParam;
import com.shuidihuzhu.cf.clewtrack.param.DateQueryParam;
import com.shuidihuzhu.cf.clewtrack.result.OpResult;
import com.shuidihuzhu.cf.clewtrack.runner.CFRecruitFollowUpVisitTaskAssignJob;
import com.shuidihuzhu.cf.clewtrack.service.donate.ClewCaseDonateTaskService;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfLocationVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.constraints.NotNull;
import java.util.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BdCfServiceTest {
    @Autowired
    private SeaAccountServiceDelegate seaAccountServiceDelegate;
    @Autowired
    private CFRecruitFollowUpVisitTaskAssignJob cfRecruitFollowUpVisitTaskAssignJob;
    @Autowired
    private ICfClewCallRecordsService cfClewCallRecordsService;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private VolunteerDelegate volunteerDelegate;

    @Autowired
    private ClewCaseDonateTaskService clewCaseDonateTaskService;
    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }
    @Autowired
    private NoticeClewTrackAddBdCrmClewConsumer noticeClewTrackAddBdCrmClewConsumer;

    @Test
    public void testGetOrgMebmbers() {
        CrowdfundingVolunteer cfVolunteer = volunteerDelegate.getVolunteerByUniqueCode("8bgyzq307");
        CfLocationVo cfLocationVo = new CfLocationVo();
        cfLocationVo.setCityName("北京");
        BdClewAttachModel bdClewAttachModel = noticeClewTrackAddBdCrmClewConsumer.dimissionAccountAssign(cfVolunteer, CfClewBaseInfoEnums.PrimaryChannelEnum.CHUSHEN_REJECT_CHANNEL, cfLocationVo);
        log.info(JSON.toJSONString(bdClewAttachModel));
    }
    @Test
    public void testGetAllOrg(){
//        List<OrgMembersResult> allOrgMembersResult = seaAccountServiceDelegate.getAllOrgMembersResult();
//        List<OrgMembersResult> allOrgMembersResult = seaAccountServiceDelegate.getAllOrgMembersResultByOrgId(CfClewCommonCons.CRM_ORG_ID);
//        allOrgMembersResult.stream().peek(orgMembersResult -> System.out.println("---------"+orgMembersResult)).collect(Collectors.toList());
//        List<OrgMembersResult> allOrgMembersResult1 = seaAccountServiceDelegate.getAllOrgMembersResultByOrgId(CfClewCommonCons.CRM_ORG_ID);
//        allOrgMembersResult1.stream().peek(orgMembersResult -> System.out.println("---------"+orgMembersResult)).collect(Collectors.toList());
    }

    @Autowired
    private IClewTrackUserInfoService trackUserInfoService;
    @Test
    public void testBdCase() {
//        FeignResponse<CfVolunteer> resVol = volunteerFeignClient.getVolunteerByMobile("eN7Zt0mfrlgQ8WOr3oJmgA==");
//
//
//        FeignResponse<List<CrowdfundingOrder>> resOrder = crowdfundingOrderFeignClient.getByUserIds(Sets.newHashSet(269381661L));
//        if(resOrder == null || resOrder.notOk() || resOrder.getData() == null){
//            int s = 10;
//        }
//        CfBdCaseInfoDo infoDo = new CfBdCaseInfoDo();
//        infoDo.setInfoUuid("1");
//        infoDo.setCaseMethod("111");
//        infoDo.setCaseSource("");
//        infoDo.setFirstRaise("");
//        infoDo.setDateCreated(new Timestamp(new Date().getTime()));
//        int count = cfBdCaseInfoService.insert(infoDo);
//
//        List<OrgMembersResult> orgMembersList = seaAccountServiceDelegate.getAllOrgMembersResultByOrgId(57).stream().filter(s -> s.getOrgId() == 57).collect(Collectors.toList());
//        OrgMembersResult orgMembersResult = orgMembersList.get(0);
//        List<SimpleOrgVo> subOrgs = orgMembersResult.getSubOrgs();
        OrgMembersResult houyufei = seaAccountServiceDelegate.getOrgMembersFromGrowthtoolForBdCrm(124);
        System.err.println("getOrgMebmbers :"+houyufei.getMembers());
//        OrgMembersResult resultOrg = orgMembersList.get(0);
//        List<String> mis = resultOrg.getMembers().stream().map(SimpleUserVo::getMis).collect(Collectors.toList());

//        List<OrgMembersResult> orgMembersList1 = seaAccountServiceDelegate.getAllOrgMembersResultByOrgId(97);
//        List<SimpleUserVo> simpleUserVos = orgMembersList1.stream().filter(orgMembersResult -> orgMembersResult.getOrgId() == 97)
//                .flatMap(orgMembersResult -> orgMembersResult.getMembers().stream()).collect(Collectors.toList());
//        System.err.println("orgMembersList1 :"+simpleUserVos);

    }


    @Test
    public void testclewTrack(){
        List<String> userIdList = new ArrayList<>();
        userIdList.add("baisuobao");
        userIdList.add("baijunfeng");
        userIdList.add("zhangwensheng");
        userIdList.add("chenyanping");
        userIdList.add("chenzhen");
        userIdList.add("wangshaoyang");
        StringBuffer qyWechatUserIdStr = new StringBuffer();
        List<String> qyWechatUserIds = trackUserInfoService.getQyWechatUserIdByUserIdBatch(userIdList, CfClewTrackUserInfoEnums.TrackerUserTypeEnum.BD_FUWU.getType());
        qyWechatUserIds.stream().forEach(str -> { qyWechatUserIdStr.append("|" + str); });
        System.out.println("qyWechatUserIdStr :" + qyWechatUserIdStr);

        Map<Integer, List<String>> rejectItems = new HashMap<>();
        List<String> userIdList1 = new ArrayList<>();
        userIdList1.add("hello");
        userIdList1.add("world");
        rejectItems.put(1, userIdList);
        rejectItems.put(2, userIdList1);
        rejectItems.put(3, new ArrayList());
        StringBuffer rejectContent = new StringBuffer();
        rejectItems.values().stream().forEach(str -> { rejectContent.append(str); });
        System.out.println("rejectContent :" + rejectContent);
    }

    @Test
    public void testInsert() {
        ClewCaseDonateTaskDO clewCaseDonateTaskDO = new ClewCaseDonateTaskDO();
        clewCaseDonateTaskDO.setCaseId(2976501);
        clewCaseDonateTaskDO.setCallNums(2);
        clewCaseDonateTaskDO.setCallTime(12);
        clewCaseDonateTaskDO.setCallStatus(2);
        clewCaseDonateTaskDO.setConfigId(57);
        clewCaseDonateTaskDO.setShareCount(2);
        clewCaseDonateTaskDO.setTaskStatus(2);
        clewCaseDonateTaskDO.setShareTarget(34);
        clewCaseDonateTaskService.insert(clewCaseDonateTaskDO);
    }

}
