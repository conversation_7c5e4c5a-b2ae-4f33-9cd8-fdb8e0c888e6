package com.shuidihuzhu.cf.clewtrack.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.ISeaAccountServiceDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewExtInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewPacketDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewRequestStatusChangeLogDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewTaskEnums;
import com.shuidihuzhu.cf.clewtrack.model.ClewWorkContentTypeModel;
import com.shuidihuzhu.cf.clewtrack.service.impl.ClewTrackUserInfoServiceImpl;
import com.shuidihuzhu.cf.clewtrack.service.patientcommunity.ICfPatientAttachService;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewPacketDao;
import com.shuidihuzhu.cf.dao.clewtrack.CfClewRequestStatusChangeLogDao;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

import static com.shuidihuzhu.cf.clewtrack.enums.CfClewTaskEnums.CloseTypeEnum.EXISTOFFLINECLEW;

/**
 * <AUTHOR>
 * @create 2018-09-17 下午5:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class ClewTrackUserInfoServiceTest {
    @Autowired
    private CfClewPacketDao packetDao;

    @Autowired
    private AlarmClient alarmClient;
    @Autowired
    private CfClewRequestStatusChangeLogDao cfClewRequestStatusChangeLogDao;
    @Autowired
    private ICfClewTaskWriteService cfClewTaskWriteService;
    @Autowired
    private ICfClewTaskReadService cfClewTaskReadService;
    @Autowired
    private ICfClewInfoService cfClewInfoService;
    @Autowired
    private ISeaAccountServiceDelegate seaAccountServiceDelegate;
    @Autowired
    private IClewTrackUserInfoService clewTrackUserInfoService;
    @Autowired
    private ICfPatientAttachService cfPatientAttachServiceImpl;
    static {
        System.setProperty("spring.cloud.consul.host","k8s-discovery-bedin.shuidi.io:80");
    }

    @Test
    public void testCheckErrorCountForAlarm() {
        ClewWorkContentTypeModel clewWorkContentTypeModel = new ClewWorkContentTypeModel();
        clewWorkContentTypeModel.setContentType(31);
        clewWorkContentTypeModel.setContentDesc("测试");
        clewTrackUserInfoService.checkErrorCountForAlarm(49, clewWorkContentTypeModel);

    }
    @Test
    public void testgetorganizetionNames(){
        long clewId = 8536244;
        String clewUserId = "lushuaihang";
        System.out.println(null != cfClewTaskReadService.getClewIdByClewIdWithUserId(Lists.newArrayList(clewId), clewUserId)
                    || null != cfPatientAttachServiceImpl.getInfoByClewIdWithAssigneeMis(Lists.newArrayList(clewId), clewUserId));
        long clewId1 = 40217;
        String clewUserId1 = "lichengjin";
        System.out.println(null != cfClewTaskReadService.getClewIdByClewIdWithUserId(Lists.newArrayList(clewId1), clewUserId1)
                    || null != cfPatientAttachServiceImpl.getInfoByClewIdWithAssigneeMis(Lists.newArrayList(clewId1), clewUserId1));
    }
    @Test
    public void testShutDownTaskByTaskId(){
        //测试随便填写个clostTypeEnum
        cfClewTaskWriteService.shutDownTaskByTaskId(Lists.newArrayList(7645L,7646L),Lists.newArrayList(CfClewTaskEnums.TaskTypeEnum.WH_TRACK_TYPE.getType()),true,EXISTOFFLINECLEW);
    }

    @Test
    public void inertTask(){
        CfClewTaskDO cfClewTaskDO = cfClewTaskReadService.getTaskInfoByTaskId(10171L);
        cfClewTaskDO.setCreateTime(null);
        cfClewTaskDO.setUpdateTime(null);
        cfClewTaskDO.setTaskStatus(6);
    }
    @Test
    public void initPacketInfo(){
        CfClewPacketDO packetDO = new CfClewPacketDO();
        packetDO.setCreateTime(new Date());
        packetDO.setPacketName("外呼客服分组");
        packetDao.insert(packetDO);
        log.info("外呼客服分组 :{}",packetDO);


        packetDO = new CfClewPacketDO();
        packetDO.setCreateTime(new Date());
        packetDO.setPacketName("BD分组");
        packetDao.insert(packetDO);
        log.info("BD分组 :{}",packetDO);
    }

    @Test
    public void testAlarm(){
        for(int i=0;i<10;i++){
            alarmClient.sendByUser(Lists.newArrayList("gaojiandong"),"https://sea.shuiditech.com/bd/clew?clewUserId=gaojiandong");
            System.out.println();
        }
    }

    @Test
    public void saveCfClewRequestStatusChangeLog(){
        CfClewRequestStatusChangeLogDO cfClewRequestStatusChangeLogDO = new CfClewRequestStatusChangeLogDO();
        cfClewRequestStatusChangeLogDO.setClewId(23L);
        cfClewRequestStatusChangeLogDO.setSourceRequestStatus(0);
        cfClewRequestStatusChangeLogDO.setTargetRequestStatus(2);
        cfClewRequestStatusChangeLogDO.setRequestStatusType(1);
        cfClewRequestStatusChangeLogDao.insert(cfClewRequestStatusChangeLogDO);
    }

    @Test
    public void testclewTrackUserInfoService(){
        List<String> online111FuWuUser = clewTrackUserInfoService.getOnline111FuWuUser();
        log.error("---------"+online111FuWuUser);
    }

}
