package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanAsynApiResponse;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.constants.CfClewCommonCons;
import com.shuidihuzhu.cf.clewtrack.delegate.ICrowdfundingServiceDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewBaseInfoDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewQiWorkCallRecordDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfClewTrackUserInfoDO;
import com.shuidihuzhu.cf.clewtrack.enums.CfClewTrackUserInfoEnums;
import com.shuidihuzhu.cf.clewtrack.enums.WorkbenchEnums;
import com.shuidihuzhu.cf.clewtrack.service.donate.AiDonateTaskHandleService;
import com.shuidihuzhu.cf.clewtrack.service.impl.ClewTrackUserInfoServiceImpl;
import com.shuidihuzhu.cf.clewtrack.service.qiwork.ICfClewQiWorkCallRecordsService;
import com.shuidihuzhu.cf.clewtrack.utils.CosUploadUtil;
import com.shuidihuzhu.cf.clewtrack.utils.HttpRequestUtil;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.admin.model.CfAsrRecordModel;
import com.shuidihuzhu.client.cf.qywechat.QywxSdkClient;
import com.shuidihuzhu.client.cf.riskadmin.CfRiskAdminAsrClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.shuidihuzhu.wx.biz.utils.WxCpHttpUtil.getHttpClient;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AiDonateTaskTest {

    static {
        System.setProperty("spring.cloud.consul.host", "k8s-discovery-bedin.shuidi.io:80");
    }

    @Autowired
    private AiDonateTaskHandleService aiDonateTaskHandleService;

    @Autowired
    private ICfClewInfoService clewInfoService;

    @Autowired
    private ICrowdfundingServiceDelegate crowfundingServiceDelegate;

    public final static String COS_CF_AUDIO_STARAGE_URL = "/call-audio/";

    @Autowired
    private ClewCosAudioUploadService cosUploadUtil;

    @Autowired
    private QywxSdkClient qywxSdkClient;

    @Resource
    private HttpRequestUtil httpRequestUtil;

    @Autowired
    private CfRiskAdminAsrClient cfAdminAsrClient;

    @Test
    public void testCall() {
        Date expirationDate = new Date(System.currentTimeMillis() + 24 * 60L * 60L * 1000L);
        String cosSign = CosUploadUtil.getCosSign("/qi-call/cgeclmgu2uaeabx6aaaaamy6ime150.mp3", expirationDate);
        List<CfAsrRecordModel> cfAsrRecordModelList = Lists.newArrayList();
        CfAsrRecordModel cfAsrRecordModel = new CfAsrRecordModel();
        cfAsrRecordModel.setCosFile(cosSign);
        cfAsrRecordModel.setId(299903L);
        cfAsrRecordModelList.add(cfAsrRecordModel);
        cfAdminAsrClient.doHandleAsrRecord(cfAsrRecordModelList);
    }
    @Test
    public void test() {
        String token = qywxSdkClient.getToken("wwf67888370c3563f8", "AH0QqHoM0FmZTikSJce3X6y_5XQyP1vG7-fp-mXFLKI");
        if (StringUtils.isBlank(token)) {
            return;
        }
        String accessToken = "";
        try {
            JSONObject result = JSONObject.parseObject(token);
            accessToken = (String) result.get("access_token");
        } catch (Exception e) {
            log.error("getQyChatToken error", e);
            return;
        }

        String url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + accessToken;

        try {
            // 构建 JSON 请求体
            JSONObject json = new JSONObject();
            json.put("touser", "18147668612"); // 发送给所有成员
            json.put("msgtype", "text");
            json.put("agentid", 1000026); // 替换为你的企业应用 ID
            JSONObject text = new JSONObject();
            text.put("content", "Hello, this is a test message.");
            json.put("text", text);
            qywxSdkClient.sendMsgShuidiChou(accessToken, json.toJSONString());
            String s = httpRequestUtil.postJsonToHttp(url, Map.of("Content-Type", "application/json; charset=utf-8"), json.toJSONString());
            log.info("requestResult:{}", s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCosUpload() {
        try {
            String recordFile = "http://records.icsoc.net/saas_ali/call/bjyz10/20241107/2002869/2/20241107185529-7260243458487508992-9674720-01083424009-6108.mp3";
            List<String> pathList = Splitter.on("/").splitToList(recordFile);
            String path = COS_CF_AUDIO_STARAGE_URL + pathList.get(pathList.size() - 1);
            cosUploadUtil.uploadCosAudio(recordFile, path);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void aiPreTest() {
        CfClewBaseInfoDO clewBaseInfoByClewId = clewInfoService.getClewBaseInfoByClewId(585900L);
        CrowdfundingInfo caseInfoById = crowfundingServiceDelegate.getCaseInfoById(3147899);
        Integer aiCasePredictDonateCount = aiDonateTaskHandleService.getAiCasePredictDonateCount(clewBaseInfoByClewId, caseInfoById);
        log.info("aiCasePredictDonateCount:{}", aiCasePredictDonateCount);
    }

    @Test
    public void canAssignAiDonateTaskTest() {

        try {
            for (int i = 0; i < 3; i++) {
                if (i == 2) {
                    Thread.sleep(10000L);
                }
                aiDonateTaskHandleService.canAssignAiDonateTask("AI一星");
            }
        } catch (Exception e) {

        }
    }
}
