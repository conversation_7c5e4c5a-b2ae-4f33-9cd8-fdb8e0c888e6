package com.shuidihuzhu.cf.clewtrack.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.delegate.IkratosFeignClientDelegate;
import com.shuidihuzhu.cf.clewtrack.domain.Cf1V1UserGradeDO;
import com.shuidihuzhu.cf.dao.clewtrack.Cf1V1UserGradeDao;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageSearchParam;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageSearchResult;
import com.shuidihuzhu.kratos.client.feign.vo.ChatMessageVo;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-03-02
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class) // 指定我们SpringBoot工程的Application启动类
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class
})
@WebAppConfiguration
@Slf4j
public class QyWxChatTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private IkratosFeignClientDelegate kratosFeignClientDelegateImpl;

    @Test
    public void test(){
        ChatMessageSearchParam chatMessageSearchParam = new ChatMessageSearchParam();
        chatMessageSearchParam.setLimit(20);
        chatMessageSearchParam.setSkip(0);
        Date endTime = DateUtil.getCurrentDate();
        Date startTime = DateUtil.addDay(endTime,1);
        chatMessageSearchParam.setStartTime(startTime.getTime());
        chatMessageSearchParam.setEndTime(endTime.getTime());
        chatMessageSearchParam.setCorpId("wwb8cc2f5c0fc58917");
        chatMessageSearchParam.setUserId("wangjinsen");
        chatMessageSearchParam.setExternalUserId("wm683dCwAANfEX1QrTIUvZn0lC1fIQQg");
        ChatMessageSearchResult chatMessageSearchResult = kratosFeignClientDelegateImpl.searchMessageList(chatMessageSearchParam);
        log.info("chatMessageVos:{}", chatMessageSearchResult);
    }

}
