package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.constants.CfClewMQTagCons;
import com.shuidihuzhu.cf.clewtrack.mq.consumer.FoundationClewMsgFromGrowthtoolConsumer;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.growthtool.param.FoundationParam;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * <AUTHOR>
 * @date 2019-11-13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class) // 指定我们SpringBoot工程的Application启动类
@EnableAutoConfiguration(exclude = {
        DataSourceAutoConfiguration.class
})
@WebAppConfiguration
@Slf4j
public class FoundationClewMsgTest {
    static {
        System.setProperty("spring.cloud.consul.host", "consul.zelda.shuiditech.com:80");
    }

    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private FoundationClewMsgFromGrowthtoolConsumer foundationClewMsgFromGrowthtoolConsumer;

    @Test
    public void test(){
        FoundationParam foundationParam = new FoundationParam("xiaoq",
                "其它","370921199598761324","14001400576","河南","郑州","金水区","王小二");
        this.producer.send(new Message(MQTopicCons.CF, CfClewMQTagCons.FOUNDATION_CLEW_MSG_FROM_GROWTHTOOL,
                CfClewMQTagCons.FOUNDATION_CLEW_MSG_FROM_GROWTHTOOL + "-" + System.currentTimeMillis(), foundationParam));
    }

    @Test
    public void testProvinceAndCity(){
        FoundationParam foundationParam = new FoundationParam("xiaoq",
                "其它","370921199598761324","14001400576","河南省","郑州市","金水区","王小二");
        //foundationClewMsgFromGrowthtoolConsumer.getClewBaseInfoDOByFoundationParam(foundationParam);
    }

}
