package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.Application;
import com.shuidihuzhu.cf.clewtrack.domain.clewindex.CfClewTaskIndexDO;
import com.shuidihuzhu.cf.clewtrack.model.CFDengjiDataPanelModel;
import com.shuidihuzhu.cf.clewtrack.param.CfDengjiClewForWhQueryParam;
import com.shuidihuzhu.cf.clewtrack.result.CFClewInfoIndexModelResult;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2019-12-16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class CfEsStatisticsServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private ICfEsStatisticsService cfEsStatisticsServiceImpl;

    @Autowired
    private ICfSearchTaskIndexService cfEsTaskIndexServiceImpl;

    @Test
    public void queryTest(){
        Date queryDate = DateUtil.addDay(DateUtil.getCurrentDate(),-61);
        log.info("queryDate:{}",queryDate);
        List<CFDengjiDataPanelModel> list = cfEsStatisticsServiceImpl.get7DayWaihuClewGroupByWorkContentType(queryDate, 0);
        log.info("list:{}",list);
    }


    @Test
    public void  queryIndex() {
        CfDengjiClewForWhQueryParam queryParam = new CfDengjiClewForWhQueryParam();
        queryParam.setDisease("白血病,肺癌");
        queryParam.setIsManager(true);
        CFClewInfoIndexModelResult<CfClewTaskIndexDO> result = cfEsTaskIndexServiceImpl.queryDengjiCfClewTaskIndex(queryParam);
        log.info("result:{}",result);
    }
}
