package com.shuidihuzhu.cf.clewtrack.service;

import com.shuidihuzhu.cf.clewtrack.domain.CfClewTaskDO;
import com.shuidihuzhu.cf.clewtrack.domain.CfQyWxBindClewDO;
import com.shuidihuzhu.cf.clewtrack.service.runner.AbstractTest;
import com.shuidihuzhu.cf.clewtrack.utils.ShuidiCipherUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 企业微信绑定线索服务测试类
 *
 * <AUTHOR>
 * @since 2024/12/12
 */
public class ICfQyWxBindClewServiceTest extends AbstractTest {

    @Resource
    private ICfQyWxBindClewService cfQyWxBindClewService;

    @Before
    public void init() {
        ReflectionTestUtils.setField(cfQyWxBindClewService, "readSwitch", true);
    }

    @Test
    public void testGetCfQyWxBindClewDOByContact() {
        // 准备测试数据
//        CfQyWxBindClewDO bindClewDO = new CfQyWxBindClewDO();
//        bindClewDO.setQyWechatUserId("test_qy_user");
//        bindClewDO.setQyWechatUserIdEncrypt(ShuidiCipherUtils.encrypt("test_qy_user"));
//        bindClewDO.setExternalUserid("test_external_user");
//        bindClewDO.setTaskId(9999999L);
//        bindClewDO.setCreateTime(new Date());
//        bindClewDO.setUpdateTime(new Date());
//        cfQyWxBindClewService.insert(bindClewDO);

        // 执行测试
        CfQyWxBindClewDO result = cfQyWxBindClewService.getCfQyWxBindClewDOByContact("test_qy_user", "test_external_user");

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals("test_qy_user", result.getQyWechatUserId());
        Assert.assertEquals("test_external_user", result.getExternalUserid());
    }

    @Test
    public void testUpdateTaskIdByCflewTaskDO() {


        // 执行测试
        int result = cfQyWxBindClewService.updateTaskIdByCflewTaskDO(9999999L, 99999991L);

        // 验证结果
        Assert.assertEquals(1, result);

        // 执行测试
        result = cfQyWxBindClewService.shutdownByTaskId(99999991L);

        // 验证结果
        Assert.assertEquals(1, result);
    }

    @Test
    public void testExistByTaskIdFromMaster() {

        // 执行测试
        boolean result = cfQyWxBindClewService.existByTaskIdFromMaster(9999999L);

        // 验证结果
        Assert.assertTrue(result);
    }

    @Test
    public void testGetCfQyWxBindClewDOByTaskId() {

        // 执行测试
        CfQyWxBindClewDO result = cfQyWxBindClewService.getCfQyWxBindClewDOByTaskId(9999999L);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(Long.valueOf(9999999L), result.getTaskId());
    }

    @Test
    public void testGetCfQyWxBindClewDOByTaskIdSet() {
        // 准备测试数据
        for (long i = 1; i <= 3; i++) {
            CfQyWxBindClewDO bindClewDO = new CfQyWxBindClewDO();
            bindClewDO.setQyWechatUserId("test_qy_user_" + i);
            bindClewDO.setExternalUserid("test_external_user_" + i);
            bindClewDO.setTaskId(9999999L + i);
            bindClewDO.setCreateTime(new Date());
            bindClewDO.setUpdateTime(new Date());
            cfQyWxBindClewService.insert(bindClewDO);
        }

        Set<Long> taskIdSet = new HashSet<>();
        taskIdSet.add(9999999L + 1L);
        taskIdSet.add(9999999L + 2L);
        taskIdSet.add(9999999L + 3L);

        // 执行测试
        Map<Long, CfQyWxBindClewDO> result = cfQyWxBindClewService.getCfQyWxBindClewDOByTaskIdSet(taskIdSet);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(3, result.size());
        for (Long taskId : taskIdSet) {
            CfQyWxBindClewDO bindClewDO = result.get(taskId);
            Assert.assertNotNull(bindClewDO);
            Assert.assertEquals(taskId, bindClewDO.getTaskId());
        }
    }
}
